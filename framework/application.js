"use strict";

const path = require('path');
const Koa = require('koa');
const utils = require('./utils');

global.helper = require('./helper');
global.Model = require('./model');
global.Logic = require('./logic');
global.Controller = require('./controller');

module.exports = class Application extends Koa {

  constructor(options) {
    const debug = !!options.debug;
    const root = options.root || path.dirname(process.mainModule.filename);
    const env = utils.loadEnv(root, debug);

    super({proxy: true, env: env, keys: env.keys});

    // 多模块项目
    this.isMultiples = Array.isArray(env.modules);

    // 关键目录
    this.rootPath = root;
    this.appPath = path.join(root, '/app');
    this.extendPath = path.join(root, '/extend');
    this.middlewarePath = path.join(root, '/middleware');

    // 文件索引
    this.extends = utils.loadExtend(this.rootPath);
    this.models = {};
    this.logics = {};
    this.modules = {};
    this.services = {};
    this.validators = {};
    this.controllers = {};
    this.routerMiddleware = {};

    // 扫描索引
    (this.isMultiples ? env.modules : ['']).forEach(name => this.scanModuleFile(name));

    // 加载插件
    env.plugin.forEach(name => require(this.middlewarePath + '/' + name)(this));

    // 加载中间件
    env.middleware.forEach(name => {
      const fn = require(this.middlewarePath + '/' + name)(this);
      fn && this.use(fn);
    });
  }

  get debug() {
    return this.env.debug;
  }

  scanModuleFile(name) {
    const root = name ? path.join(this.appPath, '/' + name) : this.appPath;
    const env = utils.loadEnv(root, this.debug);

    utils.loadController(root, name, this.controllers);
    utils.loadModel(root, name, this.models);
    utils.loadLogic(root, name, this.logics);
    utils.loadService(root, name, this.services);
    utils.loadValidator(root, name, this.validators);

    this.modules[name] = Object.assign({}, this.env, {middleware: [], root: root}, env);
  }

  run(callback) {
    const {port} = this.env;
    this.listen(port, '0.0.0.0', function () {
      callback((this.debug ? '调试模式' : '线上服务') + '已启动，端口号：' + port);
    });
  }
}