const fs = require('fs');

module.exports = {
  loadFile(name) {
    return fs.existsSync(name) ? require(name) : null;
  },
  loadEnv(root, debug) {
    root = root + '/config/';
    let envCnf = this.loadFile(root + 'env.js');
    let devCnf = this.loadFile(root + 'env.' + (debug ? 'dev' : 'pro') + '.js');
    return Object.assign({debug: debug}, envCnf, devCnf);
  },
  loadNamespace(root, prefix = '', list = {}) {
    let files;

    try {
      files = fs.readdirSync(root, {withFileTypes: true});
    } catch (e) {
      return;
    }

    for (let i = 0, len = files.length, name, file; i < len; i++) {
      name = files[i].name;
      file = root + '/' + name;

      if (files[i].isDirectory()) {
        this.loadNamespace(file, prefix + (prefix ? '.' : '') + name, list);
      } else if (name == 'index.js') {
        list[prefix] = file;
      } else if (name.endsWith('.js')) {
        name = prefix + (prefix ? '.' : '') + name.substr(0, name.length - 3);
        list[name] = file;
      }
    }

    return list;
  },
  loadController(root, prefix, appendTo) {
    return this.loadNamespace(root + '/controller', prefix, appendTo);
  },
  loadLogic(root, prefix, appendTo) {
    return this.loadNamespace(root + '/logic', prefix, appendTo);
  },
  loadService(root, prefix, appendTo) {
    return this.loadNamespace(root + '/service', prefix, appendTo);
  },
  loadModel(root, prefix, appendTo) {
    return this.loadNamespace(root + '/model', prefix, appendTo);
  },
  loadValidator(root, prefix, appendTo) {
    return this.loadNamespace(root + '/validator', prefix, appendTo);
  },
  loadRouter(root) {
    return this.loadFile(root + '/router.js');
  },
  loadExtend(root, appendTo) {
    return this.loadNamespace(root + '/extend', appendTo);
  }
}