"use strict";

const proto = exports;

/**************
 * ID加密/解密
 * type：11-99
 * pad：填充0，保证加密后长度
 ****************/
(function () {
  let options = {
    user: {
      pad: 6,
      key: '7W5AXD9NJFL61U3HCPQB2ZKORITS0E4V8MYG',
      type: {user: '11'}
    },
    media: {
      pad: 6,
      key: 'bpdfDqrnh4KzaNmCYA2HLiMsXw6y5JGkT98QUvjlgSotuWVB7F3ZPcRIOE01ex',
      type: {news: '12', video: '13', audio: '14', motto: '15'}
    }
  };

  function encode(id, key) {
    let num = new Number(id), str = '', mod, len = key.length;
    while (num) {
      mod = num % len;
      num = (num - mod) / len;
      str += key[mod];
    }
    return str;
  }

  function decode(str, key) {
    let id = 0, max = str.length, i = 0, len = key.length, o;
    for (; i < max; i++) {
      o = key.indexOf(str[i]);
      if (o === -1) return;
      id += o * Math.pow(len, i);
    }
    return id;
  }

  function encodeId(id, type) {
    let arr = type.split('.')
      , opt = options[arr[0]]
      , pre = opt.type[arr[1] || arr[0]]
      , zs = helper.pad(parseInt(id / pre), opt.pad)
      , ys = helper.pad(id % pre, 2)
      , str = pre[0] + ys[1] + pre[1] + zs + ys[0];
    return encode(str, opt.key);
  }

  function decodeId(str, group) {
    let opt = options[group]
      , res = decode(str, opt.key);
    if (!res) return;

    res = res.toString();

    let pre = res[0] + res[2]
      , yu = res.substr(-1) + res[1]
      , zs = res.substr(3, res.length - 3 - 1)
      , i;

    if (zs.length < opt.pad) return;

    for (i in opt.type) {
      if (opt.type[i] === pre) {
        return {id: zs * pre + parseInt(yu), type: i};
      }
    }
  }

  proto.encodeId = encodeId;
  proto.decodeId = decodeId;
}());

proto.idcardToBirthday = function (idcard) {
  if (idcard.length === 18) {
    return idcard.substring(6, 10) + '-' + idcard.substring(10, 12) + '-' + idcard.substring(12, 14);
  }

  if (idcard.length === 15) {
    return '19' + idcard.substring(6, 8) + '-' + idcard.substring(8, 10) + '-' + idcard.substring(10, 12);
  }

  return '0000-00-00';
}

proto.moneyToUpperCase = function (n) {
  let fraction = ['角', '分'];
  let digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
  let unit = [['元', '万', '亿'], ['', '拾', '佰', '仟']];
  let head = n < 0 ? '欠' : '';

  n = Math.abs(n);

  let s = '';
  for (let i = 0; i < fraction.length; i++) {
    s += (digit[Math.floor(n * 10 * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, '');
  }
  s = s || '整';
  n = Math.floor(n);
  for (let i = 0; i < unit[0].length && n > 0; i++) {
    let p = '';
    for (let j = 0; j < unit[1].length && n > 0; j++) {
      p = digit[n % 10] + unit[1][j] + p;
      n = Math.floor(n / 10);
    }
    s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s;
  }
  return head + s.replace(/(零.)*零元/, '元').replace(/(零.)+/g, '零').replace(/^整$/, '零元整');
}

proto.durationToTime = function (duration) {
  duration = parseInt(duration);

  let r = [],
    hour = parseInt(duration / 3600),
    other = duration % 3600,
    min = parseInt(other / 60),
    sec = parseInt(other % 60);

  if (hour > 0) {
    r.push((hour < 10 ? '0' + hour : hour) + '时');
  }

  r.push((min < 10 ? '0' + min : min) + '分');
  r.push((sec < 10 ? '0' + sec : sec) + '秒');

  return r.join('');
}

proto.base64Encode = function (str) {
  return Buffer.from(str.toString(), 'utf8').toString('base64');
};

proto.base64Decode = function (str) {
  return Buffer.from(str, 'base64').toString();
};

proto.zhDate = function (time, format) {
  if (!time || time === '0000-00-00') return '';
  return time.substr(0, 4) + '年' + time.substr(5, 2) + '月' + time.substr(8, 2) + '日'
};

proto.zhSex = function (sex, def = '其他') {
  return sex === 1 ? '男' : sex === 2 ? '女' : def;
};

proto.Date = function fDate (time) {
  let date;

  if (/^\d{9,10}$/.test(time)) {
    date = new Date(time * 1000);
  } else if (/^\d{13}$/.test(time)) {
    date = new Date(time);
  } else if (time instanceof Date) {
    date = time;
  } else {
    date = new Date();
  }

  return {
    get timestamp() {
      return parseInt(date.valueOf() / 1000);
    },
    get yyyy() {
      return this.y;
    },
    get yy() {
      return this.y.toString().substr(2);
    },
    get mm() {
      return this.m < 10 ? '0' + this.m : this.m;
    },
    get dd() {
      return this.d < 10 ? '0' + this.d : this.d;
    },
    get hh() {
      return this.h < 10 ? '0' + this.h : this.h;
    },
    get ii() {
      return this.i < 10 ? '0' + this.i : this.i;
    },
    get ss() {
      return this.s < 10 ? '0' + this.s : this.s;
    },
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    sss: date.getMilliseconds(),
    prev(day) {
      return fDate(this.timestamp - (day || 1) * 86400);
    },
    next(day) {
      return fDate(this.timestamp + (day || 1) * 86400);
    },
    toString: function (format) {
      return (format || 'YYYY-MM-DD HH:mm:ss').replace('sss', this.sss).replace('yy', this.yy).replace('YYYY', this.yyyy).replace('MM', this.mm).replace('DD', this.dd).replace('HH', this.hh).replace('mm', this.ii).replace('ss', this.ss);
    },
    toNumber: function () {
      return parseInt(date.valueOf() / 1000);
    },
    format: function (format) {
      let jt = fDate(), diff = jt.timestamp - this.timestamp;

      if (diff < 0) return this.toString(format);
      if (diff < 60) return '刚刚';
      if (diff < 3600) return Math.floor(diff / 60) + '分钟前';
      if (diff <= 86400) return Math.floor(diff / 3600) + '小时前';

      let zt = jt.prev();
      if (diff < 172800 && zt.d === this.d) {
        if (zt.d === this.d) return '昨天' + this.hh + ':' + this.ii;
      }

      if (diff < 259200) {
        let qt = zt.prev();
        if (qt.d === this.d) return '前天' + this.hh + ':' + this.ii;
      }

      format = format || 'YYYY-MM-DD HH:mm';

      if (jt.y === this.y) return this.toString(format.replace(/^YYYY(-|年)/, ''));

      return this.toString(format);
    }
  }
};

/**
 * 保留N位小数
 */
proto.decimal = function (num, keep = 2) {
  let val = num.toString();
  let len = val.indexOf('.');

  if (len !== -1) {
    val = val.substr(0, len) + '.' + val.substr(len + 1, keep);
  }

  return parseFloat(val);
}
