const axios = require('axios');
const Qs = require('querystring');

const request = axios.create({
  timeout: 30000,
  withCredentials: false,
  crossDomain: false,
  responseType: 'json',
  paramsSerializer: function (params) {
    return Qs.stringify(params)
  }
});

const curl = function (config) {
  return request(config).then(res => {
    let {config, data} = res;

    if (config.method == 'head') {
      return res.headers;
    }

    switch (config.responseType) {
      case 'json':
        if (typeof data == 'string') {
          throw new Error('CURL响应数据格式错误');
        }

        return data;
      case 'xml':
        if (typeof data != 'string') {
          throw new Error('CURL响应数据格式错误');
        }

        return helper.xml_decode(data);
      default:
        return data;
    }
  });
};

curl.get = function (url, data, type) {
  return curl({method: 'get', url: url, params: data, responseType: type});
};

curl.post = function (url, data, type) {
  return curl({method: 'post', url: url, data: data, responseType: type});
};

curl.head = function (url, data, headers) {
  return curl({method: 'head', url: url, params: data, headers: headers});
}

exports.curl = curl;