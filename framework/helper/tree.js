const proto = exports;

proto.findP = function (id, list = [], result = []) {
    // 查找父节点
    for (let i = 0; i < list.length; i += 1) {
        console.log('*******', list[i].id, '********')
        const item = list[i]
        // 找到目标
        if (item.id === id) {
            console.log('找到了')
            // 加入到结果中
            result.push(item.id)
            // 因为可能在第一层就找到了结果，直接返回当前结果
            if (result.length === 1) return result
            return true
        }
        // 如果存在下级节点，则继续遍历
        if (item.children) {
            // 预设本次是需要的节点并加入到最终结果result中
            result.push(item.id)
            const find = this.findP(id, item.children, result)
            // 如果不是false则表示找到了，直接return，结束递归
            if (find) {
                return result
            }
            // 到这里，意味着本次并不是需要的节点，则在result中移除
            result.pop()
        }
    }
    // 如果都走到这儿了，也就是本轮遍历children没找到，将此次标记为false
    return false
}

proto.recursionDataTree = function (dataList, pid) {
    let resultList = [];
    if (!dataList) return null;
    for (const map of dataList) {
        let id = map["id"];
        let parentId = map["pid"];
        if (pid === parentId) {
            const data = map.dataValues;
            let childrenList = this.recursionDataTree(dataList, id);
            if (childrenList)
                data["children"] = childrenList;
            resultList.push(data);
        }
    }
    return resultList;
}
