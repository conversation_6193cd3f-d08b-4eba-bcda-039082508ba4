"use strict";

const proto = exports;

proto.Date = function Date2(time) {
  let date;
  if (/^\d{9,10}$/.test(time)) {
    date = new Date(time * 1000);
  } else if (!time) {
    date = new Date();
  } else if (/^\d{13}$/.test(time)) {
    date = new Date(time);
  } else if (time instanceof Date) {
    date = time;
  } else {
    date = new Date();
  }

  return {
    get timestamp() {
      return parseInt(date.valueOf() / 1000);
    },
    get yyyy() {
      return this.y;
    },
    get mm() {
      return this.m < 10 ? '0' + this.m : this.m;
    },
    get dd() {
      return this.d < 10 ? '0' + this.d : this.d;
    },
    get hh() {
      return this.h < 10 ? '0' + this.h : this.h;
    },
    get ii() {
      return this.i < 10 ? '0' + this.i : this.i;
    },
    get ss() {
      return this.s < 10 ? '0' + this.s : this.s;
    },
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    sss: date.getMilliseconds(),
    toString: function (format) {
      return (format || 'YYYY-MM-DD HH:mm:ss').replace('sss', this.sss).replace('YYYY', this.yyyy).replace('MM', this.mm).replace('DD', this.dd).replace('HH', this.hh).replace('mm', this.ii).replace('ss', this.ss);
    },
    toNumber: function () {
      return parseInt(date.valueOf() / 1000);
    },
    format: function (format) {
      var jintian = Date2(), diff = jintian.timestamp - this.timestamp;

      if (diff > 0) {
        if (diff < 60) {
          return '刚刚';
        }

        if (diff < 3600) {
          return Math.floor(diff / 60) + '分钟前';
        }

        if (diff < 10800) {
          return Math.floor(diff / 3600) + '小时前';
        }

        if (diff < 86400 && jintian.d == this.d) {
          return '今天' + this.hh + ':' + this.ii;
        }

        if (diff < 172800) {
          return '昨天' + this.hh + ':' + this.ii;
        }

        if (diff < 259200) {
          return '前天' + this.hh + ':' + this.ii;
        }

      } else if (diff < 0) {
        diff = -diff;

        if (diff < 60) {
          return diff + '秒后';
        }

        if (diff < 3600) {
          return Math.floor(diff / 60) + '分钟后';
        }

        if (diff < 10800) {
          return Math.floor(diff / 3600) + '小时后';
        }

        if (diff < 86400 && jintian.d == this.d) {
          return '今天' + this.hh + ':' + this.ii;
        }

        if (diff < 172800) {
          return '明天' + this.hh + ':' + this.ii;
        }

        if (diff < 259200) {
          return '后面' + this.hh + ':' + this.ii;
        }
      }

      format = format || 'YYYY-MM-DD HH:mm';

      if (jintian.y == this.y) {
        return this.toString(format.replace(/^YYYY(-|年)/, ''));
      }

      return this.toString(format);
    }
  };
}
