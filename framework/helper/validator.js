const proto = exports;
const validator = require('validator');
const fs = require('fs');
const {URL} = require('url');

function isRegExp(obj) {
  return Object.prototype.toString.call(obj) === '[object RegExp]';
}

proto.is_numeric = function (obj) {
  return /^[+-]?([0-9]*[.])?[0-9]+$/.test(obj);
};

proto.is_price = function (obj, symbol = true) {
  if (obj == '' || isNaN(obj)) return false;

  switch (symbol) {
    case 0: // >=0
      return /^([0-9]*)?\.[0-9]{2}$/.test(obj);
    case true: // > 0
      return /^([0-9]*)?\.[0-9]{2}$/.test(obj) && obj > 0;
    case false: // < 0
      return /^-([0-9]*)?\.[0-9]{2}$/.test(obj) && obj < 0;
    default:
      return /^[+-]?([0-9]*)?\.[0-9]{2}$/.test(obj);
  }
}

proto.is_array = function (obj) {
  return Array.isArray(obj);
};

proto.is_object = function (obj) {
  return obj instanceof Object;
};

proto.is_empty = function (obj) {
  if (!obj || obj === '0') return true;

  if (this.is_array(obj)) return obj.length === 0;

  if (this.is_object(obj)) {
    if (obj.length === 0) return true;

    return Object.keys(obj).length == 0;
  }

  return obj.toString().replace(/\s+/g, "").replace(/<\/?.+?>/g, "").replace(/[\r\n]/g, "").length == 0;
};

proto.is_string = function (obj) {
  if (obj instanceof String) return true;

  return !this.is_object(obj) && !this.is_array(obj);
};

proto.is_null = function (obj) {
  return obj === null || obj === undefined;
}

proto.isset = function (obj, key) {
  if (!this.is_object(obj)) {
    return false;
  }

  return Object.keys(obj).indexOf(key) != -1;
}

proto.is_file = function (obj) {
  if (!this.is_string(obj)) {
    return false;
  }

  return fs.stats.isFile(obj);
}

proto.is_dir = function (obj) {
  if (!this.is_string(obj)) {
    return false;
  }

  return fs.stats.isDirectory(obj);
}

proto.is_bool = function (obj) {
  return obj === true || obj === false;
}

proto.is_int = function (obj) {
  return /^[+-]?[0-9]+$/.test(obj);
}

proto.is_integer = function (obj, match = undefined) {
  switch (match) {
    case 0: // 正整数或零
      return /^\d+$/.test(obj);
    case true: // 正整数
      return /^[1-9]\d{0,19}$/.test(obj);
    case false: // 负整数
      return /^-[1-9]\d{0,19}$/.test(obj);
    default:
      return /^[+-]?[0-9]\d{0,19}$/.test(obj);
  }
}

proto.is_id = function (obj) {
  try {
    return /^[1-9]\d{0,19}$/.test(obj) && obj <= 4294967295;
  } catch (e) {
    return false;
  }
}

proto.is_ids = function (obj) {
  return /^[1-9]\d{0,18}(,[1-9]\d{0,18}){0,20}$/.test(obj);
}

proto.isNaN = function (str) {
  return isNaN(str) || str === '' || str === null;
}

proto.isInt = function (obj) {
  return /^[+-]?[0-9]+$/.test(obj);
}

proto.isIntGreaterThan = function (v1, v2) {
  return proto.isInt(v1) && v1 > v2;
}

proto.isIntLessThan = function (v1, v2) {
  return proto.isInt(v1) && v1 < v2;
}

proto.isNumGreaterThan = function (v1, v2) {

}

proto.isNumLessThan = function (v1, v2) {

}

proto.is_email = function (str, opt) {
  return validator.isEmail(str, opt);
}

proto.is_code = function (obj, length) {
  return /^[a-zA-Z]+[0-9a-zA-Z]{4}$/.test(obj);
}

proto.is_url = function (url) {
  try {
    return new URL(url);
  } catch (e) {
    return false;
  }
}

proto.is_date = function (obj) {
  return /^\d{4}-\d{2}-\d{2}$/.test(obj)
}
