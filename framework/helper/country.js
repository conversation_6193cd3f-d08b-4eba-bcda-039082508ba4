"use strict";

const countries = {
  'zh-CN': {name: '中国', prefix: '+86', mobile: /^(\+?0?86\-?)?1[3456789]\d{9}$/},
  'zh-TW': {name: '中国台湾', prefix: '+886', mobile: /^(\+?886\-?|0)?9\d{8}$/},
  'ar-AE': {name: '阿拉伯联合酋长国', prefix: '+971', mobile: /^((\+?971)|0)?5[024568]\d{7}$/},
  'ar-DZ': {name: '阿尔及利亚', prefix: '+213', mobile: /^(\+?213|0)(5|6|7)\d{8}$/},
  'ar-EG': {name: '埃及', prefix: '+20', mobile: /^((\+?20)|0)?1[012]\d{8}$/},
  'ar-JO': {name: '约旦', prefix: '+962', mobile: /^(\+?962|0)?7[789]\d{7}$/},
  'ar-SA': {name: '沙特阿拉伯', prefix: '+966', mobile: /^(!?(\+?966)|0)?5\d{8}$/},
  'ar-SY': {name: '叙利亚', prefix: '+963', mobile: /^(!?(\+?963)|0)?9\d{8}$/},
  'be-BY': {name: '白俄罗斯', prefix: '+375', mobile: /^(\+?375)?(24|25|29|33|44)\d{7}$/},
  'bg-BG': {name: '保加利亚', prefix: '+359', mobile: /^(\+?359|0)?8[789]\d{7}$/},
  'cs-CZ': {name: '捷克', prefix: '+420', mobile: /^(\+?420)? ?[1-9][0-9]{2} ?[0-9]{3} ?[0-9]{3}$/},
  'da-DK': {name: '丹麦', prefix: '+45', mobile: /^(\+?45)?\s?\d{2}\s?\d{2}\s?\d{2}\s?\d{2}$/},
  'de-DE': {name: '德国', prefix: '+49', mobile: /^(\+?49[ \.\-])?([\(]{1}[0-9]{1,6}[\)])?([0-9 \.\-\/]{3,20})((x|ext|extension)[ ]?[0-9]{1,4})?$/},
  'el-GR': {name: '希腊', prefix: '+30', mobile: /^(\+?30|0)?(69\d{8})$/},
  'en-AU': {name: '澳大利亚', prefix: '+61', mobile: /^(\+?61|0)4\d{8}$/},
  'en-GB': {name: '英国', prefix: '+44', mobile: /^(\+?44|0)7\d{9}$/},
  'en-HK': {name: '香港', prefix: '+852', mobile: /^(\+?852\-?)?[456789]\d{3}\-?\d{4}$/},
  'en-IN': {name: '印度', prefix: '+91', mobile: /^(\+?91|0)?[6789]\d{9}$/},
  'en-KE': {name: '肯尼亚', prefix: '+254', mobile: /^(\+?254|0)?[7]\d{8}$/},
  'en-NG': {name: '尼日利亚', prefix: '+234', mobile: /^(\+?234|0)?[789]\d{9}$/},
  'en-NZ': {name: '新西兰', prefix: '+64', mobile: /^(\+?64|0)2\d{7,9}$/},
  'en-PK': {name: '巴基斯坦', prefix: '+92', mobile: /^((\+92)|(0092))-{0,1}\d{3}-{0,1}\d{7}$|^\d{11}$|^\d{4}-\d{7}$/},
  'en-RW': {name: '埃塞俄比亚', prefix: '+250', mobile: /^(\+?250|0)?[7]\d{8}$/},
  'en-SG': {name: '新加坡', prefix: '+65', mobile: /^(\+65)?[89]\d{7}$/},
  'en-TZ': {name: '坦桑尼亚', prefix: '+255', mobile: /^(\+?255|0)?[67]\d{8}$/},
  'en-UG': {name: '乌干达', prefix: '+256', mobile: /^(\+?256|0)?[7]\d{8}$/},
  'en-US': {name: '美国', prefix: '+1', mobile: /^(\+?1)?[2-9]\d{2}[2-9](?!11)\d{6}$/},
  'en-ZA': {name: '南非', prefix: '+27', mobile: /^(\+?27|0)\d{9}$/},
  'en-ZM': {name: '赞比亚', prefix: '+26', mobile: /^(\+?26)?09[567]\d{7}$/},
  'es-ES': {name: '西班牙', prefix: '+34', mobile: /^(\+?34)?(6\d{1}|7[1234])\d{7}$/},
  'et-EE': {name: '爱沙尼亚', prefix: '+372', mobile: /^(\+?372)?\s?(5|8[1-4])\s?([0-9]\s?){6,7}$/},
  'fa-IR': {name: '伊朗', prefix: '+98', mobile: /^(\+?98[\-\s]?|0)9[0-39]\d[\-\s]?\d{3}[\-\s]?\d{4}$/},
  'fi-FI': {name: '芬兰', prefix: '+358', mobile: /^(\+?358|0)\s?(4(0|1|2|4|5|6)?|50)\s?(\d\s?){4,8}\d$/},
  'fo-FO': {name: '法罗群岛', prefix: '+298', mobile: /^(\+?298)?\s?\d{2}\s?\d{2}\s?\d{2}$/},
  'fr-FR': {name: '法国', prefix: '+33', mobile: /^(\+?33|0)[67]\d{8}$/},
  'he-IL': {name: '以色列', prefix: '+972', mobile: /^(\+972|0)([23489]|5[012345689]|77)[1-9]\d{6}/},
  'hu-HU': {name: '匈牙利', prefix: '+36', mobile: /^(\+?36)(20|30|70)\d{7}$/},
  'id-ID': {name: '印度尼西亚', prefix: '+62', mobile: /^(\+?62|0[1-9])[\s|\d]+$/},
  'it-IT': {name: '意大利', prefix: '+39', mobile: /^(\+?39)?\s?3\d{2} ?\d{6,7}$/},
  'ja-JP': {name: '日本', prefix: '+81', mobile: /^(\+?81|0)[789]0[ \-]?[1-9]\d{2}[ \-]?\d{5}$/},
  'kk-KZ': {name: '俄罗斯', prefix: '+7', mobile: /^(\+?7|8)?7\d{9}$/},
  'kl-GL': {name: '格陵兰岛', prefix: '+299', mobile: /^(\+?299)?\s?\d{2}\s?\d{2}\s?\d{2}$/},
  'ko-KR': {name: '韩国', prefix: '+82', mobile: /^((\+?82)[ \-]?)?0?1([0|1|6|7|8|9]{1})[ \-]?\d{3,4}[ \-]?\d{4}$/},
  'lt-LT': {name: '立陶宛', prefix: '+370', mobile: /^(\+370|8)\d{8}$/},
  'ms-MY': {name: '蒙特塞拉特岛', prefix: '+601', mobile: /^(\+?6?01){1}(([145]{1}(\-|\s)?\d{7,8})|([236789]{1}(\s|\-)?\d{7}))$/},
  'nb-NO': {name: '挪威', prefix: '+47', mobile: /^(\+?47)?[49]\d{7}$/},
  'nl-BE': {name: '比利时', prefix: '+32', mobile: /^(\+?32|0)4?\d{8}$/},
  'nn-NO': {name: '挪威', prefix: '+47', mobile: /^(\+?47)?[49]\d{7}$/},
  'pl-PL': {name: '波兰', prefix: '+48', mobile: /^(\+?48)? ?[5-8]\d ?\d{3} ?\d{2} ?\d{2}$/},
  'pt-BR': {name: '巴西', prefix: '+55', mobile: /^(\+?55|0)\-?[1-9]{2}\-?[2-9]{1}\d{3,4}\-?\d{4}$/},
  'pt-PT': {name: '葡萄牙', prefix: '+351', mobile: /^(\+?351)?9[1236]\d{7}$/},
  'ro-RO': {name: '罗马尼亚', prefix: '+40', mobile: /^(\+?4?0)\s?7\d{2}(\/|\s|\.|\-)?\d{3}(\s|\.|\-)?\d{3}$/},
  'ru-RU': {name: '俄罗斯', prefix: '+7', mobile: /^(\+?7|8)?9\d{9}$/},
  'sk-SK': {name: '斯洛伐克', prefix: '+421', mobile: /^(\+?421)? ?[1-9][0-9]{2} ?[0-9]{3} ?[0-9]{3}$/},
  'sr-RS': {name: '苏里南', prefix: '+3816', mobile: /^(\+3816|06)[- \d]{5,9}$/},
  'th-TH': {name: '泰国', prefix: '+66', mobile: /^(\+66|66|0)\d{9}$/},
  'tr-TR': {name: '土耳其', prefix: '+90', mobile: /^(\+?90|0)?5\d{9}$/},
  'uk-UA': {name: '乌克兰', prefix: '+380', mobile: /^(\+?38|8)?0\d{9}$/},
  'vi-VN': {name: '越南', prefix: '+84', mobile: /^(\+?84|0)?((1(2([0-9])|6([2-9])|88|99))|(9((?!5)[0-9])))([0-9]{7})$/}
};

function mobileInfo(country, value, rule) {
  const number = value[0] == '+' ? value.substr(rule.prefix.length) : value;

  return {
    value: rule.prefix + number,
    prefix: rule.prefix,
    number: number,
    country_code: country,
    country_name: rule.name
  };
}

function matchMobileByCountryCode(value, country) {
  const rule = countries[country];
  return rule && rule.mobile.test(value) && mobileInfo(country, value, rule);
}

function matchMobileByAreaCode(value, prefix) {
  let rule, country;
  for (country in countries) {
    rule = countries[country];

    if (rule.prefix == prefix) {
      if (rule.mobile.test(value)) {
        return mobileInfo(country, value, rule);
      }
    }
  }
}

function matchMobileByValue(value) {
  let rule, country;
  for (country in countries) {
    rule = countries[country];

    if (rule.mobile.test(value)) {
      return mobileInfo(country, value, rule);
    }
  }
}

function is_mobile(value, area) {
  if (!value) return false;

  value = value.toString().replace(/\s/g, '');

  if (!area) {
    if (value[0] == '+') {
      return matchMobileByValue(value);
    } else {
      return matchMobileByCountryCode(value, 'zh-CN');
    }
  } else if (area[0] == '+') {
    return matchMobileByAreaCode(value, area);
  } else {
    return matchMobileByCountryCode(value, area);
  }
}

exports.countries = countries;
exports.is_mobile = is_mobile;