"use strict";

const proto = exports;
const crypto = require('crypto');
const fs = require('fs');
const moment = require('moment');
const xml2js = require('xml2js');
const SqlString = require('sqlstring');

proto.fs = fs;

proto.time = function (date = null, format = 'X') {
    let time;

    if (date) {
        let match = /^(\+|\-)(\d+) (year|month|day|hour|minute|second)$/.exec(date);
        if (match && match.length === 4) {
            time = moment().add(parseFloat(match[1] + match[2]), match[3]);
        } else {
            time = moment(date);
        }
    } else {
        time = moment();
    }

    if (format) {
        time = time.format(format);

        if (/^\d+$/.test(time)) {
            time = parseInt(time);
        }
    }

    return time;
}

proto.strtotime = function ($time, $now) {
    let time;

    let matchs = /^(\+|\-)(\d+) (year|month|day|hour|minute|second)$/.exec($time);
    if (matchs && matchs.length === 4) {
        time = moment(/^\d{10}$/.test($now) ? $now * 1000 : $now).add(parseInt(matchs[1] + matchs[2]), matchs[3]);
    } else {
        time = moment(/^\d{10}$/.test($time) ? $time * 1000 : $time, $now);
    }

    return parseInt(time.format('X'));
}

proto.date = function (format, timestamp) {
    timestamp = this.strtotime(timestamp);

    return moment(timestamp * 1000).format(format);
}

proto.gmt_iso8601 = function (timestamp) {
    let expireTime = moment(timestamp && timestamp * 1000).toISOString();
    let pos = expireTime.indexOf('.');
    let expiration = expireTime.substring(0, pos);
    return expiration + 'Z';
}

proto.config = function (path) {
    let config = {};

    for (let i = 1; i < arguments.length; i++) {
        let filename = path + '/config/' + arguments[i] + '.js';
        if (fs.existsSync(filename)) {
            let config2 = require(filename);
            Object.assign(config, config2);
        }
    }

    return config;
}

proto.random = function (min, max) {
    let range = max - min;
    let rand = Math.random();
    return min + Math.round(rand * range);
}

proto.clone = function (obj) {
    if (!obj) {
        return obj;
    }
    else if (!(obj instanceof Array)) {
        return obj;
    }
    // else if (!(obj instanceof Object)) {
    //     return obj;
    // }

    return JSON.parse(JSON.stringify(obj));
}

proto.json_encode = function (obj) {
    if (obj instanceof Array || obj instanceof Object) {
        return JSON.stringify(obj);
    }

    return obj;
}

proto.json_decode = function (json) {
    try {
        return JSON.parse(json);
    } catch (e) {
        return json;
    }
}

proto.xml_encode = function (obj, opt) {
    // opt = {headless: true, cdata: true, rootName: 'xml'}
    opt = Object.assign({renderOpts: {'pretty': false, 'indent': ' ', 'newline': '\n'}}, opt || {})

    var builder = new xml2js.Builder(opt);
    return builder.buildObject(obj);
}

proto.xml_decode = function (xml) {
    return new Promise(function (resolve) {

        var parser = new xml2js.Parser({limit: '2MB', explicitArray: false});
        parser.parseString(xml, function (err, result) {
            resolve(err ? null : result);
        });
    });
}

proto.sku_json_str = function (sku_json) {
    const skuJson = this.json_decode(sku_json);
    const spec = [];
    if (this.is_array(skuJson)) {
        skuJson.forEach(function (json) {
            spec.push(json.v);
        });
    }

    return spec.join(' ');
}

proto.sku_ticket_str = function (sku_json) {
    const skuJson = this.json_decode(sku_json);
    const spec = [];
    if (this.is_array(skuJson)) {
        skuJson.forEach(function (json) {
            spec.push(json.v);
        });
    }

    return '【' + spec.shift() + '】' + spec.join(' ');
}

proto.randString = function (len, words) {
    let chars, result = '';

    switch (words) {
        case 0:
            chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
            break;
        case 1:
            chars = '0123456789';
            break;
        case 2:
            chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
            break;
        case 3:
            chars = 'abcdefghijklmnopqrstuvwxyz';
            break;
        default:
            chars = 'ABCDEFGHIJKMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789';
            break;
    }

    while (len > 0) {
        len--;
        result += chars[Math.floor(Math.random() * chars.length)];
    }

    return result;
}

proto.addslashes = function (str) {
    return SqlString.escape(str);
}

proto.sign = function (key, obj) {
    let arr = new Array();
    let num = 0;
    for (let i in obj) {
        if (obj[i] === null || obj[i] === undefined || obj[i] === '' || i === 'sign') {
            continue;
        }

        arr[num] = i;
        num++;
    }

    let sortArr = [...arr].reverse();
    let str = '';
    for (let i in sortArr) {
        str += sortArr[i] + '=' + obj[sortArr[i]] + '&';
    }

    return crypto.createHash('md5').update(str + 'key=' + key).digest('hex').toUpperCase();
}

// 最大公约数
proto.maxGongYue = function (m, n) {
    let u = +m, v = +n, t = v;
    while (v !== 0) {
        t = u % v;
        u = v;
        v = t;
    }
    return u;
}

// 向左侧填充
proto.pad = function (v, l) {
    v += '';
    while (v.length < l) v = '0' + v;
    return v;
};

proto.splitWord = function () {
    let res = [];

    for (let i = 0, len = arguments.length, str; i < len; i++) {
        str = arguments[i] && arguments[i].toString().match(/[\u4e00-\u9fa5]|[a-zA-Z0-9]+/g);
        str && str.forEach(v => {
            if (res.indexOf(v) === -1) {
                res.push(v);
            }
        });
    }

    return res;
};

// 生成签名
proto.toSign = function (obj, key) {
    let arr = new Array();
    let num = 0;
    for (let i in obj) {
        if (obj[i] === null || obj[i] === undefined || obj[i] === '' || i === 'sign') {
            continue;
        }

        arr[num] = i;
        num++;
    }

    let sortArr = [...arr].reverse();
    let str = '';
    for (let i in sortArr) {
        str += sortArr[i] + '=' + obj[sortArr[i]] + '&';
    }

    return crypto.createHash('md5').update(str + 'key=' + key).digest('hex').toUpperCase();
};

/**
 * 将list递归为tree
 */
proto.toTree = function (list) {
    let group = {}, ids = [];

    list.forEach(item => {
        if (!group[item.pid]) {
            ids.push(item.pid);
            group[item.pid] = {id: item.pid, children: []};
        } else if (!group[item.pid].children) {
            group[item.pid].children = [];
        }
        group[item.pid].children.push(item);

        if (group[item.id]) {
            let i = ids.indexOf(item.id);
            if (i !== -1) ids.splice(i, 1);
            group[item.id] = Object.assign(item, group[item.id]);
        } else {
            group[item.id] = item;
        }
    });

    // 只返回跟节点
    let res = [];
    ids.forEach(id => {
        if (!group[id].name) {
            res = res.concat(group[id].children)
        } else {
            res.push(group[id]);
        }
    });

    return res;
};

proto.eachJsPath = function (root, prefix, callback) {
    let files;

    try {
        files = fs.readdirSync(root, {withFileTypes: true});
    } catch (e) {
        return;
    }

    for (let i = 0, len = files.length, name, file; i < len; i++) {
        name = files[i].name;
        file = root + '/' + name;

        if (files[i].isDirectory()) {
            proto.eachJsPath(file, prefix + (prefix ? '.' : '') + name, callback);
        } else if (name === 'index.js') {
            callback(prefix, file);
        } else if (name.endsWith('.js')) {
            name = prefix + (prefix ? '.' : '') + name.substr(0, name.length - 3);
            callback(name, file);
        }
    }
}
