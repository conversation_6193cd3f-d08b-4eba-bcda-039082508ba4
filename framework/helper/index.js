"use strict";

const merge = require('./merge');
const helper = {merge};

merge(helper, require('./assign'));
merge(helper, require('./crypto'));
merge(helper, require('./common'));
merge(helper, require('./validator'));
merge(helper, require('./country'));
merge(helper, require('./curl'));
merge(helper, require('./array'));
merge(helper, require('./date'));
merge(helper, require('./convert'));
merge(helper, require('./tree'));

module.exports = helper;
