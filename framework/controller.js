/**
 * API接口命名约定
 * 增加：create、insert
 * 删除：delete、destroy、remove
 * 修改：update
 * 获取单个：get
 * 列表查询：list
 * 分页查询：query
 * 统计查询：count
 */
module.exports = class Controller {

  constructor(ctx) {
    this.ctx = ctx;
  }

  getLogin(must) {
    const {user} = this.ctx.token;

    if (!user || !user.id) {
      this.ctx.assert(must === false, 401, '授权信息无效');
    }

    return user;
  }

  __call(action) {

  }
}