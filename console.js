global.ROOT_PATH = __dirname;

const env = require('./config/env');
const http = require('http');
const compose = require('koa-compose');
const App = require('./framework/app');
const LiveService = require('./app/common/controller/live');

// 允许访问模块
const app = App(env, 'common');

const req = {
  headers: {
    host: '127.0.0.1',
    'x-forwarded-for': '127.0.0.1',
  },
  query: {},
  querystring: '',
  host: '127.0.0.1',
  hostname: '127.0.0.1',
  protocol: 'http',
  secure: 'false',
  method: 'GET',
  url: '/',
  path: '/',
  socket: {
    remoteAddress: '127.0.0.1',
    remotePort: 7001
  }
};

const res = new http.ServerResponse(req);
const ctx = app.createContext(req, res);
const fn = compose(app.middleware);

fn(ctx).then(() => {
  new LiveService(ctx).start();
}).catch(e => {
  console.error(e);
});

console.log('data-service已运行');