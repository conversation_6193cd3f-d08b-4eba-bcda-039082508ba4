{"name": "", "version": "1.0.0", "description": "", "main": "app.js", "scripts": {"start": "node app.js", "serve": "node console.js"}, "author": "", "license": "ISC", "dependencies": {"amqplib": "^0.5.6", "async-validator": "^3.2.3", "aws-sdk": "^2.1378.0", "axios": "^0.20.0", "bignumber.js": "^9.0.0", "co-body": "^6.0.0", "formidable": "^1.2.1", "ioredis": "^4.14.0", "jsonwebtoken": "^8.5.1", "koa": "^2.5.2", "moment": "^2.22.2", "mysql2": "^1.6.4", "node-schedule": "^1.3.2", "path-to-regexp": "^2.2.1", "piliv2": "^2.1.1", "qiniu": "^7.3.0", "sequelize": "^5.8.6", "validator": "^13.1.1", "xml2js": "^0.4.19", "node-xlsx": "^0.21.0"}}