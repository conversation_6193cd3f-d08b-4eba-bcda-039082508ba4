module.exports = {
  create: {
    name: {type: 'string', required: true, min: 2, max: 32, message: 'invalid name'},
    sex: {type: 'number', pattern: /^[12]$/, required: true, message: 'invalid sex'},
    company_id: {type: 'integer', required: true, min: 1, message: 'invalid company_id'},
    sequence: {type: 'number', required: true, min: 1, max: 9999, message: 'invalid sequence'},
    birthday: {type: 'string', pattern: /^\d{4}-\d{2}-\d{2}$/, required: true, message: 'invalid birthday'},
    join_work: {type: 'string', pattern: /^\d{4}-\d{2}-\d{2}$/, required: true, message: 'invalid join_work'}
  },
  update: {
    id: {type: 'integer', required: true, min: 1, message: 'invalid id'},
    name: {type: 'string', required: true, min: 2, max: 32, message: 'invalid name'},
    sex: {type: 'number', pattern: /^[12]$/, required: true, message: 'invalid sex'},
    company_id: {type: 'integer', required: true, min: 1, message: 'invalid company_id'},
    sequence: {type: 'number', required: true, min: 1, max: 9999, message: 'invalid sequence'},
    birthday: {type: 'string', pattern: /^\d{4}-\d{2}-\d{2}$/, required: true, message: 'invalid birthday'},
    join_work: {type: 'string', pattern: /^\d{4}-\d{2}-\d{2}$/, required: true, message: 'invalid join_work'}
  },
  party: {
    id: {type: 'integer', required: true, min: 1, message: 'invalid id'},
    party_code: {type: 'integer', required: true, message: 'invalid party_code'},
    party_join: {type: 'string', pattern: /^\d{4}-\d{2}-\d{2}$/, required: true, message: 'invalid party_join'},
    dept_id: {type: 'integer', required: true, message: 'invalid dept_id'}
  }
}