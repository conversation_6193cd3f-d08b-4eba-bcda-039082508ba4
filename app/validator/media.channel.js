module.exports = {
  create: {
    owner: {type: 'integer', required: true, message: 'invalid owner'},
    type: {type: 'integer', pattern: /^[012]$/, required: true, message: 'invalid type'},
    name: {type: 'string', required: true, min: 2, max: 32, message: 'invalid name'},
    pid: {type: 'integer', required: true, min: 0, message: 'invalid pid'},
    intro: {type: 'string', min: 0, max: 128, message: 'invalid intro'},
    sort: {type: 'integer', pattern: /^\d+$/, required: true, message: 'invalid sort'},
    first_sort: {type: 'integer', pattern: /^[01]$/, required: true, message: 'invalid first_sort'},
    second_sort: {type: 'integer', pattern: /^[01]$/, required: true, message: 'invalid second_sort'}
  },
  update: {
    id: {type: 'integer', min: 1, required: true, message: 'invalid id'},
    type: {type: 'integer', pattern: /^[012]$/, required: true, message: 'invalid type'},
    name: {type: 'string', required: true, min: 2, max: 32, message: 'invalid name'},
    pid: {type: 'integer', required: true, min: 0, message: 'invalid pid'},
    intro: {type: 'string', min: 0, max: 128, message: 'invalid intro'},
    sort: {type: 'integer', pattern: /^\d+$/, required: true, message: 'invalid sort'},
    first_sort: {type: 'integer', pattern: /^[01]$/, required: true, message: 'invalid first_sort'},
    second_sort: {type: 'integer', pattern: /^[01]$/, required: true, message: 'invalid second_sort'}
  }
}