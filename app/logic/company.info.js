"use strict";

/**
 * 企业信息管理
 */
module.exports = class extends Logic {

  async getEditData(id) {
    let sql = 'SELECT * FROM company WHERE id=' + id;
    let rows = await this.ctx.db().query(sql, {type: 'SELECT'});
    return rows[0];
  }

  /**
   * 更新基础信息
   */
  async updateBase(id, data) {
    let {assert} = this.ctx;

    assert(/^\d+$/.test(id), 'invalid id');
    //assert(data.logo, 'invalid logo');
    assert(data.name, 'invalid name');
    assert(data.alias, 'invalid alias');
    // 读取数据库判断code是否存在(略)
    //assert(/^\d+$/.test(data.industry_code), 'invalid industry_code');
    //assert(/^\d+$/.test(data.stage_code), 'invalid stage_code');
    //assert(/^\d+$/.test(data.scale_code), 'invalid scale_code');

    await this.ctx.model('company').update(id, {
      //logo: data.logo,
      name: data.name,
      alias: data.alias,
      company_image: data.company_image,
      company_background: data.company_background
      //industry_code: data.industry_code,
      //stage_code: data.stage_code,
      //scale_code: data.scale_code
    });

    return '已保存';
  }

  /**
   * 更新介绍
   */
  async updateIntro(id, intro) {
    let {assert} = this.ctx;

    assert(/^\d+$/.test(id), 'invalid id');
    assert(!!intro, 'invalid intro');

    await this.ctx.model('company').update(id, {intro: intro});

    return '已保存';
  }

  /**
   * 更新作息时间
   */
  async updateWorkReset(id, data) {
    let {assert} = this.ctx;

    assert(/^\d+$/.test(id) && id > 0, 'invalid id');
    assert(/^\d{2}:\d{2}$/.test(data.work_start), 'invalid work_start');
    assert(/^\d{2}:\d{2}$/.test(data.work_end), 'invalid work_end');
    assert(/^\d+$/.test(data.overtime) && data.overtime > 0, 'invalid overtime');

    await this.ctx.model('company').update(id, {
      work_start: data.work_start,
      work_end: data.work_end,
      overtime: data.overtime
    });

    return '已保存';
  }

  /**
   * 更改联系地址
   */
  async updateContact(id, data) {
    let {assert} = this.ctx;

    assert(/^\d+$/.test(id) && id > 0, 'invalid id');
    assert(/^http(s)?:\/\//.test(data.website), 'invalid website');
    assert(/^\d{6}$/.test(data.province_code), 'invalid province_code');
    assert(/^\d{6}$/.test(data.city_code), 'invalid city_code');
    assert(/^\d{6}$/.test(data.district_code), 'invalid district_code');
    assert(!!data.address, 'invalid address');
    assert(data.lng && !isNaN(data.lng), 'invalid lng');
    assert(data.lat && !isNaN(data.lat), 'invalid lat');

    await this.ctx.model('company').update(id, {
      website: data.website,
      service_phone: data.service_phone,
      province_code: data.province_code,
      city_code: data.city_code,
      district_code: data.district_code,
      address: data.address,
      lng: data.lng,
      lat: data.lat
    });

    return '已保存';
  }

  async getUpdateData(id, prevTime) {
    let data = await this.ctx.model('company').getUpdateData(id, prevTime);
    let update = !!data;

    return {
      type: 'company',
      name: '党部信息',
      update: update,
      message: update ? '有变更，可更新' : '无更新',
      content: data
    };
  }
}
