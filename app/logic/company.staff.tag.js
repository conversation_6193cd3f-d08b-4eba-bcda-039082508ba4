"use strict";

/**
 * 企业员工分组管理
 */
module.exports = class extends Logic {

  get model() {
    return this.ctx.model('company.staff.tag');
  }

  async getList(q) {
    return this.model.getList(q.company_id, q.visible);
  }

  async create(params) {
    let body = {
      company_id: params.company_id,
      name: params.name,
      sort: /^\d+$/.test(params.sort) ? parseInt(params.sort) : 0,
      hidden: params.hidden ? 1 : 0
    };

    return this.model.create(body);
  }

  async update(params) {
    let body = {
      name: params.name,
      sort: /^\d+$/.test(params.sort) ? parseInt(params.sort) : 0,
      hidden: params.hidden ? 1 : 0
    };

    return this.model.update(params.id, body);
  }

  async delete(id) {
    const {model} = this;
    let data = await model.find(id);

    this.ctx.assert(data, '分组不存在');
    if (data.people > 0) {
      await model.clearUser(data.company_id, id);
    }

    await model.delete(id);
  }

  /**
   * 设置排序
   */
  async saveSort(obj) {
    let list = [];
    for (let key in obj) {
      if (helper.is_id(key) && /^\d+$/.test(obj[key])) {
        list.push({key: key, val: parseInt(obj[key])});
      }
    }

    if (list.length == 0) return;

    await this.model.saveSort(list);
  }

  async getUpdateData(companyId, prevTime) {
    let list = await this.model.getUpdateData(companyId, prevTime);
    let update = list.length > 0;

    return {
      type: 'userTag',
      name: '党部分组',
      update: update,
      message: update ? '可更新' : '无更新',
      content: list
    };
  }
}