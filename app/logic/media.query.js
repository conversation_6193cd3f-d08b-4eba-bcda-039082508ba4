"use strict";

module.exports = class extends Logic {

  async getQueryEditorViewParams(q) {
    const params = {status: q.status, type: this.type};

    if (helper.is_ids(q.id)) {
      params.id = q.id;
    }

    if (helper.is_id(q.creator)) {
      params.creator = q.creator;
    }

    if (helper.is_id(q.channel)) {
      let res = await this.ctx.model('media.channel').getItemQuery(q.channel);
      params.channel = res.in;
      params.first_sort = res.first_sort;
      params.second_sort = res.second_sort;
    }

    if (/^\d{10}_\d{10}$/.test(q.pubdate)) {
      params.pubdate = q.pubdate.split('_');
    }

    if (q.kw) {
      params.kw = helper.splitWord(q.kw);
    }

    params.offset = /^\d+$/.test(q.offset) ? parseInt(q.offset) : 0;
    params.limit = /^\d+$/.test(q.limit) ? parseInt(q.limit) : 15;

    return params;
  }

}