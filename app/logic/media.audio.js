const MediaBaseLogic = require('./media.base');

module.exports = class extends MediaBaseLogic {

    get type () {
        return 'audio';
    }

    getInsertAudio (params) {
        let i = params.file_url.lastIndexOf('.');
        let ext = params.file_url.substr(i + 1).toLowerCase();
        this.ctx.assert(ext == 'mp3', '仅支持.mp3文件');

        return {
            duration: params.duration,
            address: params.file_url,
            file_size: params.file_size
        }
    }

    /**
     * 创建
     */
    async create (params) {
        let audio = this.getInsertAudio(params);
        let item = this.getInsertItem(params);
        let info = this.getInsertInfo(params);
        let content = this.getInsertContent(params);
        let columns = this.getInsertChannel(params);
        let keyword = this.getInsertKeyword(params);
        let model = this.ctx.model('media.audio');

        return model.transaction(async (transaction) => {
            // 插入item表
            item = await model.insertItem(item, transaction);

            info.item_id = audio.id = item.id;

            // 插入info表
            info = await model.insertInfo(info, transaction);

            // 绑定item版本
            let vid = info.id;

            await Promise.all([
                // 插入audio表
                model.insertAudio(audio, transaction),
                // 插入content表
                model.insertContent(vid, content, transaction),
                // 插入column表
                model.insertChannel(vid, columns, transaction),
                // 插入search表
                model.insertKeyword(vid, keyword, transaction),
                // 变更item版本
                model.updateItem(item.id, {version: vid, draft: vid}, transaction)
            ]);

            // 返回结果
            return {id: item.id, type: item.type, version: vid, draft: vid, status: item.status};
        });
    }

    /**
     * 修改
     */
    async update (params) {
        let info = this.getInsertInfo(params);
        let content = this.getInsertContent(params);
        let columns = this.getInsertChannel(params);
        let keyword = this.getInsertKeyword(params);
        let item = await this.getCoreInfo(params.id);
        let model = this.ctx.model('media.audio');

        return model.transaction(async (transaction) => {
            // 插入info表
            info.item_id = item.id;
            info = await model.insertInfo(info, transaction);

            // 绑定item版本
            let vid = info.id;
            let version = item.status == 'online' ? item.version : info.id;
            let top_expired = params.top_expired || 0;
            let staff_id = params.staff_id || 0;
            let display_date = params.display_date || 0;
            let data = {version: version, draft: vid};
            if (top_expired > 0) {
                data.top_expired = top_expired;
            }
            if (staff_id > 0) {
                data.staff_id = staff_id;
            }
            if (display_date > 0) {
                data.display_date = display_date;
            }
            await Promise.all([
                // 插入content表
                model.insertContent(vid, content, transaction),
                // 插入column表
                model.insertChannel(vid, columns, transaction),
                // 插入search表
                model.insertKeyword(vid, keyword, transaction),
                // 变更item版本
                model.updateItem(item.id, data, transaction)
            ]);

            // 返回结果
            return {id: item.id, type: item.type, version: version, draft: vid, status: item.status};
        });
    }

    /**
     * 读取详情 - 编辑者
     */
    async getEditData (id, version) {
        let {assert} = this.ctx;
        assert(helper.is_id(id), 'invalid id');
        let media = await this.ctx.model('media.audio').getEditData(id, version);
        assert(media, '资源ID无效');
        return media;
    }

    async getCoreInfo (id) {
        let {assert} = this.ctx;
        assert(helper.is_id(id), 'invalid id');
        let media = await this.ctx.model('media.audio').getCoreById(id, this.type);
        assert(media, '资源ID无效');
        return media;
    }

    /**
     * 删除
     */
    async delete (id, version) {
        this.ctx.assert(helper.is_id(id), 'invalid id');

        version = helper.is_id(version) ? version : null;

        let model = this.ctx.model('media.audio');
        let media = await (version ? model.getCoreByVersion(version, this.type) : model.getCoreById(id, this.type));
        this.ctx.assert(media && media.id == id, '资源信息无效');

        if (!version || version == media.version) {
            if (media.status == 'online') {
                await this.ctx.model('media.channel').resetItemCount(media.version, -1);
            }

            let vs = await model.getVersions(media.id, media.version);
            await model.deleteById(media.id);
            if (vs.length > 0) await model.deleteVersion(vs);
        } else {
            await model.deleteVersion(version);
        }

        return '已删除';
    }

    /**
     * 读取列表 - 编辑者
     */
    async queryEditorView (q) {
        const params = await this.getQueryEditorViewParams(q);

        let query = await this.ctx.model('media.audio').queryEditorView(params);

        return {
            count: await query.count(),
            rows: await query.rows(params.offset, params.limit)
        }
    }

    async getUpdateData (ownerId, selfId, prevTime, offset, limit) {
        let list = await this.ctx.model('media.audio').getUpdateData(ownerId, selfId, prevTime, offset, limit);
        let len = list.length;

        return {
            type: 'mediaAudio',
            name: '音频素材',
            update: len > 0,
            message: len ? ('共' + len + '条，可更新') : '无更新',
            content: list
        };
    }

    async getUpdateDataByChannel (ownerId, selfId, offset, limit, channelId) {
        let list = await this.ctx.model('media.audio').getUpdateDataByChannel(ownerId, selfId, offset, limit, channelId);
        let len = list.length;

        return {
            type: 'mediaAudio',
            name: '音频素材',
            update: len > 0,
            message: len ? ('共' + len + '条，可更新') : '无更新',
            content: list
        };
    }
}
