"use strict";

const UserAccountLogic = require('./user.account');
const WeiXin = require(__rootdir + '/extend/weixin');
const jwt = require('jsonwebtoken');

/**
 * 登录逻辑
 */
module.exports = class extends UserAccountLogic {

    /**
     * 密码登录
     */
    async byPassword (params) {
        const {assert} = this.ctx;
        assert(params.username, '请输入账号');
        assert(params.password, '请输入密码');

        // 如果是11位手机号
        if (/^\d{11}$/.test(params.username)) {
            const mobile = helper.is_mobile(params.username, '+86');
            if (mobile) params.username = mobile.value;
        }

        // 获取用户信息
        let user = await this.getLoginUserByUsername(params.username);

        assert(user, '账号不存在');
        assert(user.password && user.password === params.password, '密码错误');
        assert(user.audit_status === 1, '账号审核中,请联系管理员');
        assert(user.status === 1, '账号不可用,请联系管理员');
        if(user.pid > 0 && user.home_datasource === 1){
            // 获取用户信息
            let pUser = await this.getLoginUserById(user.pid);
            if(pUser){
                user.company_id = pUser.company_id;
            }
        }
        return this.loginSuccess(user);
    }

    /**
     * 手机验证码登录
     */
    async byMobile (params) {
        const mobile = this.validateMobile(params.mobile, params.country);
        const username = mobile.value;

        // 校验验证码
        await this.checkCaptcha('login', username, params.captcha);

        // 获取用户信息
        let user = await this.getLoginUserByUsername(username);

        // 如果不存在则创建账号
        if (!user) {
            user = await this.ctx.logic('user.create').byMobile({number: params.mobile, prefix: params.country});
        }

        return this.loginSuccess(user);
    }

    async byEmail (params) {
        const username = params.email;
        this.validateEmail(username);

        // 校验验证码
        await this.checkCaptcha('login', username, params.captcha);

        // 获取用户信息
        let user = await this.getLoginUserByUsername(username);

        // 如果不存在则创建账号
        if (!user) {
            user = await this.ctx.logic('user.create').byEmail({email: username});
        }

        return this.loginSuccess(user);
    }

    //todo 待实现
    goBindAccount () {

    }

    /**
     * 微信公众号授权登录
     */
    async byWxPub (params) {
        const {assert} = this.ctx;
        assert(params.code, 'invalid code');

        // 用code换取微信用户信息
        const wx = new WeiXin(this.ctx, this.ctx.env.wxmp);
        const wu = await wx.getAuthUser(params.code);

        const wuModel = this.ctx.model('wx_user');

        const u1 = await wuModel.get(wu.openid, wu.appid);
        if (u1) Object.assign(wu, u1);

        if (!u1.uid && u1.unionid) {
            const u2 = await wuModel.getUserId(u1.unionid);
            if (u2) wu.uid = u2;
        }

        if (wu.id) {
            wuModel.update(wu.id, wu);
        } else {
            wuModel.create(wu);
        }

        // 提示客户端绑定账号
        // if (!wu.uid) return this.goBindAccount({
        //   id: wu.id,
        //   type: 'wx',
        //   invite_code: params.invite_code
        // });

        const user = await this.getLoginUserById(wu.uid);

        return this.loginSuccess(user);
    }

    /**
     * 微信APP授权登录
     */
    async byWxApp (params) {
        params = {
            appid: '',
            code: ''
        };

        // 获取appid信息

        // 根据code获取微信用户openid

        // 根据openid读取本地微信用户表

        // 如果微信用户不存在则插入

        // 如果微信用户没有user_id则提示客户端绑定账号

        // 如果微信用户含有user_id则获取用户信息

        // 写入session
    }

    /**
     * 微信小程序登录
     */
    async byWxPro (params) {
        params = {
            appid: '',
            code: ''
        };

        // 获取appid信息

        // 根据code获取微信用户openid

        // 根据openid读取本地微信用户表

        // 如果微信用户不存在则插入

        // 如果微信用户没有user_id则提示客户端绑定账号

        // 如果微信用户含有user_id则获取用户信息

        // 写入session
    }

    /**
     * 苹果账号登录
     */
    async byApple (params) {
        let payload;
        try {
            payload = jwt.verify(params.identityToken, AppleKey, {algorithms: 'RS256'});
        } catch (e) {
            this.ctx.throw('签名校验失败，请尝试使用其他方式登录');
        }

        // 判断用户是否已存在
        let openid = payload.sub;
        this.ctx.assert(openid === params.user, '授权信息无效');

        let au = {
            uid: 0,
            openid: openid,
            nickname: params.fullName.nickname,
            last_auth: helper.time(),
            email: params.email
        };

        let db = this.ctx.db();
        let sql = 'SELECT id, uid FROM apple_user WHERE openid=? LIMIT 1';
        let [u1] = await db.query(sql, {type: 'SELECT', replacements: [openid]});

        if (u1) Object.assign(au, u1);

        let user = au.uid ? await this.getUserById(au.uid) : null;
        let rps = [];

        sql = ' apple_user SET ';
        for (let key in au) {
            if (key !== 'id') {
                sql += (rps.length > 0 ? ',' : '') + `${key}=?`;
                rps.push(au[key]);
            }
        }

        if (!au.id) {
            sql = 'INSERT' + sql;
            let res = await db.query(sql, {type: 'INSERT', replacements: rps});
            au.id = res[0];
        } else {
            sql = 'UPDATE' + sql + ' WHERE id=' + au.id;
            await db.query(sql, {type: 'UPDATE', replacements: rps});
        }

        // 让客户端去绑定账号
        if (!user) {
            let third = {id: au.id, type: 'apple', timestamp: au.last_auth, invite_code: body.invite_code};
            third.sign = helper.sign(this.ctx.app.env.keys, third);

            return {
                openid: au.openid,
                redirect: {
                    name: 'login.bind',
                    params: {
                        skip: 0,
                        third: third
                    },
                    query: {
                        redirect: body.redirect
                    }
                }
            }
        }

        return this.loginSuccess(user, body.redirect);
    }

    /**
     * 发送验证码
     */
    async sendSmsCaptcha (number) {
        const mobile = this.validateMobile(number);
        return await this.ctx.logic('captcha').sendCaptcha('login', 1, mobile.value);
    }
}
