"use strict";

const fs = require('fs');
const path = require('path');
const QiNiu = require(__rootdir + '/extend/qiniu');
const ALLOW = {
  image: {type: 1, root: 'img'},
  video: {type: 2, root: 'video'},
  audio: {type: 3, root: 'audio'},
  upload: {type: 0, root: 'upload'},
};

/**
 * 文件上传
 */
module.exports = class extends Logic {

  /**
   * 获取客户端配置
   */
  async getClientConfig() {
    let cnf = this.ctx.env.upload;

    return {
      type: cnf.type,
      zone: cnf.zone,
      https: cnf.https,
      domain: cnf.domain,
      bucket: cnf.bucket
    }
  }

  /**
   * 获取服务端配置
   */
  async getServeConfig() {
    let cnf = this.ctx.env.upload;
    return {
      root: this.ctx.env.static,
      type: cnf.type,
      zone: cnf.zone,
      https: cnf.https,
      bucket: cnf.bucket,
      domain: cnf.domain,
      accessKey: cnf.accessKey,
      secretKey: cnf.secretKey,
      origin: 'http' + (cnf.https ? 's' : '') + '://' + cnf.domain
    }
  }

  /**
   * 授权客户端上传
   */
  async request(body, userId) {
    let cnf = await this.getServeConfig();
    let allow = ALLOW[body.type] || ALLOW.upload;
    let http = this.ctx.protocol + '://';
    let prefix = this.generatePath(allow.root);
    let callbackBody = {debug: this.ctx.env.debug ? 1 : 0, type: allow.type, creator: userId || 0};

    switch (body.type) {
      case 'image':
        Object.assign(callbackBody, {width: body.width, height: body.height});
        break;
      case 'audio':
        Object.assign(callbackBody, {duration: body.duration});
        break;
      case 'video':
        Object.assign(callbackBody, {duration: body.duration, width: body.width, height: body.height});
        break;
    }

    switch (cnf.type) {
      case 1: // 七牛
        let callbackUrl = this.ctx.origin + '/v1/media.upload.success?platform=qiniu';
        let qiNiu = new QiNiu(cnf);
        return qiNiu.authClientUpload(prefix, callbackUrl, callbackBody);
      case 2: // 阿里
        break;
      default: // 本地
        return {
          type: cnf.type,
          prefix: prefix,
          remote: http + this.ctx.hostname + '/v1/media.upload.submit',
          append: callbackBody,
          origin: cnf.origin
        }
    }
  }

  generatePath(prefix) {
    let now = helper.date('YYYYMMDDHHmmss');
    let path = (prefix || '') + '/' + now.substr(0, 8) + '/';
    path += now.substr(8, 2) * 3600 + now.substr(10, 2) * 60 + parseInt(now.substr(12));
    path += Math.random().toString().substr(-3) + '/';
    return path;
  }

  generateName(ext) {
    return Math.random().toString().substr(-3) + (ext ? '.' + ext : '');
  }

  generateObject(prefix, ext) {
    return this.generatePath(prefix) + this.generateName(ext);
  }

  getExtName(name) {
    let i = name.lastIndexOf('.');
    if (i === -1) {
      return ['', ''];
    }

    return [name.substr(0, i), name.substr(i + 1)];
  }

  /**
   * 上传文件
   */
  async submit(body, files, creator) {
    let file = files.file;
    this.ctx.assert(file, 'invalid file');

    let allow = ALLOW[body.type] || ALLOW.upload;
    let cnf = await this.getServeConfig();
    let upload = {
      mime: file.type,
      type: allow.type,
      size: file.size,
      width: 0,
      height: 0,
      duration: 0,
      md5: '',
      hash: '',
      creator: creator || 0,
      private: 0
    };

    switch (upload.type) {
      case 1:
        upload.width = parseInt(body.width) || 0;
        upload.height = parseInt(body.height) || 0;
        break;
      case 2:
        upload.width = parseInt(body.width) || 0;
        upload.height = parseInt(body.height) || 0;
        upload.duration = parseFloat(body.duration) || 0;
        break;
      case 3:
        upload.duration = parseFloat(body.duration) || 0;
        break;
    }

    if (body.key.startsWith(allow.root + '/')) {
      upload.object = body.key;
    } else {
      upload.object = this.generateObject(allow.root, upload.ext);
    }

    await this.moveFile(file.path, cnf.root + '/' + upload.object);

    return this.success(cnf, upload);
  }

  /**
   * 移动文件
   */
  async moveFile(source, target) {
    return new Promise((resolve, reject) => {

      fs.mkdir(path.dirname(target), {recursive: true}, (err) => {
        if (err) {
          return reject(new Error('创建目标文件夹失败'));
        }

        fs.rename(source, target, function (err) {
          return err ? reject(new Error('移动文件失败')) : resolve();
        })
      })
    })
  }

  /**
   * 上传回调
   */
  async callback(type, body) {
    let cnf = await this.getServeConfig();

    switch (type) {
      case 'qiniu':
        let auth = this.ctx.get('authorization');
        let qiNiu = new QiNiu(cnf);
        let isTrue = await qiNiu.isQiniuCallback(this.ctx.url, body, auth);
        this.ctx.assert(isTrue, '回调参数校验失败');
        break;
    }

    this.ctx.body = await this.success(cnf, {
      creator: body.creator || 0,
      bucket: body.bucket || '',
      object: body.key,
      mime: body.mime,
      name: (body.name || '').substr(0, 20),
      type: body.type,
      size: body.size,
      private: body.private ? 1 : 0,
      width: body.width || 0,
      height: body.height || 0,
      duration: body.duration || 0,
      md5: body.md5 || '',
      hash: body.hash || ''
    }, body.debug);
  }

  async success(cnf, upload, debug) {
    [upload.name, upload.ext] = this.getExtName(upload.name);

    if (!debug) {
      let {id} = await this.ctx.db('media_upload').create(upload);
      upload.id = id;
    }

    upload.url = cnf.origin + '/' + upload.object;
    return upload;
  }

  /**
   * 获取用户上传的图片
   */
  async getUserImages(userId, query) {
    let where = 'WHERE creator=' + userId + ' AND type=1';
    let params = [];

    if (query.ext) {
      where += ' AND ext=?';
      params.push(query.ext);
    }

    if (query.name) {
      where += ' AND name LIKE ?';
      params.push('%' + query.name + '%');
    }

    let scope = this;
    let db = this.ctx.db();
    let opt = {type: 'SELECT', replacements: params};
    let count = -1;

    return {
      async count() {
        let sql = 'SELECT COUNT(*) AS total FROM media_upload ' + where;
        let rows = await db.query(sql, opt);
        return count = rows[0].total;
      },
      async rows(offset = query.offset, limit = query.limit) {
        if (count === 0) return [];

        offset = /^\d+$/.test(offset) ? offset : 0;
        limit = /^\d+$/.test(limit) ? limit : 30;

        let sql = 'SELECT id, object, name, size, private, width, height' +
          ' FROM media_upload ' + where +
          ' ORDER BY id DESC' +
          ` LIMIT ${offset}, ${limit}`;
        let rows = await db.query(sql, opt);

        if (rows.length === 0) return rows;

        let cnf = await scope.getServeConfig();
        rows.forEach(item => {
          item.url = cnf.origin + '/' + item.object;
        });
        return rows;
      }
    }
  }
}
