"use strict";

/**
 * 企业分组管理
 */
module.exports = class extends Logic {

  get model() {
    return this.ctx.model('company.group');
  }

  async getList(q) {
    return this.model.getList(q.company_id, q.visible);
  }

  async create(params) {
    let body = {
      company_id: params.company_id,
      name: params.name,
      sort: /^\d+$/.test(params.sort) ? parseInt(params.sort) : 0,
      hidden: params.hidden ? 1 : 0
    };

    return this.model.create(body);
  }

  async update(params) {
    let body = {
      name: params.name,
      sort: /^\d+$/.test(params.sort) ? parseInt(params.sort) : 0,
      hidden: params.hidden ? 1 : 0
    };

    return this.model.update(params.id, body);
  }

  async delete(id) {
    return this.model.delete(id);
  }

}