"use strict";

const UserAccountLogic = require('./user.account');

/**
 * 注册逻辑
 */
module.exports = class extends UserAccountLogic {

    /**
     * 注册手机号账号
     */
    async byMobile (params) {
        const mobile = this.validateMobile(params.mobile, params.country);
        const username = mobile.value;

        await this.needNotExists(username, '手机号已存在');
        await this.checkCaptcha('register', username, params.captcha);

        const user = await this.ctx.logic('user.create').byMobile({
            number: params.mobile,
            prefix: params.country,
            password: params.password
        });
        return this.loginSuccess(user);
    }

    /**
     * 注册自定义账号
     */
    async byUsername (params) {
        const {username} = params;

        this.validateUsername(username);
        this.validatePassword(params.password);
        await this.needNotExists(username, '账号已经存在');

        const user = await this.ctx.logic('user.create').byUsername({
            username: username,
            password: params.password
        });
        return this.loginSuccess(user);
    }

    /**
     * 注册邮箱账号
     */
    async byEmail (params) {
        const {email} = params;

        this.isEmail(email);
        this.validatePassword(params.password);
        await this.checkCaptcha('register', email, params.captcha);
        await this.needNotExists(email, '邮箱已经存在');

        const user = await this.createUser({email: email, password: params.password});

        return this.loginSuccess(user);
    }

    /**
     * 管理员创建账号
     */
    async byAdmin (params) {
        this.ctx.throw('功能暂未实现');
    }

    /**
     * 企业账号
     */
    async byCompany (params) {
        const {company_name} = params;

        // 判断企业
        this.ctx.assert(company_name && company_name.length >= 4 && company_name.length <= 32, 'invalid company_name');
        const companyModel = this.ctx.model('company');
        const exists = await companyModel.exists(company_name);
        this.ctx.assert(!exists, '组织名称已存在');

        // 判断账号密码
        const {username} = params;
        this.validateUsername(username);
        this.validatePassword(params.password);
        await this.needNotExists(username, '登录账号已经存在');

        // 创建企业账号
        const company = await companyModel.create({name: company_name});
        const user = await this.ctx.logic('user.create').byUsername({
            username: username,
            password: params.password,
            nickname: company_name
        });
        await this.ctx.model('user.info').update(user.id, {company_id: company.id});
        user.company_id = company.id;

        // 给账号授权68
        await this.ctx.logic('user.access').setRole(user.id, 68);
        return "注册成功，等待审核"
        // return this.loginSuccess(user);
    }

    /**
     * 发送验证码
     */
    async sendSmsCaptcha (number) {
        const mobile = this.validateMobile(number);
        const username = mobile.value;

        await this.needNotExists(username, '手机号已存在');
        return await this.ctx.logic('captcha').sendCaptcha('register', 1, username);
    }
}
