"use strict";

module.exports = class extends Logic {

  async query(params) {
    let rows = await this.ctx.db().query("SELECT * FROM cms_sys_menu ORDER BY pid, sort, id", {type: 'SELECT'});
    if (rows.length == 0) return [];

    let menus = {};
    let nodes = {};
    let pid;

    rows.forEach(row => {
      pid = row.pid;

      if (row.type == 1) { // 节点
        if (!nodes[pid]) nodes[pid] = [];
        nodes[pid].push(row);
      } else { // 菜单
        if (!menus[pid]) menus[pid] = [];
        menus[pid].push(row);
      }
    });

    return this.sortList(0, menus, nodes);
  }

  sortList(pid, groups, nodes, level = 1, list = []) {
    if (!groups[pid]) return list;

    groups[pid].forEach(menu => {
      menu.level = level;
      menu.isLeaf = groups[menu.id] ? 0 : 1;

      if (nodes[menu.id]) menu.nodes = nodes[menu.id];

      list.push(menu);

      if (!menu.isLeaf) {
        this.sortList(menu.id, groups, nodes, level + 1, list);
      }
    });

    return list;
  }

  async create(data) {
    await this.ctx.db('cms_sys_menu').create({
      title: data.title,
      type: data.type,
      alias: data.alias || '',
      path: data.path || '',
      icon: data.icon || '',
      pid: isNaN(data.pid) ? 0 : data.pid,
      sort: isNaN(data.sort) ? 100 : data.sort,
      disabled: data.disabled ? 1 : 0
    });
  }

  async update(data) {
    await this.ctx.db('cms_sys_menu').update({
      title: data.title,
      type: data.type,
      alias: data.alias || '',
      path: data.path || '',
      icon: data.icon || '',
      pid: isNaN(data.pid) ? 0 : data.pid,
      sort: isNaN(data.sort) ? 100 : data.sort,
      disabled: data.disabled ? 1 : 0
    }, {where: {id: data.id}});
  }

  async delete(id) {
    this.ctx.assert(helper.is_id(id), 'invalid id');

    let db = this.ctx.db();

    let rows = await db.query(`SELECT id FROM cms_sys_menu WHERE pid=${id} AND type=0 LIMIT 1`, {type: 'SELECT'});
    this.ctx.assert(rows.length < 1, '请先删除下级菜单');

    await db.query(`DELETE FROM cms_sys_menu WHERE id=${id} OR pid=${id}`, {type: 'DELETE'});
  }

  async sidebar(roles) {
    let [{menus, nodes}, pages] = await Promise.all([
      this.getAccess(roles),
      this.ctx.db().query("SELECT id, title, type, alias, path, icon, pid, disabled FROM cms_sys_menu ORDER BY sort,id", {type: 'SELECT'})
    ]);

    let group = {}, ids = [], key;

    pages.forEach(item => {
      key = item.type == 1 ? 'nodes' : 'children';

      if (!group[item.pid]) {
        ids.push(item.pid);
        group[item.pid] = {id: item.pid};
      }

      if (!group[item.pid][key]) group[item.pid][key] = [];
      group[item.pid][key].push(item);

      if (item.type != 1) {
        if (group[item.id]) {
          let i = ids.indexOf(item.id);
          if (i != -1) ids.splice(i, 1);
          group[item.id] = Object.assign(item, group[item.id]);
        } else {
          group[item.id] = item;
        }
      }
    });

    // 只返回跟节点
    let list = [];
    ids.forEach(id => {
      if (!group[id].name) {
        list = list.concat(group[id].children)
      } else {
        list.push(group[id]);
      }
    });

    return this.eachSidebar(list, menus, nodes);
  }

  eachSidebar(list, menus, nodes) {
    for (let i = list.length - 1, menu; i > -1; i--) {
      menu = list[i];

      if (menu.disabled) {
        list.splice(i, 1);
        continue;
      }

      let children = menu.children;
      if (!children) {
        if (!menus.has(menu.alias)) {
          list.splice(i, 1);
        }
      } else {
        this.eachSidebar(children, menus, nodes);
        if (children.length == 0) {
          list.splice(i, 1);
        }
      }
    }

    return list;
  }
}
