"use strict";

/**
 * 用户权限
 */
module.exports = class extends Logic {

  async setRole(userId, roles) {
    roles = Array.isArray(roles) ? roles.join(',') : roles.toString();
    let opts = {type: 'UPDATE', replacements: [roles]};

    const db = this.ctx.db();
    let sql = 'SELECT id FROM cms_user_access WHERE user_id=' + userId;
    const rows = await db.query(sql, {type: 'SELECT'});

    if (rows.length > 0) {
      await db.query('UPDATE cms_user_access SET roles=? WHERE user_id=' + userId, opts);
    } else {
      await db.query("INSERT INTO cms_user_access SET roles=?, user_id=" + userId + ", nodes='{}'", opts);
    }
  }

}
