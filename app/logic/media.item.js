const MediaQueryLogic = require('./media.query');
const MediaNews = require('./media.news');
const MediaVideo = require('./media.video');
const MediaAudio = require('./media.audio');
const MediaMotto = require('./media.motto');
const MediaImage = require('./media.image');
const Sequelize = require('sequelize');
const Op = Sequelize.Op;

module.exports = class MediaItem extends MediaQueryLogic {

    static getInstance (ctx, name) {
        switch (name) {
            case 'news':
                return new MediaNews(ctx);
            case 'video':
                return new MediaVideo(ctx);
            case 'audio':
                return new MediaAudio(ctx);
            case 'motto':
                return new MediaMotto(ctx);
            case 'image':
                return new MediaImage(ctx);
            default:
                return new MediaItem(ctx);
        }
    }

    /**
     * 读取列表 - 编辑者
     */
    async queryEditorView (q) {
        const params = await this.getQueryEditorViewParams(q);

        let query = await this.ctx.model('media.query').queryEditorView(params);

        return {
            count: await query.count(),
            rows: await query.rows(params.offset, params.limit)
        }
    }

    /**
     * 读取列表 - 下级用户
     */
    async queryMediaList (q) {
        q.creator = /^\d+$/.test(q.creator) ? parseInt(q.creator) : null;
        q.userId = /^\d+$/.test(q.userId) ? parseInt(q.userId) : null;
        q.subUserId = /^\d+$/.test(q.subUserId) ? parseInt(q.subUserId) : null;
        //作者id，支部管理使用
        q.owner = /^\d+$/.test(q.owner) ? parseInt(q.owner) : null;
        //党员id
        q.staff_id = /^\d+$/.test(q.staff_id) ? parseInt(q.staff_id) : null;
        return await this.ctx.model('media.query').queryMediaList(q);
    }

    /**
     * 待审核资讯列表
     */
    async queryAuditList (q) {
        let query = await this.ctx.model('media.query').queryAuditList(q);
        return {
            count: await query.count(),
            rows: await query.rows()
        }
    }

    /**
     * 快速搜索
     */
    async quickSearch (id, q) {
        q.id = /^\d+$/.test(q.id) ? parseInt(q.id) : null;
        q.kw = q.id ? null : '+' + helper.splitWord(q.kw).join('>');
        q.offset = /^\d+$/.test(q.offset) ? parseInt(q.offset) : 0;
        q.limit = /^\d+$/.test(q.limit) ? parseInt(q.limit) : 15;

        return this.ctx.model('media.query').quickSearch(id, q);
    }

    async getUpdateDelete (ownerId,pid, prevTime) {
        let list = await this.ctx.model('media.query').getUpdateDelete(ownerId,pid, prevTime);
        let len = list.length;

        return {
            type: 'mediaDelete',
            name: '清理素材',
            update: len > 0,
            message: len ? ('共' + len + '条') : '无',
            content: list
        };
    }

    async userTabs (id) {
        return this.ctx.model('media.query').userTabs(id);
    }

    async branchStatistics (id, company_id) {
        return this.ctx.model('media.query').branchStatistics(id, company_id);
    }

    //定时清理置顶过期资讯
    async handleTopExpired () {
        await this.ctx.db('media_item').update({is_top: 0}, {
            where: {
                top_expired: {
                    [Op.and]: {
                        [Op.lt]: helper.time(),
                        [Op.gt]: 0
                    }
                }
            }
        });
    }
}
