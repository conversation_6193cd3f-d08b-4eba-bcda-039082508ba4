"use strict";

const QiNiu = require(__rootdir + '/extend/qiniu');

module.exports = class extends Logic {

  sendToEmail() {

  }

  sendToMobile() {

  }

  /**
   * 发送验证码
   */
  async sendCaptcha(type, to, number, params = {}) {
    const key = type + ':' + number;
    const exp = params.expire_in || 300;
    const code = params.code || helper.random(1000, 9999);
    const body = {type: type, to: to, number: number, content: code, exp: exp, success: 1, message: '', ip: this.ctx.request.ip};
    let msg = '验证码已发送';

    try {
      if (this.ctx.env.debug) {
        msg = '调试验证码：' + code;
      } else if (to === 1) {
        await this.sendSmsCaptcha(type, number, code);
      } else if (to === 2) {

      }

      await this.ctx.redis.set(key, code, exp);
    } catch (e) {
      body.success = 0;
      body.message = e.toString();
      msg = '验证码发送失败，请稍后再试';
    }

    // 保存记录

    return msg;
  }

  /**
   * 发送通知
   */
  async sendNotify() {

  }

  /**
   * 发送短信验证码
   */
  async sendSmsCaptcha(type, mobile, code) {
    await QiNiu.getInstance(this.ctx.env.upload).sendSingleMessage(type, mobile, {code: code});
  }

  /**
   * 发送邮箱验证码
   */
  async sendEmailCaptcha(type, email, params) {

  }

  async validate(type, username, value) {
    const val = await this.ctx.redis.get(type + ':' + username);
    return val && val == value;
  }
}