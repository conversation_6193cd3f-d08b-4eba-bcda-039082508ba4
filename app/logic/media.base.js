"use strict";

const MediaQueryLogic = require('./media.query');

module.exports = class extends MediaQueryLogic {

    get type () {
        return 'other';
    }

    /**
     * (存稿)添加和编辑统一入口
     */
    async save (editor, params) {
        params.editor = editor;
        if (params.top_expired > 0) {
            params.top_expired = helper.time(params.top_expired);
        }
        if (helper.is_id(params.id)) {
            if (params.sub_users && Array.isArray(params.sub_users) && params.sub_users.length > 0) {
                await this.ctx.db("media_item").update(params, {where: {id: params.id}});
            }
            return this.update(params);
        }
        // 初始公共参数
        params.creator = editor;
        return this.create(params);
    }

    /**
     * 提交审核
     */
    async submitAudit (id, version) {
        let info = await this.ctx.model('media.item').getOnPublishInfo(version);

        this.ctx.assert(info.item_id == id, '版本信息不匹配');

        this.ctx.assert(info.status != 'uploading', '请等待上传完成');
        this.ctx.assert(info.status != 'transcoding', '请等待转码完成');

        let model = this.ctx.model('media.item');
        await model.updateItem(info.item_id, {
            status: 'auditing',
            version: info.id,
            draft: info.id,
            modified: helper.time()
        });
        return '已提交审核';
    }

    /**
     * 审核
     */
    async audit (id, version, status) {
        let info = await this.ctx.model('media.item').getOnPublishInfo(version);
        this.ctx.assert(info.item_id == id, '版本信息不匹配');
        let model = this.ctx.model('media.item');
        let statusVal = '';
        if (status === 1) {
            statusVal = 'online';
        }
        if (status === 2) {
            statusVal = 'rejected';
        }
        await model.updateItem(info.item_id, {
            status: statusVal,
            version: info.id,
            draft: info.id,
            is_top: 1,
            modified: helper.time()
        });
        return '已提交审核';
    }

    /**
     * 申请发布
     */
    async applyPublish (id, version) {
        let info = await this.ctx.model('media.item').getOnPublishInfo(version);

        this.ctx.assert(info.item_id == id, '版本信息不匹配');

        //await this.ctx.isAllowed('media:item:put', source.creator)

        return this.publish(info);
    }

    /**
     * 发布
     */
    async publish (info) {
        this.ctx.assert(info.status != 'uploading', '请等待上传完成');
        this.ctx.assert(info.status != 'transcoding', '请等待转码完成');
        this.ctx.assert(info.version != info.id || info.status != 'online', '当前版本已发布');

        let model = this.ctx.model('media.item');
        await model.updateItem(info.item_id, {
            status: 'online',
            version: info.id,
            draft: info.id,
            modified: helper.time()
        });
        let itemData = await this.ctx.db("media_item").findByPk(info.item_id);
        if (Array.isArray(itemData.sub_users) && itemData.sub_users.length > 0) {
            let itemArr = [];
            itemData.sub_users.forEach(id => {
                let item = this.getInsertItem(itemData);
                item.status = itemData.status;
                item.type = itemData.type;
                item.creator = id;
                item.pid = info.item_id
                item.version = info.id;
                item.draft = info.id;
                item.modified = helper.time();
                delete item.sub_users;
                itemArr.push(item);
            })
            await this.ctx.db().getQueryInterface().bulkInsert("media_item", itemArr);
        }
        let channel = this.ctx.model('media.channel');
        if (info.status == 'online') {
            await channel.resetItemCount(info.version, -1);
        }
        await channel.resetItemCount(info.id, 1);

        return '已发布';
    }

    /**
     * 申请下架
     */
    async applyOffline (id, version) {
        this.ctx.assert(helper.is_id(id), 'invalid id');
        let media = await this.getCoreInfo(id);
        return this.offline(media);
    }

    /**
     * 发布 变更为 下架
     */
    async offline (media) {
        this.ctx.assert(media.status == 'online', '未发布无需下架');
        await this.ctx.model('media.item').updateItem(media.id, {status: 'offline'});
        await this.ctx.model('media.channel').resetItemCount(media.version, -1);
        return '已下架';
    }

    getInsertItem (data) {
        let now = helper.time();
        return {
            type: this.type,
            status: 'editing',
            creator: data.creator,
            version: 0,
            draft: 0,
            pv: 0,
            uv: 0,
            is_top: this.isTop(data.tags),
            praise_num: 0,
            reply_num: 0,
            share_num: 0,
            collect_num: 0,
            reward_num: 0,
            allow_reply: 1,
            created: now,
            modified: now,
            sub_users: data.sub_users,
            top_expired: data.top_expired || 0,
            staff_id: data.staff_id,
            display_date: data.display_date
        }
    }

    getInsertInfo (data) {
        let now = helper.time();
        return {
            editor: data.editor,
            item_id: 0,
            title: data.title || '',
            subtitle: data.subtitle || '',
            abstract: data.abstract || '',
            source: data.source || '',
            cover_url: data.cover_url || '',
            pubdate: data.pubdate || now,
            year: data.year || helper.date('YYYY', now),
            area: data.area || '',
            language: data.language || '',
            province_code: data.province_code || 0,
            city_code: data.city_code || 0,
            district_code: data.district_code || 0,
            detail_url: data.detail_url || '',
            top_left_corner: data.top_left_corner || '',
            lower_right_corner: data.lower_right_corner || '',
            created: now
        }
    }

    getInsertContent (data, json = false) {
        let content = data.content || '';
        return json ? JSON.stringify(content) : content;
    }

    getInsertKeyword (data) {
        let kw = helper.splitWord(data.name, data.title, data.subtitle, data.abstract);
        return kw.join(' ');
    }

    getInsertChannel (data) {
        let list = Array.isArray(data.channels) ? data.channels : [];

        return list.map(item => {
            if (!data.sub_users || data.sub_users.length === 0) {
                this.ctx.assert(/^\d+$/.test(item.cid), 'invalid channels.cid');
            }
            return {
                cid: item.cid || 0,
                cover_url: item.cover_url || '',
                title: item.title || '',
                sort: parseInt(item.sort) || 0
            }
        })
    }

    isTop (tags) {
        return 0;
        //return Array.isArray(tags) && tags.some(tag => tag.value == 'zhiding') ? 1 : 0;
    }

    /**
     * 查看历史版本
     */
    async version (itemId) {
        helper.is_id(itemId) || this.ctx.throw('invalid itemId');

        let sql = `SELECT
                item.id, item.type, info.id AS version, item.creator, info.editor,
                info.title, info.author, u.nickname AS editor_name, info.created
               FROM media_info AS info
               INNER JOIN media_item AS item ON item.id=info.item_id
               INNER JOIN user AS u ON u.id=info.editor
               WHERE info.item_id=${itemId}
               ORDER BY info.id DESC`;
        return this.ctx.db().query(sql, {type: 'SELECT'});
    }

    /**
     * 增加点击量
     */
    async addPv (id, uid) {
        let db = this.ctx.db();
        let uv = 0;
        let add1 = parseInt(Math.random().toString().substr(-2) / 2);
        let add2 = Math.ceil(Math.random().toString().substr(-1) / 2);

        if (uid) {
            // 增加PV记录
            let date = helper.date('YYYY-MM-DD');
            let pvsql = `INSERT INTO media_pv SET
                     date='${date}',
                     item_id='${id}',
                     user_id='${uid}',
                     times=1
                   ON DUPLICATE KEY UPDATE
                     times=times+1`;
            // 增加UV记录
            let uvsql = `INSERT INTO media_uv SET
                     item_id='${id}',
                     user_id='${uid}',
                     times=1,
                     created=UNIX_TIMESTAMP(),
                     newest=UNIX_TIMESTAMP()
                   ON DUPLICATE KEY UPDATE
                     times=times+1, newest=VALUES(newest)`;
            let [res] = await Promise.all([
                db.query(uvsql, {type: 'UPSERT'}),
                db.query(pvsql, {type: 'UPDATE'}),
            ]);

            uv = res === 1 ? 1 : 0;
        }

        let sql = `UPDATE media_item SET pv=pv + IF(pv<5000, ${add1}, ${add2}), uv=uv+${uv} WHERE id=${id}`;
        await db.query(sql, {type: 'UPDATE'});
    }

    /**
     * 获取分享参数
     */
    async getShareParams (id) {
        this.ctx.assert(helper.is_id(id), 'invalid id');
        let code = helper.encodeId(id, 'media.' + this.type);

        let logo, address;
        if (this.type == 'video') {
            let sql = 'SELECT address FROM media_video WHERE id=' + id;
            let tmp = await this.ctx.db().query(sql, {type: 'SELECT'});
            address = tmp[0].address;
        } else if (this.type == 'audio') {
            let sql = 'SELECT file_url FROM media_audio WHERE id=' + id;
            let tmp = await this.ctx.db().query(sql, {type: 'SELECT'});
            address = tmp[0].file_url;
        }

        return {
            id: id,
            type: this.type,
            code: code,
            inner_link: '/media/detail/' + code,
            outer_link: '/media/iframe/' + code,
            logo: logo,
            address: address
        }
    }

    /**
     * 获取预览地址
     */
    async getPreviewUrl (id, version, prefix) {
        this.ctx.assert(helper.is_id(id), 'id failed');

        version = helper.is_id(version) ? version : 'item.version';

        let sql = `SELECT item.id, item.type, item.status, info.id AS version, info.detail_url
               FROM media_item AS item
               INNER JOIN media_info AS info ON info.id=${version} AND info.item_id=item.id
               WHERE item.id=${id} AND type='${this.type}'`;
        let [media] = await this.ctx.db().query(sql, {type: 'SELECT'});

        this.ctx.assert(media, '资源ID无效');

        let url = media.detail_url;

        if (!url) {
            let alias = helper.encodeId(media.id, 'media.' + this.type);
            let query = {version: media.version, expired: helper.time('+15 minute'), oncestr: helper.randString(16)};
            let sign = helper.toSign(Object.assign({id: media.id, type: media.type}, query), this.ctx.env.keys);
            url = prefix + '/media/detail/' + alias + '?version=' + query.version + '&expired=' + query.expired + '&oncestr=' + query.oncestr + '&sign=' + sign;
        } else if (url.startsWith('/')) {
            url = prefix + url;
        } else if (url == '-') {
            url = '';
        }

        return {url: url}
    }
}
