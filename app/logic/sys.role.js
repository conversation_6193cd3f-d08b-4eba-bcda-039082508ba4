"use strict";

module.exports = class extends Logic {

  async query(params) {
    let fields = params.fields ? 'id, `name`, enabled, `group`' : '*';
    return await this.ctx.db().query(`SELECT ${fields} FROM cms_sys_role ORDER BY enabled DESC, id`, {type: 'SELECT'});
  }

  async create(data) {
    await this.ctx.db('cms_sys_role').create({
      group: data.group || '',
      name: data.name,
      menus: data.menus,
      nodes: data.nodes,
      enabled: data.enabled ? 1 : 0,
      remark: data.remark || ''
    });

    await this.updateCache();
  }

  async update(data) {
    await this.ctx.db('cms_sys_role').update({
      group: data.group || '',
      name: data.name,
      menus: data.menus,
      nodes: data.nodes,
      enabled: data.enabled ? 1 : 0,
      remark: data.remark || ''
    }, {where: {id: data.id}});

    await this.updateCache();
  }

  async delete(id) {
    this.ctx.assert(helper.is_id(id), 'invalid id');

    await this.ctx.db().query("DELETE FROM cms_sys_role WHERE id=" + id, {type: 'DELETE'});

    await this.updateCache();
  }

  async updateCache() {
    let sql = `SELECT id, nodes FROM cms_sys_role WHERE enabled=1`;
    let rows = await this.ctx.db().query(sql, {type: 'SELECT'});
    let {redis} = this.ctx;
    let tall = [];

    // 清空角色权限
    await redis.del('role:access');

    rows.forEach(role => {
      for (let key in role.nodes) {
        tall.push(redis.hset(
          'role:access',
          key + ':' + role.id,
          role.nodes[key]
        ));
      }
    });

    await Promise.all(tall);
  }
}
