"use strict";

/**
 * 组织架构
 */
module.exports = class extends Logic {

  async list(companyId) {
    this.ctx.assert(/^\d+$/.test(companyId), 'invalid company_id');

    let rows = await this.ctx.model('company.dept').getListByCompanyId(companyId);

    if (rows.length == 0) return rows;

    let root = {};
    rows.forEach(item => {
      if (root[item.id]) {
        Object.assign(root[item.id], item);
      } else {
        root[item.id] = item;
      }

      if (!root[item.pid]) {
        root[item.pid] = {children: [item]};
      } else if (!root[item.pid].children) {
        root[item.pid].children = [item];
      } else {
        root[item.pid].children.push(item);
      }
    });

    return root[0].children;
  }

  async validate(params, isAdd) {
    let {assert} = this.ctx;

    let body = {
      name: params.name,
      code: params.code || '',
      sort: params.sort || 0,
      leader_id: params.leader_id || 0,
      pid: parseInt(params.pid) || 0,
      phone: params.phone || '',
      intro: params.intro || ''
    };

    if (isAdd) {
      body.leaf = 1;
      assert(/^\d+$/.test(params.company_id), 'invalid company_id');
      body.company_id = params.company_id;
    }

    assert(!isNaN(body.leader_id), 'invalid leader_id');
    assert(body.name, 'invalid name');
    assert(/^[a-zA-z0-9\-]+$/, 'invalid code');
    assert(!isNaN(body.sort), 'invalid sort');
    assert(!isNaN(body.pid), 'invalid pid');

    // 判断leader_id是否存在
    if (body.leader_id) {
      let leader = await this.ctx.model('company.staff').exists(body.company_id, 'user_id', body.leader_id);
      assert(leader, '部门主管不存在');
    }

    if (body.pid) {
      let dept = await this.ctx.model('company.dept').getBaseInfo(body.pid);
      assert(dept, '上级部门不存在');

      if (!isAdd && dept.level > 1 && dept.parents.split(',').indexOf(params.id.toString()) != -1) {
        this.ctx.throw('上级ID错误');
      }

      body.level = dept.level + 1;
      body.parents = dept.parents + (dept.level > 1 ? ',' : '') + body.pid;
    } else {
      // 判断是否有多个跟节点
      let root = await this.ctx.model('company.dept').getRoot(body.company_id);
      assert(!root || (!isAdd && root.id == params.id), '只能有一个跟部门');

      body.level = 1;
      body.parents = '';
    }

    return body;
  }

  /**
   * 创建部门
   */
  async create(params) {
    let body = await this.validate(params, true);
    return this.ctx.model('company.dept').create(body);
  }

  /**
   * 编辑部门
   */
  async update(params) {
    let cid = params.company_id;
    let {id} = params;
    let {assert} = this.ctx;

    assert(/^\d+$/.test(id), 'invalid id');

    let model = this.ctx.model('company.dept');
    let oldData = await model.getBaseInfo(id);
    assert(oldData.company_id == cid, 'invalid company_id');

    let newData = await this.validate(params, false);
    await model.update(id, newData);

    if (oldData.pid != newData.pid) {
      if (oldData.pid) {
        // 减掉部门人数
        await model.updatePeople(oldData.parents, -oldData.people);
        // 判定是否有下级
        await model.updateLeaf(oldData.pid, cid);
      }

      // 增加上级部门人数
      await model.updatePeople(newData.parents, oldData.people);
      // 标记上级有子部门
      await model.updateLeaf(newData.pid, cid);
      // 变更子部门级别
      await model.updateParents(id, oldData.parents, newData.parents, cid);
    }
  }

  /**
   * 删除部门
   */
  async delete(id) {
    let {assert} = this.ctx;
    assert(/^\d+$/.test(id), 'invalid id');

    let model = this.ctx.model('company.dept');

    let dept = await model.getBaseInfo(id);
    assert(dept.leaf, '请先清空下级部门');

    // 删除
    await model.delete(id);

    // 更新上级
    if (dept.pid > 0) {
      let num = await model.getChildNum(dept.pid, dept.company_id)
      if (num < 1) {
        await model.update(dept.pid, {leaf: 1});
      }
    }

    return '已删除';
  }
}