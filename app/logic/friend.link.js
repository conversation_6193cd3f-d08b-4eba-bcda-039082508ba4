/**
 * 友情链接
 */
module.exports = class extends Logic {

  async query() {
    let sql = `SELECT * FROM friend_link ORDER BY seq, id`;
    return this.ctx.db().query(sql, {type: 'SELECT'});
  }

  getBody(body) {
    this.ctx.assert(body.title, '链接名称必填');

    return {
      title: body.title,
      url: body.url || '',
      icon: body.icon || '',
      seq: isNaN(body.seq) ? 100 : body.seq,
      visible: body.visible ? 1 : 0
    }
  }

  async create(body) {
    body = this.getBody(body);
    let {id} = await this.ctx.db('friend_link').create(body);
    return {id: id};
  }

  async update(body) {
    let {id} = body;
    this.ctx.assert(helper.is_id(id), 'invalid id');

    body = this.getBody(body);
    await this.ctx.db('friend_link').update(body, {where: {id: id}});
  }

  async delete(id) {
    this.ctx.assert(helper.is_id(id), 'id failed');

    await this.ctx.db('friend_link').destroy({where: {id: id}});
  }

  async sort(list) {
    this.ctx.assert(Array.isArray(list), '参数错误');
    let db = this.ctx.db();

    await Promise.all(list.map(item => {
      if (helper.is_id(item.id) && /^\d+$/.test(item.seq))
        return db.query(`UPDATE friend_link SET seq=${item.seq} WHERE id=${item.id}`, {type: 'UPDATE'});
    }));
  }
}