"use strict";

module.exports = class extends Logic {

    randNickname () {
        return helper.randString(16);
    }

    async byMobile (info) {
        const username = '' + info.prefix + info.number;
        const user = await this.createUser({
            mobile: info.number,
            mobile_area: info.prefix
        });

        await this.createAccount(user.id, username, 1);

        return user;
    }

    async byEmail (info) {
        const user = await this.createUser({
            email: info.email,
            password: info.password
        });

        await this.createAccount(user.id, info.email, 2);

        return user;
    }

    async byUsername (info) {
        const user = await this.createUser({
            username: info.username,
            password: info.password,
            nickname: info.nickname
        });

        await this.createAccount(user.id, info.username, 2);

        return user;
    }

    async createUser (info) {
        if (!info.nickname) info.nickname = this.randNickname();
        return this.ctx.model('user.account').createUser(info);
    }

    async createAccount (userId, username, type) {
        return this.ctx.model('user.account').createAccount(userId, username, type);
    }
}
