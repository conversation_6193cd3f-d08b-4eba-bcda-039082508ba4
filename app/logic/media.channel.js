"use strict";

/**
 * 频道管理
 */
module.exports = class extends Logic {
    recursionDataTree (dataList, pid) {
        let resultList = [];
        if (!dataList) return null;
        for (const map of dataList) {
            let bmid_new = map["id"];
            let parentId = map["pid"];
            if (pid == parentId) {
                const data = map;
                let childrenList = this.recursionDataTree(dataList, bmid_new);
                if (childrenList)
                    data["children"] = childrenList;
                resultList.push(data);
            }
        }
        return resultList;
    }

    async getList (q) {
        this.ctx.assert(/^\d+$/.test(q.owner), 'invalid owner');
        let rows = await this.ctx.model('media.channel').getListByOwner(q.owner);
        if (rows.length == 0) return rows;
        let dataTree = this.recursionDataTree(rows, 0);
        return dataTree;
    }

    async validate (params, isAdd) {
        let body = await this.ctx.validate('media.channel.create', {
            id: isAdd ? null : params.id,
            owner: params.owner,
            type: params.type,
            name: params.name,
            intro: params.intro || '',
            sort: params.sort || 0,
            logo: params.logo || '',
            pid: params.pid,
            hidden: params.hidden,
            first_sort: params.first_sort,
            second_sort: params.second_sort,
            template: params.template,
        });

        let {assert} = this.ctx;

        if (body.pid) {
            let parent = await this.ctx.model('media.channel').getBaseInfo(body.pid);

            assert(parent, '上级节点不存在');
            assert(parent.type === 0, '上级节点不能为路径');

            if (!isAdd && parent.level > 1 && parent.parents.split(',').indexOf(body.id.toString()) != -1) {
                this.ctx.throw('上级ID错误');
            }

            body.level = parent.level + 1;
            body.parents = parent.parents + (parent.level > 1 ? ',' : '') + body.pid;
        } else {
            body.level = 1;
            body.parents = '';
        }

        return body;
    }

    /**
     * 创建部门
     */
    async create (params) {
        let body = await this.validate(params, true);
        let model = this.ctx.model('media.channel');
        return model.create(body);
    }

    /**
     * 编辑部门
     */
    async update (params) {
        let {id} = params;
        let newData = await this.validate(params, false);
        let model = this.ctx.model('media.channel');
        let oldData = await model.getBaseInfo(newData.id);

        this.ctx.assert(oldData.owner == newData.owner, 'invalid owner');

        await model.update(id, newData);

        if (oldData.pid != newData.pid) {
            if (oldData.pid) {
                // 减掉数量
                await model.updateItemCount(oldData.parents, -oldData.item_count);
                // 判定是否有下级
                await model.updateLeaf(oldData.pid, oldData.owner);
            }

            // 增加上级部门人数
            await model.updateItemCount(newData.parents, oldData.item_count);
            // 标记上级有子部门
            await model.updateLeaf(newData.pid, oldData.owner);
            // 变更子部门级别
            await model.updateParents(id, oldData.parents, newData.parents);
        }
    }

    /**
     * 删除部门
     */
    async delete (id) {
        let {assert} = this.ctx;
        assert(/^\d+$/.test(id), 'invalid id');

        let model = this.ctx.model('media.channel');

        let info = await model.getBaseInfo(id);
        assert(info.leaf, '请先清空下级部门');

        // 删除
        await model.delete(id);

        // 更新上级
        if (info.pid > 0) {
            let num = await model.getChildNum(info.pid, info.owner)
            if (num < 1) {
                await model.update(info.pid, {leaf: 1});
            }
        }

        return '已删除';
    }

    getTypeFromList (channels, type) {
        let count = 0, list = [], item;

        for (let i = channels.length - 1; i >= 0; i--) {
            item = channels[i];

            if (item.children) {
                let res = this.getTypeFromList(item.children, type);
                if (res.list.length > 0) {
                    item = Object.assign({}, item, {children: res.list});
                    list.splice(0, 0, item);
                    count += res.count;
                }
            } else if (item.type == type) {
                list.splice(0, 0, item);
                count++;
            }
        }

        return {count: count, list: list};
    }

    async getUpdateData (ownerId) {
        let result = {type: 'mediaChannel', name: '频道栏目', message: '未创建', update: true, content: []};
        let rows = await this.ctx.model('media.channel').getListByOwner(ownerId);

        if (rows.length == 0) return result;

        let root = {};
        rows.forEach(item => {
            if (root[item.id]) {
                Object.assign(root[item.id], item);
            } else {
                root[item.id] = item;
            }

            if (!root[item.pid]) {
                root[item.pid] = {children: [item]};
            } else if (!root[item.pid].children) {
                root[item.pid].children = [item];
            } else {
                root[item.pid].children.push(item);
            }
        });

        let res = this.getTypeFromList(root['0'].children, 1);
        result.content = res.list;
        result.message = '共' + res.count + '条，可更新';

        return result;
    }

    /**
     * 设置排序
     */
    async saveSort (obj) {
        let list = [];
        for (let key in obj) {
            if (helper.is_id(key) && /^\d+$/.test(obj[key])) {
                list.push({key: key, val: parseInt(obj[key])});
            }
        }

        if (list.length == 0) return;

        await this.ctx.model('media.channel').saveSort(list);
    }
}
