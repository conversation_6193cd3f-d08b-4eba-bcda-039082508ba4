"use strict";

/**
 * 企业相关
 */
module.exports = class extends Logic {

  /**
   * 创建企业
   */
  create(params) {
    let body = {
      name: params.name,
      logo: params.logo || '',
      status: 1,
      intro: params.intro || '',
      created: helper.time(),
      industry_code: params.industry_code || 0,
      start_date: params.start_date || '',
      oper_name: params.oper_name || '',
      license: params.license || '',
      credit_code: params.credit_code || '',
      work_start: params.work_start || '',
      work_end: params.work_start || '',
      rest_code: params.rest_code || 0,
      stage_code: params.stage_code || 0,
      scale_code: params.scale_code || 0,
      website: params.website || '',
      welfare: params.welfare || '',
      images: Array.isArray(params.images) ? params.images : [],
      video: params.video || {}
    };
  }

  /**
   * 更改基本信息
   */
  update() {

  }

  setAddress(params) {
    let body = {
      province_code: params.province_code,
      city_code: params.city_code,
      district_code: params.district_code,
      address: params.address,
      lng: params.lng,
      lat: params.lat
    };
  }

  /**
   * 企业视频
   */
  setVideo() {

  }

  /**
   * 更新相册
   */
  setImages() {

  }

  /**
   * 设置介绍
   */
  setIntro() {

  }
}