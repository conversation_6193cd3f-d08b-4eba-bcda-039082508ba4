"use strict";

module.exports = class extends Logic {

    async getAuthMenu (uid) {
        const db = this.ctx.db();

        // 获取用户角色
        let rows = await db.query('SELECT roles FROM cms_user_access WHERE user_id=' + uid, {type: 'SELECT'});

        if (rows.length == 0 || !rows[0].roles) return [];

        let [{menus, nodes}, pages] = await Promise.all([
            this.getAccess(rows[0].roles),
            db.query("SELECT id, title, type, alias, path, icon, pid, disabled FROM cms_sys_menu ORDER BY sort,id", {type: 'SELECT'})
        ]);

        let group = {}, ids = [], key;

        pages.forEach(item => {
            key = item.type == 1 ? 'nodes' : 'children';

            if (!group[item.pid]) {
                ids.push(item.pid);
                group[item.pid] = {id: item.pid};
            }

            if (!group[item.pid][key]) group[item.pid][key] = [];
            group[item.pid][key].push(item);

            if (item.type != 1) {
                if (group[item.id]) {
                    let i = ids.indexOf(item.id);
                    if (i != -1) ids.splice(i, 1);
                    group[item.id] = Object.assign(item, group[item.id]);
                } else {
                    group[item.id] = item;
                }
            }
        });

        // 只返回跟节点
        let list = [];
        ids.forEach(id => {
            if (!group[id].name) {
                list = list.concat(group[id].children)
            } else {
                list.push(group[id]);
            }
        });

        return this.eachSidebar(list, menus, nodes);
    }

    async getAccess (roles) {
        let menus = new Set();
        let nodes = new Set();
        let res = {menus, nodes};
        let sql = `SELECT menus, nodes FROM cms_sys_role WHERE id IN (${roles}) AND enabled=1`;
        let rows = await this.ctx.db().query(sql, {type: 'SELECT'});

        rows.forEach(role => {
            role.menus.forEach(key => {
                menus.add(key);
            });

            for (let key in role.nodes) {
                nodes.add(key);
            }
        });

        return res;
    }

    eachSidebar (list, menus, nodes) {
        for (let i = list.length - 1, menu; i > -1; i--) {
            menu = list[i];

            if (menu.disabled) {
                list.splice(i, 1);
                continue;
            }

            let children = menu.children;
            if (!children) {
                if (!menus.has(menu.alias)) {
                    list.splice(i, 1);
                }
            } else {
                this.eachSidebar(children, menus, nodes);
                if (children.length == 0) {
                    list.splice(i, 1);
                }
            }
        }

        return list;
    }

    /**
     * 获取权限所需的用户信息
     */
    async getUser (id) {
        id = parseInt(id);

        let sql = 'SELECT roles, nodes FROM cms_user_access WHERE user_id=' + id;
        let [access] = await this.ctx.db().query(sql, {type: 'SELECT'});

        if (!access) {
            return {id: id, roles: [], nodes: {}};
        }

        return {id: id, roles: access.roles.split(',').map(str => parseInt(str)), nodes: access.nodes};
    }

    /**
     * 是否有权限
     */
    async hasAccess (uid, name) {
        const {nodes, roles} = await this.getUser(uid);

        if (nodes[name] !== undefined) {
            return !!nodes[name];
        }

        for (let i = 0, val, len = roles.length; i < len; i++) {
            val = await this.ctx.redis.hget('role:access', name + ':' + roles[i]);
            if (val && val != '0') return true;
        }

        return false;
    }

    /**
     * 是否允许修改他人数据
     */
    async isAllowed (name, creator, editor) {

    }

    async getNodes (uid) {
        const db = this.ctx.db();

        // 获取用户角色
        let rows = await db.query('SELECT roles FROM cms_user_access WHERE user_id=' + uid, {type: 'SELECT'});

        if (rows.length == 0 || !rows[0].roles) return [];

        let [{menus, nodes}, pages] = await Promise.all([
            this.getAccess(rows[0].roles),
        ]);
        return Array.from(nodes);
    }

}
