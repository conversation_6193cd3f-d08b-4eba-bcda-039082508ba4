"use strict";

module.exports = class extends Logic {

    /**
     * 是否符合手机号账号格式
     */
    isMobile (value, country) {
        return helper.is_mobile(value, country)
    }

    validateMobile (value, country) {
        const {assert} = this.ctx;

        assert(!!value, '请输入手机号');

        const res = this.isMobile(value, country);
        assert(res, '手机号格式错误');

        return res;
    }

    /**
     * 是否符合邮箱账号格式
     */
    isEmail (value) {
        return helper.is_email(value);
    }

    validateEmail (value) {
        const res = this.isEmail(value);
        this.ctx.assert(res, '邮箱格式错误');
    }

    /**
     * 是否符合自定义账号格式
     */
    isUsername (value) {
        return /^[a-zA-Z]\w{4,31}$/.test(value);
    }

    validateUsername (value) {
        const res = this.isUsername(value);
        this.ctx.assert(res, '账号格式错误');
    }

    validatePassword (value) {
        this.ctx.assert(value && value.length == 32, '密码检验失败');
    }

    /**
     * 发送验证码
     */
    async sendCaptcha () {

    }

    /**
     * 校验验证码
     */
    async checkCaptcha (type, username, value) {
        this.ctx.assert(value, '请输入验证码');
        const res = await this.ctx.logic('captcha').validate(type, username, value);
        this.ctx.assert(res, '验证码错误');
    }

    /**
     * 是否存在登录账号
     */
    exists (username) {
        return this.ctx.model('user.account').existsAccount(username);
    }

    async needNotExists (username, errmsg) {
        const exists = await this.ctx.model('user.account').existsAccount(username);
        this.ctx.assert(!exists, errmsg);
    }

    /**
     * 根据登录账号获取登录所需信息
     */
    async getLoginUserByUsername (username) {
        return this.ctx.model('user.account').getLoginUserByUsername(username);
    }

    async getLoginUserById (id) {
        return this.ctx.model('user.account').getLoginUserById(id);
    }

    /**
     * 登录/注册成功
     */
    async loginSuccess (user) {
        this.ctx.token.user = {id: user.id, company_id: user.company_id};
        let subUserFlag = await this.ctx.db("user").findAndCountAll({
            where: {pid: user.id}
        })

        return {
            id: user.id,
            pid: user.pid,
            sex: user.sex,
            name: user.name,
            headimg: user.headimg,
            nickname: user.nickname,
            mobile: user.mobile,
            mobile_area: user.mobile_area,
            username: user.username,
            email: user.email,
            status: 1,
            isSpecial: user.pid > 0 || subUserFlag.count > 0,
            //country_code: user.country_code,
            //province_code: user.province_code,
            //city_code: user.city_code,
            //district_code: user.district_code,
            created: user.created,
            company_id: user.company_id,
            company_name: user.company_name
        }
    }
}
