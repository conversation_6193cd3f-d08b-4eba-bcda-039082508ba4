"use strict";
const Sequelize = require('sequelize');
const Op = Sequelize.Op;
module.exports = class extends Logic {

    async getQuery (q) {
        const params = {};
        const model = this.ctx.model('user.info');
        let tmp;

        if (helper.is_id(q.id)) {
            params.id = q.id;
        }

        tmp = helper.is_mobile(q.mobile);
        if (tmp) {
            tmp = await this.ctx.model('user.account').getAccount(tmp.value);
            if (!tmp) throw new Error('手机号不存在');
            params.id = tmp.uid;
        }

        if (q.username) {
            tmp = await this.ctx.model('user.account').getAccount(q.username);
            if (!tmp) throw new Error('账号不存在');
            params.id = tmp.uid;
        }

        if (q.invcode) {
            tmp = helper.decodeId(q.invcode, 'user');
            if (!tmp) throw new Error('邀请码不存在');
            params.id = tmp.id;
        }

        if (/^\d{9,10}-\d{9,10}$/.test(q.created)) {
            params.created = q.created.split('-');
        }

        tmp = q.pid || q.p_id;
        if (helper.is_id(tmp)) {
            params.pid = tmp;
        }

        tmp = helper.is_mobile(q.p_mobile);
        if (tmp) {
            tmp = await this.ctx.model('user.account').getAccount(tmp.value);
            if (!tmp) throw new Error('推荐人不存在');
            params.pid = tmp.uid;
        }

        if (q.p_username) {
            tmp = await this.ctx.model('user.account').getAccount(q.p_username);
            if (!tmp) throw new Error('推荐人不存在');
            params.pid = tmp.uid;
        }

        if (q.p_invcode) {
            tmp = helper.decodeId(q.p_invcode, 'user');
            if (!tmp) throw new Error('推荐人不存在');
            params.pid = tmp.id;
        }
        if (q.audit_status) {
            params.audit_status = q.audit_status;
        }
        if (q.status) {
            params.status = q.status;
        }

        if (q.p_invcode) {
            tmp = helper.decodeId(q.p_invcode, 'user');
            if (!tmp) throw new Error('推荐人不存在');
            params.pid = tmp.id;
        }
        if (q.nickname) {
            params.nickname = q.nickname;
        }

        params.offset = /^\d+$/.test(q.offset) ? parseInt(q.offset) : 0;
        params.limit = /^\d+$/.test(q.limit) ? parseInt(q.limit) : 30;

        return model.query(params);
    }

    /**
     * 获取昵称
     */
    async getNickname (userId) {
        let sql = 'SELECT nickname FROM user WHERE id=' + userId;
        let rows = await this.ctx.db().query(sql, {type: 'SELECT'});
        return rows.length > 0 ? rows[0].nickname : null;
    }

    /**
     * 修改昵称
     */
    async setNickname (userId, value) {
        this.ctx.assert(typeof value == 'string' && value.length > 3 && value.length < 16, '昵称仅限4~16个字符');

        const sql = 'UPDATE user SET nickname=? WHERE id=' + userId;
        await this.ctx.db().query(sql, {type: 'UPDATE', replacements: [value]});
    }

    /**
     * 是否存在昵称
     */
    async hasNickname (value, userId) {
        let sql = 'SELECT id FROM user WHERE nickname=? LIMIT 1';
        let rows = await this.ctx.db().query(sql, {type: 'SELECT'});
        return rows.length > 0 ? rows[0].id == userId : false;
    }

    /**
     * 设置头像
     */
    async setHeadimg (userId, url) {
        await this.ctx.model('user.info').update({headimg: url}, userId);
    }

    /**
     * 获取手机号
     */
    async getMobile (userId) {
        const sql = 'SELECT mobile, mobile_area FROM user WHERE id=' + userId;
        const rows = await this.ctx.db().query(sql, {type: 'SELECT'});
    }

    /**
     * 设置手机号
     */
    setMobile (userId, value) {

    }

    /**
     * 修改性别
     */
    async setSex (userId, value) {
        let sex = value.toString();
        sex = sex == '1' ? 1 : sex == '2' ? 2 : 0;

        let sql = `UPDATE user SET sex='${sex}' WHERE id=` + userId;
        await this.ctx.db().query(sql, {type: 'UPDATE'});
    }

    /**
     * 设置区域
     */
    async setAddress (userId, address) {
        await this.ctx.db('user').update({
            province_id: address.province_id,
            city_id: address.city_id,
            district_id: address.district_id,
            address: address.address
        }, {where: {id: userId}});
    }

    /**
     * 设置密码
     */
    async setPassword (userId, password) {
        this.ctx.assert(password && password.length == 32, 'invalid password');
        await this.ctx.db('user').update({password: password}, {where: {id: userId}});
    }

    /**
     * 修改密码
     */
    async modifyPassword (userId, oldpwd, password) {
        this.ctx.assert(oldpwd && oldpwd.length == 32, 'invalid oldpwd');
        this.ctx.assert(password && password.length == 32, 'invalid password');

        let model = this.ctx.model('user.info');
        let equ = await model.equPassword(userId, oldpwd);
        this.ctx.assert(equ, '原密码错误');

        await model.setPassword(userId, password);
    }

    /**
     * 用户审核
     */
    async userVerify (userId, auditStatus) {
        this.ctx.assert(auditStatus === 1 || auditStatus === 2, 'audit_status 参数错误');
        await this.ctx.db('user').update({audit_status: auditStatus}, {where: {id: userId}});
    }

    /**
     * 可分配用户烈
     */
    async unassignList () {
        let nodes = await this.treeData(this.ctx.query);
        let {nodeId} = this.ctx.query;
        let pid = helper.findP(Number(nodeId), nodes);

        let ret = await this.ctx.db('user').findAll(
            {
                attributes: ['username', 'id', 'nickname'],
                where: {
                    [Op.and]: [
                        {audit_status: '1'},
                        {pid: 0},
                        {id: {[Op.ne]: 1}},
                        {id: {[Op.ne]: pid[0]}},
                    ]
                }
            },
        );
        return ret;
    }

    async treeData (q) {
        let rootNeeds;
        if (q.nickname) {
            rootNeeds = await this.ctx.db('user').findAll({
                attributes: ['username', 'id', 'pid', 'audit_status', 'status', 'nickname'],
                where: {nickname: {[Op.like]: `%${q.nickname}%`}, audit_status: '1'}
            })
        } else {
            rootNeeds = await this.ctx.db('user').findAll({
                attributes: ['username', 'id', 'pid', 'audit_status', 'status', 'nickname'],
                where: {audit_status: '1'}
            })
        }
        if (rootNeeds.length > 1) {
            return helper.recursionDataTree(rootNeeds, 0);
        }
        return rootNeeds;
    }


    /**
     * 分配下级用户
     */
    async assignSub (pid, subIdArr) {
        this.ctx.assert(pid && pid > 0, '无效的参数：pid');
        this.ctx.assert(subIdArr && Array.isArray(subIdArr), '无效的参数：subIdArr');
        await this.ctx.db('user').update({pid: pid}, {where: {id: subIdArr}});
    }

    /**
     * 获取直属下级用户
     */
    async subUsers (pid, nickname) {
        this.ctx.assert(pid && pid > 0, '无效的参数：pid');
        let where = {pid: pid};
        if (nickname) {
            where.nickname = {[Op.like]: "%" + nickname + "%"};
        }
        return await this.ctx.db('user').findAll({attributes: ['username', 'id', 'nickname'], where: where});
    }

    /**
     * 修改用户信息
     */
    async updateUser (body) {
        let {id, company_id} = body;
        this.ctx.assert(id && id > 0, '无效的参数：id');
        this.ctx.assert(company_id && company_id > 0, '无效的参数：company_id');
        let userWhere = {id: id};
        let userUpdateData = {
            nickname: body.nickname,
            headimg: body.headimg,
        }
        let companyWhere = {id: body.company_id}
        let companyUpdateData = {
            name: body.c_name,
            alias: body.alias,
            logo: body.logo
        }
        await this.ctx.db().transaction(async (t) => {
            await this.ctx.db('user').update(userUpdateData, {where: userWhere}, {transaction: t});
            await this.ctx.db('company').update(companyUpdateData, {where: companyWhere}, {transaction: t});
        });
    }

}
