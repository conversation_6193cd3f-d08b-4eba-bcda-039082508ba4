"use strict";

module.exports = class extends Logic {

  async getTypes(q) {
    let offset = /^\d+$/.test(q.offset) ? q.offset : 0;
    let limit = /^\d+$/.test(q.limit) ? q.limit : 0;
    let sql = `SELECT * FROM cms_sys_dict_type LIMIT ${offset}, ${limit}`;
    let rows = await this.ctx.db().query(sql, {type: 'SELECT'});
    return rows;
  }

  async getItems(type) {
    this.ctx.assert(type, 'invalid type');

    let db = this.ctx.db();
    let root = {};
    let option = {type: 'SELECT', replacements: [type]};
    let [data, rows] = await Promise.all([
      db.query('SELECT type FROM cms_sys_dict_type WHERE code=? LIMIT 1', option),
      db.query('SELECT * FROM cms_sys_dict_item WHERE type=? ORDER BY sort DESC, id', option),
    ]);

    if (rows.length == 0) return rows;

    rows.forEach(item => {
      if (data[0].type == 1) {
        item.code = parseInt(item.code);

        if (item.parent) {
          item.parent = parseInt(item.parent);
        }
      }

      if (root[item.code]) {
        Object.assign(root[item.code], item);
      } else {
        root[item.code] = item;
      }

      if (!root[item.parent]) {
        root[item.parent] = {id: null, type: item.type, code: item.parent, name: '', pinyin: '', sort: 0, state: 0, parent: '', leaf: 0, children: [item]};
      } else if (!root[item.parent].children) {
        root[item.parent].children = [item];
      } else {
        root[item.parent].children.push(item);
      }
    });

    return root[''].children;
  }

  async getPostType(params, isAdd) {
    let {assert} = this.ctx;

    assert(isAdd || /^\d+$/.test(params.id), 'invalid id');

    let body = {
      code: params.code,
      name: params.name,
      type: params.type === 1 ? 1 : 0,
      remark: params.remark || ''
    };

    assert(/^\w+$/.test(body.code), 'invalid code');
    assert(body.name && body.name.length < 16, 'invalid name');

    // 判断code是否重复出现
    let rows = await this.ctx.db().query('SELECT id FROM cms_sys_dict_type WHERE code=? LIMIT 1', {type: 'SELECT', replacements: [body.code]});

    if (rows.length > 0) {
      assert(!isAdd && rows[0].id == params.id, 'Code已存在');
    }

    return body;
  }

  async createType(params) {
    let body = await this.getPostType(params, true);
    let sql = `INSERT INTO cms_sys_dict_type SET code=?, name=?, type=${body.type}, remark=?`;
    let rps = [body.code, body.name, body.remark || ''];
    let res = await this.ctx.db().query(sql, {type: 'INSERT', replacements: rps});

    body.id = res[0];
    return body;
  }

  async updateType(params) {
    let body = await this.getPostType(params, false);
    let sql = `UPDATE cms_sys_dict_type SET name=?, type=${body.type}, remark=? WHERE id=` + params.id;
    let rps = [body.name, body.remark || ''];
    await this.ctx.db().query(sql, {type: 'UPDATE', replacements: rps});

    body.id = params.id;
    return body;
  }

  async deleteType(id) {
    this.ctx.assert(/^\d+$/.test(id), 'invalid id');

    let db = this.ctx.db();
    let [data] = await db.query('SELECT code FROM cms_sys_dict_type WHERE id=' + id, {type: 'SELECT'});

    this.ctx.assert(data, 'ID不存在');

    await Promise.all([
      db.query('DELETE FROM cms_sys_dict_type WHERE id=' + id, {type: 'DELETE'}),
      db.query('DELETE FROM cms_sys_dict_item WHERE type=?', {type: 'DELETE', replacements: [data.type]})
    ]);

    return '已删除';
  }

  async getPostItem(params, isAdd) {
    let {assert} = this.ctx;

    assert(isAdd || /^\d+$/.test(params.id), 'invalid id');

    let body = {
      type: params.type,
      parent: params.parent || '',
      code: params.code,
      name: params.name,
      pinyin: params.pinyin || '',
      sort: parseInt(params.sort) || 0,
      state: params.state === 1 ? 1 : 0,
      leaf: params.leaf === 0 ? 0 : 1
    };

    assert(body.type, 'invalid type');
    assert(!body.parent || /^\w+$/.test(body.parent), 'invalid pid');
    assert(/^\w+$/.test(body.code), 'invalid code');
    assert(body.name && body.name.length < 16, 'invalid name');

    // 判断type是否存在
    let sql, rows, db = this.ctx.db();

    if (!isAdd) {
      sql = 'SELECT leaf,' +
        '(SELECT 1 FROM cms_sys_dict_item AS child WHERE child.type=base.type AND child.parent=base.code LIMIT 1) AS hasChild ' +
        'FROM cms_sys_dict_item AS base ' +
        'WHERE base.id=' + params.id;
      rows = await db.query(sql, {type: 'SELECT'});

      assert(rows.length == 1, '原数据不存在');
      assert(rows[0].leaf || !rows[0].hasChild, '请先清空下级在变更为子页');
    } else {
      sql = 'SELECT id FROM cms_sys_dict_type WHERE code=? LIMIT 1';
      rows = await db.query(sql, {type: 'SELECT', replacements: [body.type]});
      assert(rows.length == 1, '类型不存在：' + body.type);
    }

    // 判断上级是否有效
    if (body.parent) {
      sql = 'SELECT leaf FROM cms_sys_dict_item WHERE type=? AND code=? LIMIT 1';
      rows = await db.query(sql, {type: 'SELECT', replacements: [body.type, body.parent]});

      assert(rows.length == 1, '上级编码无效');
      assert(!rows[0].leaf, '上级编码非子页');
    }

    // 判断code是否重复出现
    sql = 'SELECT id FROM cms_sys_dict_item WHERE type=? AND code=? LIMIT 1'
    rows = await db.query(sql, {type: 'SELECT', replacements: [body.type, body.code]});
    if (rows.length > 0) {
      assert(!isAdd && rows[0].id == params.id, 'Code已存在');
    }

    return body;
  }

  async createItem(params) {
    let body = await this.getPostItem(params, true);
    let sql1 = 'INSERT INTO cms_sys_dict_item SET ';
    let sql2 = '';
    let rps = [];

    for (let field in body) {
      sql2 += `,${field}=?`;
      rps.push(body[field]);
    }

    let res = await this.ctx.db().query(sql1 + sql2.substr(1), {type: 'INSERT', replacements: rps});
    await this.ctx.setDict(body.type, body.code, body.name);

    body.id = res[0];
    return body;
  }

  async updateItem(params) {
    let body = await this.getPostItem(params, false);
    let sql1 = 'UPDATE cms_sys_dict_item SET ';
    let sql2 = '';
    let rps = [];

    for (let field in body) {
      if (field != 'code') {
        sql2 += `,${field}=?`;
        rps.push(body[field]);
      }
    }

    await this.ctx.db().query(sql1 + sql2.substr(1) + ' WHERE id=' + params.id, {type: 'UPDATE', replacements: rps});
    await this.ctx.setDict(body.type, body.code, body.name);

    body.id = params.id;
    return body;
  }

  async deleteItem(id) {
    this.ctx.assert(/^\d+$/.test(id), 'invalid id');

    // 读取数据
    let db = this.ctx.db();
    let sql = 'SELECT type, code, ' +
      '(SELECT 1 FROM cms_sys_dict_item AS child WHERE child.type=base.type AND child.parent=base.code LIMIT 1) AS hasChild ' +
      'FROM cms_sys_dict_item AS base ' +
      'WHERE base.id=' + id;
    let [item] = await db.query(sql, {type: 'SELECT'});

    this.ctx.assert(item, '记录不存在');
    this.ctx.assert(!item.hasChild, '请先清空子集');

    // 执行删除
    sql = 'DELETE FROM cms_sys_dict_item WHERE id=' + id;
    await db.query(sql, {type: 'DELETE'});
    await this.ctx.setDict(item.type, item.code);

    return '已删除';
  }

  async refreshCache() {
    const {redis} = this.ctx;
    const limit = 1000;
    let sql, rows, task, offset = 0;

    do {
      sql = `SELECT type, code, name FROM cms_sys_dict_item ORDER BY id LIMIT ${offset}, ${limit}`;
      rows = await this.ctx.db().query(sql, {type: 'SELECT'});
      task = rows.map(item => {
        return this.ctx.setDict(item.type, item.code, item.name);
      });
      await Promise.all(task);
      offset += limit;
    } while (rows.length == limit);
  }
}
