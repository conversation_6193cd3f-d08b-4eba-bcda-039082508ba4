"use strict";

const moment = require("moment");

/**
 * 企业员工
 */
module.exports = class extends Logic {
  async search(q) {
    this.ctx.assert(/^\d+$/.test(q.company_id), "invalid company_id");

    q.offset = /^\d+$/.test(q.offset) ? parseInt(q.offset) : 0;
    q.limit = /^\d+$/.test(q.limit) ? parseInt(q.limit) : 100;

    return this.ctx.model("company.staff").search(q);
  }

  async query(q) {
    this.ctx.assert(/^\d+$/.test(q.company_id), "invalid company_id");

    const model = await this.ctx.model("company.staff").query(q);
    const scope = this;

    return {
      async count() {
        return model.count();
      },
      async rows() {
        let rows = await model.rows(q.offset, q.limit);

        for (let i = 0, len = rows.length, item; i < len; i++) {
          item = rows[i];
          [item.party_name, item.nation_name, item.education_name] =
            await Promise.all([
              scope.ctx.getDict("politics", item.party_code, "未知"),
              scope.ctx.getDict("nation", item.nation, "未知"),
              scope.ctx.getDict("education", item.education, "未知"),
            ]);
        }

        return rows;
      },
    };
  }

  async getEditData(id) {
    return this.ctx.model("company.staff").getEditData(id);
  }

  /**
   * 添加员工
   */
  async create(params) {
    let user = await this.ctx.validate("staff.create", {
      company_id: params.company_id,
      name: params.name,
      sex: params.sex,
      headimg: params.headimg || "",
      status: params.status,
      sequence: params.sequence,
      school_name: params.school_name || "",
      education: params.education || 0,
      join_work: params.join_work || "0000-00-00",
      nation: params.nation,
      position_name: params.position_name,
      birthday: params.birthday || "0000-00-00",
      tag: Array.isArray(params.tag) ? params.tag : [],
      grade: params.grade,
      promise_image: params.promise_image,
    });

    user = await this.ctx.model("company.staff").create(user);

    if (Array.isArray(params.tag)) {
      await this.setTags(user.id, params.tag);
    }

    return { id: user.id, company_id: user.company_id };
  }

  /**
   * 编辑员工信息
   */
  async update(params) {
    let user = await this.ctx.validate("staff.update", {
      id: params.id,
      company_id: params.company_id,
      name: params.name,
      sex: params.sex,
      headimg: params.headimg || "",
      status: params.status,
      sequence: params.sequence,
      school_name: params.school_name || "",
      education: params.education || 0,
      join_work: params.join_work || "0000-00-00",
      nation: params.nation,
      position_name: params.position_name,
      birthday: params.birthday || "0000-00-00",
      grade: params.grade,
      promise_image: params.promise_image,
    });

    await this.ctx.model("company.staff").update(user.id, user);

    if (Array.isArray(params.tag)) {
      await this.setTags(user.id, params.tag);
    }

    return "已保存";
  }

  /**
   * 给用户打标签
   */
  async setTags(id, list) {
    const model = this.ctx.model("company.staff");
    const dels = await model.getUserTags(id);

    this.ctx.assert(dels, "打标签失败：用户不存在");

    const adds = [];
    const tags = list.map((id, i) => {
      id = parseInt(id);
      this.ctx.assert(id && !isNaN(id), "分组ID错误");

      i = dels.indexOf(id);
      if (i == -1) {
        adds.push(id);
      } else {
        dels.splice(i);
      }

      return id;
    });

    const task = [model.setUserTags(id, tags)];

    if (dels.length > 0) {
      task.push(this.ctx.model("company.staff.tag").adjustPeople(dels, "-1"));
    }

    if (adds.length > 0) {
      task.push(this.ctx.model("company.staff.tag").adjustPeople(adds, "+1"));
    }

    await Promise.all(task);
  }

  /**
   * 设置排序
   */
  async saveSort(list) {
    for (let i = 0, item; i < list.length; ) {
      item = list[i];

      if (!helper.is_id(item.key) || !/^\d+$/.test(item.val)) {
        list.splice(i, 1);
      } else {
        i++;
      }
    }

    if (list.length == 0) return;

    await this.ctx.model("company.staff").saveSort(list);
  }

  /**
   * 更新党员信息
   */
  async updateParty(params) {
    let user = await this.ctx.validate("staff.party", {
      id: params.id,
      party_code: params.party_code,
      party_join: params.party_join || "0000-00-00",
      party_title: params.party_title,
      party_oath: params.party_oath,
      party_intro: params.party_intro,
      dept_id: parseInt(params.dept_id) || 0,
      grade: params.grade,
      promise_image: params.promise_image,
    });

    let model = this.ctx.model("company.staff");

    let old = await model.getBeforeUpdate(user.id);
    this.ctx.assert(old, "数据不存在");

    await model.update(user.id, user);

    if (old.dept_id != user.dept_id) {
      let deptModel = this.ctx.model("company.dept");
      let parents;

      if (old.dept_id) {
        parents = await deptModel.getParents(old.dept_id, true);
        await deptModel.updatePeople(parents, -1);
      }

      if (user.dept_id) {
        parents = await deptModel.getParents(user.dept_id, true);
        await deptModel.updatePeople(parents, 1);
      }
    }

    return "已保存";
  }

  async delete(id) {
    this.ctx.assert(helper.is_id(id), "invalid id");
    const model = this.ctx.model("company.staff");
    let tags = await model.getUserTags(id);
    await model.update(id, { status: 0, tag: "" });
    if (tags.length > 0)
      await this.ctx.model("company.staff.tag").adjustPeople(tags, "-1");
    return "已删除";
  }

  async getUpdateData(companyId, prevTime) {
    let list = await this.ctx
      .model("company.staff")
      .getUpdateData(companyId, prevTime);
    let len = list.length;

    // 格式化数据
    for (let i = 0, item; i < len; i++) {
      item = list[i];

      item.birthday = helper.zhDate(item.birthday);
      item.party_join = helper.zhDate(item.party_join);
      item.join_work = helper.zhDate(item.join_work);
      item.tag =
        item.tag != ""
          ? item.tag.split(",").map((id) => {
              return parseInt(id);
            })
          : [];

      [item.party_name, item.nation, item.education] = await Promise.all([
        this.ctx.getDict("politics", item.party_code, ""),
        this.ctx.getDict("nation", item.nation, ""),
        this.ctx.getDict("education", item.education, ""),
      ]);
    }

    return {
      type: "staff",
      name: "党部成员",
      update: len > 0,
      message: len ? "共" + len + "条，可更新" : "无更新",
      content: list,
    };
  }

  /**
   * 性别统计
   */
  async getSexCount(companyId) {
    let rows = await this.ctx.model("company.staff").getSexCount(companyId);

    let total = 0;
    let columns = [
      { label: "男", value: 0, percent: 50 },
      { label: "女", value: 0, percent: 50 },
    ];

    rows.forEach((item) => {
      total += item.val;

      if (item.key != 2) {
        columns[0].value += item.val;
      } else {
        columns[1].value += item.val;
      }
    });

    total > 0 &&
      columns.forEach((item) => {
        item.percent = helper.decimal((item.value / total) * 100);
      });

    return columns;
  }

  /**
   * 统计学历
   */
  async getEducationCount(companyId) {
    let rows = await this.ctx
      .model("company.staff")
      .getEducationCount(companyId);

    let total = 0;
    let columns = [
      { label: "大专及以下", value: 0, percent: 33.33 },
      { label: "本科", value: 0, percent: 33.33 },
      { label: "硕士及以上", value: 0, percent: 33.33 },
    ];

    rows.forEach((item) => {
      total += item.val;

      if (item.key == 5) {
        columns[1].value += item.val;
      } else if (item.key > 5) {
        columns[2].value += item.val;
      } else {
        columns[0].value += item.val;
      }
    });

    total > 0 &&
      columns.forEach((item) => {
        item.percent = helper.decimal((item.value / total) * 100);
      });

    return columns;
  }

  /**
   * 统计年龄
   */
  async getAgeCount(companyId) {
    let columns = [
      { label: "25岁以下", value: 0, percent: 25 },
      { label: "25岁~35岁", value: 0, percent: 25 },
      { label: "35岁~45岁", value: 0, percent: 25 },
      { label: "45岁以上", value: 0, percent: 25 },
    ];

    let total = 0;
    let now = parseInt(moment().format("x"));
    let ymd = "YYYY-MM-DD";
    let range = [
      [moment(now).add(-24, "year").format(ymd), 0],
      [
        moment(now).add(-35, "year").format(ymd),
        moment(now).add(-24, "year").format(ymd),
      ],
      [
        moment(now).add(-45, "year").format(ymd),
        moment(now).add(-35, "year").format(ymd),
      ],
      [0, moment(now).add(-45, "year").format(ymd)],
    ];

    let rows = await this.ctx
      .model("company.staff")
      .getAgeCount(companyId, range);

    rows.forEach((item, i) => {
      total += item.val;
      columns[item.key].value = item.val;
    });

    total > 0 &&
      columns.forEach((item) => {
        item.percent = helper.decimal((item.value / total) * 100);
      });

    return columns;
  }

  /**
   * 统计党龄
   */
  async getPartyAgeCount(companyId) {
    let columns = [
      { label: "5年以下", value: 0, percent: 20 },
      { label: "5~10年", value: 0, percent: 20 },
      { label: "11~15年", value: 0, percent: 20 },
      { label: "16~20年", value: 0, percent: 20 },
      { label: "20年以上", value: 0, percent: 20 },
    ];

    let total = 0;
    let now = parseInt(moment().format("x"));
    let ymd = "YYYY-MM-DD";
    let date = moment().add(-4, "year");
    let end;

    let range = [
      [moment(now).add(-5, "year").format(ymd), 0],
      [
        moment(now).add(-10, "year").format(ymd),
        moment(now).add(-5, "year").format(ymd),
      ],
      [
        moment(now).add(-15, "year").format(ymd),
        moment(now).add(-11, "year").format(ymd),
      ],
      [
        moment(now).add(-20, "year").format(ymd),
        moment(now).add(-16, "year").format(ymd),
      ],
      [0, moment(now).add(-20, "year").format(ymd)],
    ];

    let rows = await this.ctx
      .model("company.staff")
      .getPartyAgeCount(companyId, range);

    rows.forEach((item, i) => {
      total += item.val;
      columns[item.key].value = item.val;
    });

    total > 0 &&
      columns.forEach((item) => {
        item.percent = helper.decimal((item.value / total) * 100);
      });

    return columns;
  }

  async getUpdateSexCount(companyId) {
    let columns = await this.getSexCount(companyId);
    return {
      type: "sexCount",
      name: "性别统计",
      update: true,
      message: "可更新",
      content: columns,
    };
  }

  async getUpdateAgeCount(companyId) {
    let columns = await this.getAgeCount(companyId);
    return {
      type: "ageCount",
      name: "年龄统计",
      update: true,
      message: "可更新",
      content: columns,
    };
  }

  async getUpdatePartyAgeCount(companyId) {
    let columns = await this.getPartyAgeCount(companyId);
    return {
      type: "partyAgeCount",
      name: "党龄统计",
      update: true,
      message: "可更新",
      content: columns,
    };
  }

  async getUpdateEducationCount(companyId) {
    let columns = await this.getEducationCount(companyId);
    return {
      type: "educationCount",
      name: "学历统计",
      update: true,
      message: "可更新",
      content: columns,
    };
  }
};
