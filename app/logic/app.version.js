"use strict";

module.exports = class extends Logic {

  get model() {
    return this.ctx.model('app.version');
  }

  getList(q) {
    return this.model.getList({
      platform: q.platform,
      owner_id: /^\d+$/.test(q.owner_id) ? q.owner_id : 0
    });
  }

  /**
   * 获取最后一个版本
   */
  async getLastVersion(platform) {
    this.ctx.assert(/^[0]$/.test(platform), 'invalid platform');
    let data = await this.model.getLastVersion(platform);
    data.content = data.content.replace(/\n/g, '<br>');
    data.created = helper.Date(data.created).toString('YYYY年MM月DD日 HH:mm:ss');
    return data;
  }

  /**
   * 检查版本更新
   */
  async checkVersion(platform, code, name) {
    this.ctx.assert(/^[0]$/.test(platform), 'invalid platform');
    let data = await this.getLastVersion(platform);
    data.update = code != data.code || name != data.name;
    return data;
  }
}