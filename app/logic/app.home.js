"use strict";

module.exports = class extends Logic {

  async getByOwnerId(id) {
    this.ctx.assert(helper.is_id(id), 'invalid owner id');
    return this.ctx.model('app.home').getByOwnerId(id);
  }

  async update(params) {
    this.ctx.assert(helper.is_id(params.owner), 'invalid owner');
    // this.ctx.assert(Array.isArray(params.menus) && params.menus.length === 7, 'invalid menus');
    this.ctx.assert(Array.isArray(params.body) && params.body.length > 2, 'invalid body');

    let where = {pid: params.owner};
    let subUsers = await this.ctx.db('user').findAll({attributes: ['username', 'id', 'nickname'], where: where})

    let model = this.ctx.model('app.home');
    let id = await model.exists(params.owner);

    let body = {
      owner: params.owner,
      title: params.title,
      bgimage: params.bgimage,
      bgcolor: params.bgcolor,
      grayscale: params.grayscale,
      theme: params.theme,
      menus: params.menus,
      body: params.body
    };

    if (!id) {
      await model.create(body);
    } else {
      await model.update(id, body);
    }

    // for (let i = 0; i < subUsers.length; i++) {
    //   let subUser = subUsers[i];
    //   let id = await model.exists(subUser.id);
    //   body.owner = subUser.id;
    //   if (!id) {
    //     await model.create(body);
    //   } else {
    //     delete body.title;
    //     await model.update(id, body);
    //   }
    // }
    return '已保存';
  }

  async getUpdateData(ownerId, prevTime) {
    let data = await this.ctx.model('app.home').getUpdateData(ownerId, prevTime);
    let update = !!data;

    return {
      type: 'homePage',
      name: '首页数据',
      update: update,
      message: update ? '需要更新' : '无需更新',
      content: data
    }
  }
}
