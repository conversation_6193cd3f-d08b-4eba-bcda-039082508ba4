// ioc 经济结构详情接口
const Sequelize = require('sequelize');
const Op = Sequelize.Op;

module.exports = class extends Controller {
    async query () {
        let {offset, limit, grid_name} = this.ctx.query;
        let where = {};
        // if (code) {
        //     where.code = code
        // }
        where.type = 1;
        if (grid_name) {
            where.grid_name = {
                [Op.like]: `%${grid_name}%`
            }
        }
        return this.ctx.db("economic").findAndCountAll({
            where: where,
            offset: offset ? Number(offset) : 0,
            limit: limit ? Number(limit) : 10,
            order: [['sort', 'ASC']]
        });
    }
}