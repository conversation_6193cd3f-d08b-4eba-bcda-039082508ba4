"use strict";

/**
 * 企业员工分组管理
 */
module.exports = class extends Controller {

  get logic() {
    return this.ctx.logic('company.staff.tag');
  }

  list() {
    return this.logic.getList(this.ctx.query);
  }

  create() {
    return this.logic.create(this.ctx.request.body);
  }

  update() {
    return this.logic.update(this.ctx.request.body);
  }

  delete() {
    return this.logic.delete(this.ctx.request.body.id);
  }

  sort(){
    return this.logic.saveSort(this.ctx.request.body);
  }
}