/**
 * mini 志愿者申请
 */
const Sequelize = require('sequelize');
const Op = Sequelize.Op;

module.exports = class extends Controller {
    query () {
        let {open_id} = this.ctx.query;
        return this.ctx.db("mini_volunteers").findOne({
            where: {
                open_id: open_id
            }
        });
    }

    async listAll(){

        let {audit} = this.ctx.query;

        if(audit){
            let list1 = await this.ctx.db("mini_volunteers").findAndCountAll({
                order: [['id', 'ASC']],
                where: {audit: audit},

            });
    
            let list2 = await this.ctx.db("mini_volunteers_child").findAndCountAll({
                order: [['id', 'ASC']],
                where: {audit: audit},

            });
    
            let list = list1.rows.concat(list2.rows)
            return list
        }else{
            let list1 = await this.ctx.db("mini_volunteers").findAndCountAll({
                order: [['id', 'ASC']],
            });
    
            let list2 = await this.ctx.db("mini_volunteers_child").findAndCountAll({
                order: [['id', 'ASC']],
            });
    
            let list = list1.rows.concat(list2.rows)
            return list
        }

       
    }

    list () {
        let {audit} = this.ctx.query;

        if(audit){

            return this.ctx.db("mini_volunteers").findAndCountAll({
                order: [['id', 'ASC']],
                where: {audit: audit},
            });

        }else{

            return this.ctx.db("mini_volunteers").findAndCountAll({
                order: [['id', 'ASC']],
            });
        }
    }

    create () {
        let model = this.ctx.request.body;
        return this.ctx.db("mini_volunteers").create(model);
    }

    update () {
        let model = this.ctx.request.body;
        return this.ctx.db("mini_volunteers").update(model, {where: {open_id: model.open_id}});
    }

    delete () {
        let {id} = this.ctx.request.body;
        return this.ctx.db("mini_volunteers").destroy({where: {id: id}});
    }

    audit(){
        let {audit} = this.ctx.request.body;
        let {open_id} = this.ctx.request.body;
        return this.ctx.db("mini_volunteers").update({audit:audit}, {where: {open_id: open_id}});
    }
}
