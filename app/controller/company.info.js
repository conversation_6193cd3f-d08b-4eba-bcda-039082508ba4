"use strict";

/**
 * 企业信息管理
 */
module.exports = class extends Controller {

  get logic() {
    return this.ctx.logic('company.info');
  }

  get() {
    return this.logic.getEditData(this.ctx.query.id);
  }

  profile () {
    let login = this.getLogin();
    let company_id = login.company_id;
    return this.ctx.db("company").findByPk(company_id);
  }

  update() {
    let {body} = this.ctx.request;
    switch (this.ctx.query.action) {
      case 'base':
        return this.logic.updateBase(body.id, body);
      case 'intro':
        return this.logic.updateIntro(body.id, body.intro);
      case 'workReset':
        return this.logic.updateWorkReset(body.id, body);
      case 'contact':
        return this.logic.updateContact(body.id, body);
    }
  }
}
