const City = require(__rootdir + '/extend/city');
const Sequelize = require('sequelize');
const Op = Sequelize.Op;

module.exports = class extends Controller {


    city () {
        return City.children(this.ctx.query.pid);
    }

    /**
     * 终端设备数据更新
     */
    async update () {
        let login = this.getLogin();
        let owner = login.id;
        let companyId = login.company_id;
        let {type, prev, offset, limit} = this.ctx.query;

        prev = /^\d{9,10}$/.test(prev) ? parseInt(prev) : 0;
        offset = /^\d+$/.test(offset) ? parseInt(offset) : 0;
        limit = /^\d+$/.test(limit) ? parseInt(limit) : 100;

        switch (type) {
            case 'mediaNews':
                return this.ctx.logic('media.item', 'news').getUpdateData(owner, prev, offset, limit);
            case 'mediaAudio':
                return this.ctx.logic('media.item', 'audio').getUpdateData(owner, prev, offset, limit);
            case 'mediaVideo':
                return this.ctx.logic('media.item', 'video').getUpdateData(owner, prev, offset, limit);
            case 'mediaMotto':
                return this.ctx.logic('media.item', 'motto').getUpdateData(owner, prev, offset, limit);
            case 'mediaImage':
                return this.ctx.logic('media.item', 'image').getUpdateData(owner, prev, offset, limit);
            case 'mediaDelete':
                return this.ctx.logic('media.item').getUpdateDelete(owner, prev);
            case 'staffCount':
                const staff = this.ctx.logic('company.staff');
                return Promise.all([
                    staff.getUpdateSexCount(companyId, prev),
                    staff.getUpdateAgeCount(companyId, prev),
                    staff.getUpdatePartyAgeCount(companyId, prev),
                    staff.getUpdateEducationCount(companyId, prev)
                ]);
            default:
                return Promise.all([
                    this.ctx.logic('company.info').getUpdateData(companyId, prev),
                    this.ctx.logic('company.staff.tag').getUpdateData(companyId, prev),
                    this.ctx.logic('company.staff').getUpdateData(companyId, prev),
                    this.ctx.logic('app.home').getUpdateData(owner, prev),
                    this.ctx.logic('media.channel').getUpdateData(owner, prev),
                ]);
        }
    }

    /**
     * 终端设备数据更新
     */
    async updateFromParent () {
        let login = this.getLogin();
        let owner = login.id;
        //本人id
        let _owner = login.id;
        let companyId = login.company_id;
        let {type, prev, offset, limit} = this.ctx.query;

        prev = /^\d{9,10}$/.test(prev) ? parseInt(prev) : 0;
        offset = /^\d+$/.test(offset) ? parseInt(offset) : 0;
        limit = /^\d+$/.test(limit) ? parseInt(limit) : 100;
        let ownerObj = await this.ctx.db('user').findByPk(owner);
        if (ownerObj && ownerObj.pid > 0 && ownerObj.dataValues.home_datasource === 1) {
            owner = ownerObj.dataValues.pid
            let pOwnerObj = await this.ctx.db('user').findByPk(owner);
            companyId = pOwnerObj.dataValues.company_id
        }
        switch (type) {
            case 'mediaNews':
                return this.ctx.logic('media.item', 'news').getUpdateData(owner, _owner, prev, offset, limit);
            case 'mediaAudio':
                return this.ctx.logic('media.item', 'audio').getUpdateData(owner, _owner, prev, offset, limit);
            case 'mediaVideo':
                return this.ctx.logic('media.item', 'video').getUpdateData(owner, _owner, prev, offset, limit);
            case 'mediaMotto':
                return this.ctx.logic('media.item', 'motto').getUpdateData(owner, _owner, prev, offset, limit);
            case 'mediaImage':
                return this.ctx.logic('media.item', 'image').getUpdateData(owner, _owner, prev, offset, limit);
            case 'mediaDelete':
                return this.ctx.logic('media.item').getUpdateDelete(owner, _owner, prev);
            case 'staffCount':
                const staff = this.ctx.logic('company.staff');
                return Promise.all([
                    staff.getUpdateSexCount(companyId, prev),
                    staff.getUpdateAgeCount(companyId, prev),
                    staff.getUpdatePartyAgeCount(companyId, prev),
                    staff.getUpdateEducationCount(companyId, prev)
                ]);
            default:
                return Promise.all([
                    this.ctx.logic('company.info').getUpdateData(companyId, prev),
                    this.ctx.logic('company.staff.tag').getUpdateData(companyId, prev),
                    this.ctx.logic('company.staff').getUpdateData(companyId, prev),
                    this.ctx.logic('app.home').getUpdateData(_owner, prev),
                    this.ctx.logic('media.channel').getUpdateData(_owner, prev),
                ]);
        }
    }

    /**
     * 终端设备数据获取
     */
    async getDataByType () {
        let {type, channelId, offset, limit} = this.ctx.query;
        offset = /^\d+$/.test(offset) ? parseInt(offset) : 0;
        limit = /^\d+$/.test(limit) ? parseInt(limit) : 100;
        let ret = {};
        switch (type) {
            case 'channel':
                let channel = await this.ctx.db("media_channel").findByPk(channelId);
                //路径，返回下级
                if (channel.type === 0) {
                    let subChannel = await this.ctx.db("media_channel")
                        .findAll({
                            attributes: ['id', 'name'],
                            where:
                                {
                                    pid: channel.id
                                }
                        });
                    ret.dataType = 'channel';
                    ret.data = subChannel;
                }
                if (channel.type === 1) {
                    let medias = await this.ctx.db("media_column").findAndCountAll({
                        where: {cid: channel.id},
                        offset: offset,
                        limit: limit
                    });
                    ret.dataType = 'media';
                    ret.data = medias;
                }
                break;
            case 'dept':
                // let login = this.getLogin();
                // let owner = login.id;
                let owner = 10004077;
                // let companyId = login.company_id;
                let companyId = 10047;
                let ownerObj = await this.ctx.db('user').findByPk(owner);
                if (ownerObj && ownerObj.pid > 0 && ownerObj.dataValues.home_datasource === 1) {
                    owner = ownerObj.dataValues.pid
                    let pOwnerObj = await this.ctx.db('user').findByPk(owner);
                    companyId = pOwnerObj.dataValues.company_id
                }
                let companyStaff = await this.ctx.db("company_staff").findAll(
                    {
                        where: {company_id: companyId},
                    });

                let companyTag = await this.ctx.db("company_staff_tag").findAll(
                    {
                        where: {company_id: companyId},
                        order: [['sort', 'ASC']]
                    });
                companyTag.map(t => {
                    if (t.people > 0) {
                        t.dataValues.staff = companyStaff.filter(s => parseInt(s.tag) === t.id)
                    } else {
                        t.dataValues.staff = [];
                    }
                    return t;
                })
                //路径，返回下级
                ret.dataType = 'dept';
                ret.data = companyTag;
                break;
            default:
                break;
        }
        return ret;
    }

    /**
     * 终端设备获取所有频道
     */
    async getChannelData () {
        let login = this.getLogin();
        let owner = login.id;
        let {prev} = this.ctx.query;
        prev = /^\d{9,10}$/.test(prev) ? parseInt(prev) : 0;
        return Promise.all([
            this.ctx.logic('media.channel').getUpdateData(owner, prev),
        ]);
    }

    /**
     * 终端设备获取指定频道所有资讯
     */
    async getMediaDataByChannel () {
        let login = this.getLogin();
        let owner = login.id;
        //本人id
        let _owner = login.id;
        let {offset, limit, channelId} = this.ctx.query;
        limit = /^\d+$/.test(limit) ? parseInt(limit) : 100;
        let ownerObj = await this.ctx.db('user').findByPk(owner);
        if (ownerObj && ownerObj.pid > 0 && ownerObj.dataValues.home_datasource === 1) {
            owner = ownerObj.dataValues.pid
        }
        return Promise.all([
            this.ctx.logic('media.item', 'news').getUpdateDataByChannel(owner, _owner, offset, limit, channelId),
            this.ctx.logic('media.item', 'audio').getUpdateDataByChannel(owner, _owner, offset, limit, channelId),
            this.ctx.logic('media.item', 'video').getUpdateDataByChannel(owner, _owner, offset, limit, channelId),
            this.ctx.logic('media.item', 'motto').getUpdateDataByChannel(owner, _owner, offset, limit, channelId),
            this.ctx.logic('media.item', 'image').getUpdateDataByChannel(owner, _owner, offset, limit, channelId),
        ])
    }
}
