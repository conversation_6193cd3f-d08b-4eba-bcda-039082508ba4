/**
 * 首页轮播图
 */
const Sequelize = require('sequelize')
const Op = Sequelize.Op
const xlsx = require('node-xlsx')

module.exports = class extends Controller {
  async query() {
    let { offset, limit, grid, house, name, gender, id_card, political, user_tag } = this.ctx.query
    let where = {}
    if (grid) {
      where.grid_code = grid
    }
    if (house) {
      where.house_code = house
    }
    if (gender) {
      where.gender = gender
    }
    if (political) {
      where.political = political
    }
    if (id_card) {
      where.id_card = {
        [Op.like]: `%${id_card}%`,
      }
    }
    if (name) {
      where.name = {
        [Op.like]: `%${name}%`,
      }
    }
    if (user_tag && Array.isArray(user_tag.split(','))) {
      user_tag = user_tag.split(',')
      let orArr = []
      //特殊人群
      if (user_tag.includes('1')) {
        orArr.push({ special_group: { [Op.not]: `` } })
      }
      //小区名人
      if (user_tag.includes('2')) {
        orArr.push({ famous_type: { [Op.not]: `` } })
      }
      //军人
      if (user_tag.includes('3')) {
        orArr.push({ soldier_type: { [Op.not]: `` } })
      }
      //重点监控
      if (user_tag.includes('4')) {
        orArr.push({ focus_type: { [Op.not]: `` } })
      }
      let orObj = {
        [Op.or]: orArr,
      }
      where = { ...orObj }
    }

    return this.ctx.db('ioc_user').findAndCountAll({
      where: where,
      offset: offset ? Number(offset) : 0,
      limit: limit ? Number(limit) : 10,
    })
  }

  create() {
    let model = this.ctx.request.body
    model.special_group = model.special_group.join('|')
    model.famous_type = model.famous_type.join('|')
    model.soldier_type = model.soldier_type.join('|')
    model.focus_type = model.focus_type.join('|')
    return this.ctx.db('ioc_user').create(model)
  }

  update() {
    let model = this.ctx.request.body
    model.special_group = model.special_group.join('|')
    model.famous_type = model.famous_type.join('|')
    model.soldier_type = model.soldier_type.join('|')
    model.focus_type = model.focus_type.join('|')
    return this.ctx.db('ioc_user').update(model, { where: { id: model.id } })
  }

  delete() {
    let { id } = this.ctx.request.body
    return this.ctx.db('ioc_user').destroy({ where: { id: id } })
  }

  async get() {
    let { id } = this.ctx.query
    let iocUser = await this.ctx.db('ioc_user').findByPk(id)
    if (iocUser) {
      iocUser.special_group = iocUser.special_group.split('|')
      iocUser.famous_type = iocUser.famous_type.split('|')
      iocUser.soldier_type = iocUser.soldier_type.split('|')
      iocUser.focus_type = iocUser.focus_type.split('|')
    }
    return iocUser
  }

  async statisticalData() {
    let { type, code } = this.ctx.query
    let where = {}
    switch (type) {
      case '1':
        break
      case '2':
        where.grid_code = code
        break
      case '3':
        where.house_code = code
        break
    }

    let users = await this.ctx.db('ioc_user').findAll({
      attributes: ['family_relation', 'domicile_type', 'domicile_from', 'political', 'special_group', 'famous_type', 'soldier_type', 'focus_type', 'age', 'gender'],
      where: where,
    })

    let gridNum = 0
    let xiaoqu = 0
    let waiLaiRenKou = 0
    let huJiRenKou = 0
    let liuDongRenKou = 0
    let changZhuRenKou = 0
    let qunZhong = 0
    let zhongGongDangYuan = 0
    let minZhuDangPai = 0
    let nan = 0
    let nv = 0
    let tuiYiJunRen = 0
    let yuBeiYi = 0
    let youFuDuiXiang = 0
    let xianYi = 0
    let xingManShiFang = 0
    let wenKongRenYuan = 0
    let xiDuRenYuan = 0
    let jingShenBing = 0
    let sheQuJiaoZheng = 0
    let diBao = 0
    let canJi = 0
    let kuJingErTong = 0
    let shiDuJiaTing = 0
    let kongChaoLaoRen = 0
    let wenHuaMingLiu = 0
    let zhiFuDaiTouRen = 0
    let zhiYeNengRen = 0
    let householder = 0
    let nianLing0_3 = 0
    let nianLing4_6 = 0
    let nianLing7_12 = 0
    let nianLing12_18 = 0
    let nianLing19_59 = 0
    let nianLing60_80 = 0
    let nianLing80 = 0
    let teenager = 0
    let teenager_percent = 0
    let middle_aged = 0
    let middle_aged_percent = 0
    let old_aged = 0
    let old_aged_percent = 0
    let xiaoxue = 0
    let chuzhong = 0
    let gaozhong = 0
    let daxue = 0

    users.forEach((item, index) => {
      if (item.family_relation === '户主') {
        householder++
      }
      if (item.domicile_type === '常住') {
        changZhuRenKou++
      }
      if (item.domicile_type === '流动人口') {
        liuDongRenKou++
      }
      if (item.domicile_from === '户籍') {
        huJiRenKou++
      }
      if (item.domicile_from === '外来') {
        waiLaiRenKou++
      }
      if (item.political === '中共党员') {
        zhongGongDangYuan++
      }
      if (item.political === '民主党派') {
        minZhuDangPai++
      }
      if (item.political === '群众') {
        qunZhong++
      }
      if (item.gender === '男') {
        nan++
      }
      if (item.gender === '女') {
        nv++
      }
      let soldierArr = item.soldier_type.split('|')
      if (soldierArr.includes('退役军人')) {
        tuiYiJunRen++
      }
      if (soldierArr.includes('优抚对象')) {
        youFuDuiXiang++
      }
      if (soldierArr.includes('预备役')) {
        yuBeiYi++
      }
      if (soldierArr.includes('现役')) {
        xianYi++
      }
      let focus_type = item.focus_type
      if (focus_type.indexOf('稳控') > -1) {
        wenKongRenYuan++
      }
      if (focus_type.indexOf('刑满') > -1) {
        xingManShiFang++
      }
      if (focus_type.indexOf('矫正') > -1) {
        sheQuJiaoZheng++
      }
      if (focus_type.indexOf('吸毒') > -1) {
        xiDuRenYuan++
      }
      if (item.special_group.indexOf('低保') > -1) {
        diBao++
      }
      if (item.special_group.indexOf('级') > -1) {
        canJi++
      }
      if (item.special_group.indexOf('困境') > -1) {
        kuJingErTong++
      }
      if (item.special_group.indexOf('失独') > -1) {
        shiDuJiaTing++
      }
      if (item.special_group.indexOf('精神') > -1) {
        jingShenBing++
      }
      if (item.special_group.indexOf('空巢') > -1) {
        kongChaoLaoRen++
      }
      let famousArr = item.famous_type.split('|')
      if (famousArr.includes('职业能人')) {
        zhiYeNengRen++
      }
      if (famousArr.includes('文化名流')) {
        wenHuaMingLiu++
      }
      if (famousArr.includes('致富带头人')) {
        zhiFuDaiTouRen++
      }
      let age = item.age
      if (age > 0 && age <= 3) {
        nianLing0_3++
      }
      if (age > 3 && age <= 6) {
        nianLing4_6++
      }
      if (age > 6 && age <= 12) {
        nianLing7_12++
      }
      if (age > 12 && age <= 18) {
        nianLing12_18++
      }
      if (age > 18 && age <= 59) {
        nianLing19_59++
      }
      if (age > 59 && age <= 80) {
        nianLing60_80++
      }
      if (age > 80) {
        nianLing80++
      }
      if (age > 6 && age <= 12) {
        xiaoxue++
      }
      if (age > 12 && age <= 16) {
        chuzhong++
      }
      if (age > 16 && age <= 20) {
        gaozhong++
      }
      if (age > 20 && age <= 24) {
        daxue++
      }
    })
    teenager = nianLing0_3 + nianLing4_6 + nianLing7_12 + nianLing12_18
    teenager_percent = this.getPercent(teenager, users.length)
    middle_aged = nianLing19_59
    middle_aged_percent = this.getPercent(middle_aged, users.length)
    old_aged = nianLing60_80 + nianLing80
    old_aged_percent = this.getPercent(old_aged, users.length)
    let ret = {
      householder: householder,
      user_sum: users.length,
      domicile_from: [
        { name: '外来人口', num: waiLaiRenKou },
        { name: '户籍人口', num: huJiRenKou },
      ],
      domicile_type: [
        { name: '流动人口', num: liuDongRenKou },
        { name: '常住人口', num: changZhuRenKou },
      ],
      gender: [
        { name: '男', num: nan },
        { name: '女', num: nv },
      ],
      political: [
        { name: '群众', value: qunZhong },
        { name: '中共党员', value: zhongGongDangYuan },
        { name: '民主党派', value: minZhuDangPai },
      ],
      special_group: [
        { name: '低保', num: diBao },
        { name: '残疾', num: canJi },
        { name: '空巢老人', num: kongChaoLaoRen },
        { name: '困境儿童', num: kuJingErTong },
        { name: '失独家庭', num: shiDuJiaTing },
        { name: '精神病', num: jingShenBing },
      ],
      focus_type: [
        { name: '刑满释放', num: xingManShiFang },
        { name: '吸毒人员', num: xiDuRenYuan },
        { name: '社区矫正', num: sheQuJiaoZheng },
        { name: '稳控人员', num: wenKongRenYuan },
      ],
      soldier_type: [
        { name: '退役军人', value: tuiYiJunRen },
        { name: '预备役', value: yuBeiYi },
        { name: '优抚对象', value: youFuDuiXiang },
        { name: '现役', value: xianYi },
      ],
      famous_type: [
        { name: '致富带头人', num: zhiFuDaiTouRen },
        { name: '文化名流', num: wenHuaMingLiu },
        { name: '职业能人', num: zhiYeNengRen },
      ],
      teenager: teenager,
      middle_aged: middle_aged,
      old_aged: old_aged,
      teenager_percent: teenager_percent,
      middle_aged_percent: middle_aged_percent,
      old_aged_percent: old_aged_percent,
      age: [
        { name: '0-3', value: nianLing0_3 },
        { name: '4-6', value: nianLing4_6 },
        { name: '7-12', value: nianLing7_12 },
        { name: '13-18', value: nianLing12_18 },
        { name: '19-59', value: nianLing19_59 },
        { name: '60-80', value: nianLing60_80 },
        { name: '80以上', value: nianLing80 },
      ],
      student: [
        { name: '小学', value: xiaoxue },
        { name: '初中', value: chuzhong },
        { name: '高中', value: gaozhong },
        { name: '大学', value: daxue },
      ],
    }

    if (code) {
      if (type === '3') {
        let region = await this.ctx.db('region').findOne({
          attributes: ['building', 'unit'],
          where: { code: code },
        })
        ret.building = region.building
        ret.unit = region.unit
      }
      let region = await this.ctx.db('region').findOne({
        attributes: ['name', 'remark'],
        where: { code: code },
      })
      ret.region_name = region.name
      ret.vr_url = region.remark

      let subRegion = await this.ctx.db('region').count({
        where: { parent_code: code },
      })
      ret.xiaoqu = subRegion
    } else {
      let subRegion = await this.ctx.db('region').count({
        where: { type: 1 },
      })
      ret.xiaoqu = subRegion
    }

    if (type === '1') {
      let gridCount = await this.ctx.db('region').count({
        where: { type: 0 },
      })
      ret.gridNum = gridCount
    }
    if (type === '2') {
      let gridCount = await this.ctx.db('region').count({
        where: { code: code },
      })
      ret.gridNum = gridCount
    }
    return ret
  }

  async specialUserData() {
    let { type, code } = this.ctx.query
    let where = { special_group: { [Op.not]: `` } }
    switch (type) {
      case '1':
        break
      case '2':
        where.grid_code = code
        break
      case '3':
        where.house_code = code
        break
    }

    let users = await this.ctx.db('ioc_user').findAll({
      attributes: ['name', 'house_name', 'domicile_from', 'political', 'special_group', 'famous_type', 'soldier_type', 'focus_type', 'age', 'gender'],
      where: where,
    })

    let diBao = []
    let canJi = []
    users.forEach((item, index) => {
      if (item.special_group.indexOf('低保') > -1) {
        diBao.push(item)
      }
      if (item.special_group.indexOf('级') > -1) {
        canJi.push(item)
      }
    })

    let ret = {
      diBao: diBao,
      canJi: canJi,
    }
    return ret
  }

  async famousUserData() {
    let { type, code } = this.ctx.query
    let where = { famous_type: { [Op.not]: `` } }
    switch (type) {
      case '1':
        break
      case '2':
        where.grid_code = code
        break
      case '3':
        where.house_code = code
        break
    }

    let users = await this.ctx.db('ioc_user').findAll({
      attributes: ['name', 'house_name', 'domicile_from', 'political', 'special_group', 'famous_type', 'soldier_type', 'focus_type', 'age', 'gender', 'phone', 'remark'],
      where: where,
    })

    let zhiYeNengRen = []
    let wenHuaMingLiu = []
    let zhiFuDaiTouRen = []
    users.forEach((item, index) => {
      let famousArr = item.famous_type.split('|')
      if (famousArr.includes('职业能人')) {
        zhiYeNengRen.push(item)
      }
      if (famousArr.includes('文化名流')) {
        wenHuaMingLiu.push(item)
      }
      if (famousArr.includes('致富带头人')) {
        zhiFuDaiTouRen.push(item)
      }
    })

    let ret = {
      zhiYeNengRen: zhiYeNengRen,
      wenHuaMingLiu: wenHuaMingLiu,
      zhiFuDaiTouRen: zhiFuDaiTouRen,
    }
    return ret
  }

  async focusUserData() {
    let { type, code } = this.ctx.query
    let where = { focus_type: { [Op.not]: `` } }
    switch (type) {
      case '1':
        break
      case '2':
        where.grid_code = code
        break
      case '3':
        where.house_code = code
        break
    }

    let users = await this.ctx.db('ioc_user').findAll({
      attributes: ['name', 'house_name', 'domicile_from', 'political', 'special_group', 'famous_type', 'soldier_type', 'focus_type', 'age', 'gender'],
      where: where,
    })

    // let jingShenBing = [];
    let xingManShiFang = []
    let sheQuJiaoZheng = []
    let xiDuRenYuan = []
    let wenKongRenYuan = []
    users.forEach((item, index) => {
      let focus_type = item.focus_type
      // if (focus_type.indexOf('精神') > -1) {
      //     jingShenBing.push(item);
      // }
      if (focus_type.indexOf('刑满') > -1) {
        xingManShiFang.push(item)
      }
      if (focus_type.indexOf('矫正') > -1) {
        sheQuJiaoZheng.push(item)
      }
      if (focus_type.indexOf('吸毒') > -1) {
        xiDuRenYuan.push(item)
      }
      if (focus_type.indexOf('稳控') > -1) {
        wenKongRenYuan.push(item)
      }
    })

    let ret = {
      // jingShenBing: jingShenBing,
      xingManShiFang: xingManShiFang,
      sheQuJiaoZheng: sheQuJiaoZheng,
      xiDuRenYuan: xiDuRenYuan,
      wenKongRenYuan: wenKongRenYuan,
    }
    return ret
  }

  // 批量添加
  async batchInsert() {
    let { file } = this.ctx.request.files
    let dataArr = []
    let created = helper.time()
    let workbook = xlsx.parse(file.path)
    let empty = ''
    let regionMap = await this.ctx.db('region').findAll({
      attributes: ['name', 'code'],
    })
    workbook.forEach((item, index) => {
      item.data.forEach((item, index) => {
        if (index !== 0) {
          let specialGroupArr = []
          let famousTypeArr = []
          let soldierTypeArr = []
          let focusTypeArr = []
          let id_card = item[4]

          if (id_card.length === 18) {
            let m = id_card.substring(10, 12)
            let d = id_card.substring(12, 14)
            if (m < 13 && d < 32) {
              if (item[12]) {
                specialGroupArr.push(item[12])
              }
              if (item[13]) {
                specialGroupArr.push(item[13])
              }
              if (item[14]) {
                specialGroupArr.push(item[14])
              }
              if (item[15]) {
                specialGroupArr.push(item[15])
              }
              if (item[16]) {
                specialGroupArr.push(item[16])
              }
              if (item[17]) {
                specialGroupArr.push(item[17])
              }
              if (item[18]) {
                specialGroupArr.push(item[18])
              }
              if (item[19]) {
                specialGroupArr.push(item[19])
              }
              if (item[20]) {
                famousTypeArr.push(item[20])
              }
              if (item[21]) {
                famousTypeArr.push(item[21])
              }
              if (item[22]) {
                soldierTypeArr.push(item[22])
              }
              if (item[23]) {
                soldierTypeArr.push(item[23])
              }
              if (item[24]) {
                soldierTypeArr.push(item[24])
              }
              if (item[25]) {
                soldierTypeArr.push(item[25])
              }
              if (item[26]) {
                focusTypeArr.push(item[26])
              }
              if (item[27]) {
                focusTypeArr.push(item[27])
              }
              if (item[28]) {
                focusTypeArr.push(item[28])
              }
              if (item[29]) {
                focusTypeArr.push(item[29])
              }
              let model = {
                name: item[0] || empty,
                family_relation: item[1] || empty,
                nation: item[2] || empty,
                gender: item[3] || empty,
                id_card: item[4] || empty,
                age: this.idCard(item[4], 3),
                birthday: this.idCard(item[4], 1),
                domicile: item[5] || empty,
                address: item[6] || empty,
                room: item[7] || empty,
                domicile_type: item[8] || empty,
                domicile_from: item[9] || empty,
                grid_name: item[10] || empty,
                grid_code:
                  regionMap
                    .filter((i) => i.name === item[10])
                    .map((ele) => {
                      return ele.code
                    })[0] || empty,
                house_name: item[5] || empty,
                house_code:
                  regionMap
                    .filter((i) => i.name === item[5])
                    .map((ele) => {
                      return ele.code
                    })[0] || empty,
                political: item[11] || empty,
                special_group: specialGroupArr.join('|'),
                famous_type: famousTypeArr.join('|'),
                soldier_type: soldierTypeArr.join('|'),
                focus_type: focusTypeArr.join('|'),
                created: created,
              }
              dataArr.push(model)
            }
          }
        }
      })
    })
    await this.ctx.db().getQueryInterface().bulkInsert('ioc_user', dataArr)
    return '已创建'
  }

  idCard(UUserCard, num) {
    //获取出生日期
    if (num === 1) {
      return UUserCard.substring(6, 10) + '-' + UUserCard.substring(10, 12) + '-' + UUserCard.substring(12, 14)
    }
    //获取性别
    if (num === 2) {
      if (parseInt(UUserCard.substr(16, 1)) % 2 === 1) {
        return '男'
      } else {
        return '女'
      }
    }
    //获取年龄
    if (num === 3) {
      let myDate = new Date()
      let month = myDate.getMonth() + 1
      let day = myDate.getDate()
      let age = myDate.getFullYear() - UUserCard.substring(6, 10) - 1
      if (UUserCard.substring(10, 12) < month || (UUserCard.substring(10, 12) == month && UUserCard.substring(12, 14) <= day)) {
        age++
      }
      return age
    }
  }

  //百分比计算
  getPercent(num, total) {
    num = parseFloat(num)
    total = parseFloat(total)
    if (isNaN(num) || isNaN(total)) {
      return 0
    }
    return total <= 0 ? '0%' : Math.round((num / total) * 1000) / 10.0 + '%'
  }
}
