/**
 * 数据字典
 */
module.exports = class extends Controller {

  get logic() {
    return this.ctx.logic('sys.dictionary');
  }

  types() {
    return this.logic.getTypes(this.ctx.query);
  }

  items() {
    return this.logic.getItems(this.ctx.query.type);
  }

  create() {
    let method = this.ctx.query.action == 'type' ? 'createType' : 'createItem';
    return this.logic[method](this.ctx.request.body);
  }

  update() {
    let method = this.ctx.query.action == 'type' ? 'updateType' : 'updateItem';
    return this.logic[method](this.ctx.request.body);
  }

  delete() {
    let method = this.ctx.query.action == 'type' ? 'deleteType' : 'deleteItem';
    return this.logic[method](this.ctx.request.body.id);
  }

  async refresh() {
    return this.logic.refreshCache();
  }
}