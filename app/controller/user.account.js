"use strict";
/**
 * 仅处理用户登录状态
 */
module.exports = class extends Controller {

    /**
     * 登录
     */
    login () {
        const {body} = this.ctx.request;
        const login = this.ctx.logic('user.login');

        switch (body.by) {
            case 'username':
            case 'password':
                return login.byPassword(body);
            case 'mobile':
                return login.byMobile(body);
            case 'wxmp':
                return login.byWxPub(body);
            case 'wxop':
                return login.byWxApp(body);
            case 'wxpro':
                return login.byWxPro(body);
            case 'apple':
                return login.byApple(body);
        }

        this.ctx.throw('不支持的登录方式');
    }

    /**
     * 注册
     */
    register () {
        const {body} = this.ctx.request;
        const register = this.ctx.logic('user.register');

        switch (body.by) {
            case 'mobile':
                return register.byMobile(body);
            case 'username':
                return register.byUsername(body);
            case 'email':
                return register.byEmail(body);
            case 'company':
                return register.byCompany(body);
        }

        this.ctx.throw('不支持的注册方式');
    }

    /**
     * 验证码
     */
    captcha () {
        const {type, number} = this.ctx.request.body;

        switch (type) {
            case 'mobile login':
                return this.ctx.logic('user.login').sendSmsCaptcha(number);
            case 'mobile register':
                return this.ctx.logic('user.register').sendSmsCaptcha(number);
        }
    }

    /**
     * 注销
     */
    logout () {

    }

    /**
     * 检查登录信息
     */
    async check () {
        let info, {user} = this.ctx.token;

        if (user && user.id) {
            info = await this.ctx.logic('user.account').getLoginUserById(user.id);
        }

        if (!info) {
            delete this.ctx.token.user;
            return {status: -1};
        }

        return this.ctx.logic('user.account').loginSuccess(info);
    }

    /**
     * 修改登录密码
     */
    async password () {
        let {id} = this.getLogin();
        let {oldpwd, password} = this.ctx.request.body;
        return this.ctx.logic('user.info').modifyPassword(id, oldpwd, password);
    }

    /**
     * 修改头像
     */
    async headimg () {
        let {id} = this.getLogin();
        return this.ctx.logic('user.info').setHeadimg(id, this.ctx.request.body.url);
    }

    /**
     * 用户列表
     */
    async query () {
        let api = await this.ctx.logic('user.info').getQuery(this.ctx.query);
        return {
            total: await api.count(),
            rows: await api.rows()
        }
    }

    /**
     * 可分配用户列表
     */
    async unassign () {
        return this.ctx.logic('user.info').unassignList(this.ctx.query);
    }

    /**
     * 审核
     */
    async verify () {
        let {user_id, audit_status} = this.ctx.request.body;
        return this.ctx.logic('user.info').userVerify(user_id, audit_status);
    }

    /**
     * 分层管理树型数据
     */
    async treeData () {
        return this.ctx.logic('user.info').treeData(this.ctx.query);
    }

    /**
     * 分层管理树型数据
     */
    async assignSub () {
        let {pid, subIdArr} = this.ctx.request.body;
        return this.ctx.logic('user.info').assignSub(pid, subIdArr);
    }

    /**
     * 获取直属下级用户
     */
    async subUsers () {
        let {id} = this.getLogin();
        let {nickname} = this.ctx.query;
        return this.ctx.logic('user.info').subUsers(id, nickname);
    }

    /**
     * 是否有下级用户
     */
    async isParentNode () {
        let {id} = this.getLogin();
        let ret = await this.ctx.logic('user.info').subUsers(id);
        return {
            isParentNode: ret.length > 0
        }
    }

    /**
     * 修改用户
     */
    async update () {
        const {body} = this.ctx.request;
        return this.ctx.logic('user.info').updateUser(body);
    }

    /**
     * 删除用户
     */
    async delete () {
        let {id} = this.ctx.request.body;
        await this.ctx.db().transaction(async (t) => {
            await this.ctx.db('user').destroy({where: {id: id}}, {transaction: t});
            await this.ctx.db('cms_user_account').destroy({where: {uid: id}}, {transaction: t});
        });
        return "删除成功";
    }
}
