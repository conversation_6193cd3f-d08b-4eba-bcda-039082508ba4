/**
 * 首页轮播图
 */
const Sequelize = require('sequelize');
const Op = Sequelize.Op;

module.exports = class extends Controller {
    async query () {
        let {offset, limit, code, name, type} = this.ctx.query;
        let where = {};
        if (code) {
            where.code = code
        }
        if (name) {
            where.name = {
                [Op.like]: `%${name}%`
            }
        }
        if (type) {
            where.type = type
        }
        return this.ctx.db("region").findAndCountAll({
            where: where,
            offset: offset ? Number(offset) : 0,
            limit: limit ? Number(limit) : 10
        });
    }

    create () {
        let model = this.ctx.request.body;
        return this.ctx.db("region").create(model);
    }

    update () {
        let model = this.ctx.request.body;
        return this.ctx.db("region").update(model, {where: {id: model.id}});
    }

    delete () {
        let {id} = this.ctx.request.body;
        return this.ctx.db("region").destroy({where: {id: id}});
    }

    get () {
        let {id} = this.ctx.query;
        return this.ctx.db("region").findByPk(id);
    }

    list () {
        let where = {status: "1"};
        return this.ctx.db("region").findAll({
            // where: where,
            order: [['sort', 'ASC']]
        })
    }

    select () {
        let {code} = this.ctx.query;
        let where = {parent_code: code};
        return this.ctx.db("region").findAll({
            where: where,
            order: [['sort', 'ASC']]
        })
    }
}
