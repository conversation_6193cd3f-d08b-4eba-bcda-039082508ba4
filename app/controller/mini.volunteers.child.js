/**
 * mini 志愿者申请
 */
const Sequelize = require('sequelize');
const Op = Sequelize.Op;

module.exports = class extends Controller {
    async query () {
        let {parent_open_id} = this.ctx.query;
        return this.ctx.db("mini_volunteers_child").findOne({
            where: {
                parent_open_id: parent_open_id
            }
        });
    }

    list () {
        let {audit} = this.ctx.query;

        if(audit){

            return this.ctx.db("mini_volunteers_child").findAndCountAll({
                order: [['id', 'ASC']],
                where: {audit: audit},
            });

        }else{

            return this.ctx.db("mini_volunteers_child").findAndCountAll({
                order: [['id', 'ASC']],
            });
        }
        
    }

    create () {
        let model = this.ctx.request.body;
        return this.ctx.db("mini_volunteers_child").create(model);
    }

    update () {
        let model = this.ctx.request.body;
        return this.ctx.db("mini_volunteers_child").update(model, {where: {parent_open_id: model.parent_open_id}});
    }

    delete () {
        let {id} = this.ctx.request.body;
        return this.ctx.db("mini_volunteers_child").destroy({where: {id: id}});
    }

    audit(){
        let {audit} = this.ctx.request.body;
        let {parent_open_id} = this.ctx.request.body;
        return this.ctx.db("mini_volunteers_child").update({audit:audit}, {where: {parent_open_id: parent_open_id}});
    }
}
