"use strict";

/**
 * APP版本
 */
module.exports = class extends Controller {

  get logic() {
    return this.ctx.logic('app.version');
  }

  list() {
    return this.logic.getList(this.ctx.query);
  }

  newest() {
    let {platform, code, name} = this.ctx.query;

    if (code) {
      return this.logic.checkVersion(platform, code, name);
    } else {
      return this.logic.getLastVersion(platform);
    }
  }
}