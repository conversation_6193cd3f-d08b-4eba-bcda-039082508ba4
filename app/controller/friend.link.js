/**
 * 友情链接
 */
module.exports = class extends Controller {

  get logic() {
    return this.ctx.logic('friend.link');
  }

  query() {
    return this.logic.query(this.ctx.query);
  }

  create() {
    return this.logic.create(this.ctx.request.body);
  }

  update() {
    return this.logic.update(this.ctx.request.body);
  }

  delete() {
    return this.logic.delete(this.ctx.request.body.id);
  }

  sort() {
    return this.logic.sort(this.ctx.request.body);
  }
}