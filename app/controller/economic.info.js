const Sequelize = require('sequelize');
const Op = Sequelize.Op;

module.exports = class extends Controller {
    async query () {
        let {offset, limit, company_name, type} = this.ctx.query;
        let where = {};
        // if (code) {
        //     where.code = code
        // }
        if (company_name) {
            where.company_name = {
                [Op.like]: `%${company_name}%`
            }
        }
        if (type) {
            where.type = type
        }
        return this.ctx.db("economic").findAndCountAll({
            where: where,
            offset: offset ? Number(offset) : 0,
            limit: limit ? Number(limit) : 10
        });
    }

    create () {
        let model = this.ctx.request.body;
        return this.ctx.db("economic").create(model);
    }

    update () {
        let model = this.ctx.request.body;
        return this.ctx.db("economic").update(model, {where: {id: model.id}});
    }

    delete () {
        let {id} = this.ctx.request.body;
        return this.ctx.db("economic").destroy({where: {id: id}});
    }

    get () {
        let {id} = this.ctx.query;
        return this.ctx.db("economic").findByPk(id);
    }

    list () {
        let where = {status: "1"};
        return this.ctx.db("economic").findAll({
            // where: where,
            order: [['sort', 'ASC']]
        })
    }

    select () {
        let {code} = this.ctx.query;
        let where = {parent_code: code};
        return this.ctx.db("economic").findAll({
            where: where,
            order: [['sort', 'ASC']]
        })
    }
}