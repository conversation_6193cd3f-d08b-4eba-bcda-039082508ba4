"use strict";

/**
 * 文件上传
 */
module.exports = class extends Controller {

  /**
   * 获取配置
   */
  config() {
    return this.ctx.logic('media.upload').getClientConfig();
  }

  /**
   * 请求上传
   */
  request() {
    let {body} = this.ctx.request;
    let user = this.getLogin(false);
    return this.ctx.logic('media.upload').request(body, user ? user.id : 0);
  }

  /**
   * 上传文件
   */
  submit() {
    let user = this.getLogin(false);
    let {body, files} = this.ctx.request;
    return this.ctx.logic('media.upload').submit(body, files, user ? user.id : 0);
  }

  /**
   * 上传成功
   */
  success() {
    return this.ctx.logic('media.upload').callback(this.ctx.query.type, this.ctx.request.body);
  }

  async images() {
    let user = this.getLogin();
    let logic = await this.ctx.logic('media.upload').getUserImages(user.id, this.ctx.query);

    return {
      count: await logic.count(),
      rows: await logic.rows()
    }
  }
}
