/**
 * 活动及通知
 */
const Sequelize = require('sequelize');
const Op = Sequelize.Op;

module.exports = class extends Controller {
    async list () {
        // let login = this.getLogin();
        // let company_id = login.company_id;
        let {offset, limit} = this.ctx.query;
        offset = /^\d+$/.test(offset) ? parseInt(offset) : 0;
        limit = /^\d+$/.test(limit) ? parseInt(limit) : 100;
        return this.ctx.db("media_activity").findAndCountAll({
            // attributes: {
            //     include: [
            //         [Sequelize.fn('date_format', Sequelize.col('created'), '%Y-%m-%d %H:%i:%s'), 'created'],
            //     ]
            // },
            order: [['id', 'ASC']],
            offset: offset,
            limit: limit
        });
    }


    async listStatus() {
        let { offset, limit } = this.ctx.query;
        offset = /^\d+$/.test(offset) ? parseInt(offset) : 0;
        limit = /^\d+$/.test(limit) ? parseInt(limit) : 100;
      
        // 获取当前时间
        const now = new Date();
      
        // 查询活动数据
        const activities = await this.ctx.db("media_activity").findAndCountAll({
          order: [['id', 'ASC']],
          offset: offset,
          limit: limit
        });
      
        // 处理每条活动数据
        const processedActivities = activities.rows.map(activity => {
          // 解析活动日期时间字符串
          const [startDateTimeStr, endDateTimeStr] = activity.activity_date.split(',');
          const startDateTime = new Date(startDateTimeStr);
          const endDateTime = new Date(endDateTimeStr);
          const deadLine = new Date(activity.dead_line * 1000); // 报名截止日期
      
          // 判断当前时间与活动日期的关系，确定活动状态
          let status;
          if (now < deadLine) {
            status = '报名中'; // 当前时间在报名截止日期之前
          } else if (now >= deadLine && now < startDateTime) {
            status = '报名已截止'; // 当前时间在报名截止日期和开始时间之间
          } else if (now >= startDateTime && now <= endDateTime) {
            status = '活动开展中'; // 当前时间在开始时间和结束时间之间
          } else {
            status = '活动已结束'; // 当前时间晚于结束时间
          }
      
          // 返回处理后的活动数据，包括状态
          return {
            ...activity.dataValues, // 使用dataValues以避免序列化问题
            status: status
          };
        });
      
        // 返回处理后的列表
        return {
          ...activities,
          rows: processedActivities
        };
      }

    create () {
        // let login = this.getLogin();
        // let company_id = login.company_id;
        // model.company_id = company_id;
        let model = this.ctx.request.body;
        // model.created = new Date();
        return this.ctx.db("media_activity").create(model);
    }

    update () {
        // let login = this.getLogin();
        // let company_id = login.company_id;
        let model = this.ctx.request.body;
        return this.ctx.db("media_activity").update(model, {where: {id: model.id}});
    }

    delete () {
        // let login = this.getLogin();
        // let company_id = login.company_id;
        let {id} = this.ctx.request.body;
        return this.ctx.db("media_activity").destroy({where: {id: id}});
    }

    // 报名
    async signUp() {
        const model = this.ctx.request.body;
        const activityId = model.id
        const isSignUp = model.is_sign_up
        const userId = model.open_id
        const activity = await this.ctx.db('media_activity').findByPk(activityId);

        if (!activity) {
            this.ctx.throw(404, '活动不存在');
        }

        // 解析activity_date字段并转换为时间戳
        const  endTimestamp = activity.dead_line * 1000

        const nowTimestamp = new Date().getTime();
        // 检查当前时间是否在活动的开始时间和结束时间之间
        if ( nowTimestamp >= endTimestamp) {
          this.ctx.throw(400, '活动不在报名期间');
        }

        // 检查报名人数是否已达到限制
        const registerNum = activity.register_num;
        const limitNum = activity.people_num; // 假设有一个limit_num字段表示报名人数限制
        if (registerNum >= limitNum && isSignUp) {
            this.ctx.throw(400, '报名人数已满');
        }

        // 检查用户是否已经报名
        const isUserSignedUp = await this.ctx.db('user_activity_sign_up').findOne({
            where: {
                activity_id: activityId,
                open_id: userId
            }
        });

        if (isSignUp) {
            // 用户报名
            if (isUserSignedUp) {
                // 如果用户已经报名，则返回错误
                this.ctx.throw(400, '用户已经报名');
            }
            // 增加报名人数
            await this.ctx.db('media_activity').increment('register_num', {
                by: 1,
                where: { id: activityId }
            });
            // 记录用户的报名状态
            await this.ctx.db('user_activity_sign_up').create({
                activity_id: activityId,
                open_id: userId,
                created: model.created,
                activity_name: model.activity_name,
                user_name: model.people_name
            });
        } else {
            // 用户取消报名
            if (!isUserSignedUp) {
                // 如果用户没有报名，则返回错误
                this.ctx.throw(400, '用户未报名');
            }
            // 减少报名人数
            await this.ctx.db('media_activity').decrement('register_num', {
                by: 1,
                where: { id: activityId }
            });
            // 取消用户的报名状态
            await this.ctx.db('user_activity_sign_up').destroy({
                where: {
                    activity_id: activityId,
                    open_id: userId
                }
            });
        }

        return {
            success: true,
            message: isSignUp ? '报名成功' : '取消报名成功'
        };
    }
   


    userSignUpList(){
        // return this.ctx.db("user_activity_sign_up").findAndCountAll({
        //     order: [['id', 'ASC']],
        // });
        return this.ctx.db("user_activity_sign_up").findAndCountAll({
          attributes: ['activity_id' ,'activity_name' , [Sequelize.fn('COUNT', Sequelize.col('id')), 'sign_up_count']],
          group: ['activity_id','activity_name'],
          order: [['activity_id', 'ASC']],
        });
    }
    async getSignUpsByActivityId() {
      const { activityId } = this.ctx.request.query;
      
      if (!activityId) {
          this.ctx.throw(400, '活动ID不能为空');
      }
  
      return this.ctx.db("user_activity_sign_up").findAndCountAll({
          where: { activity_id: activityId },
          order: [['id', 'ASC']],
          attributes: ['id', 'open_id', 'created', 'activity_name', 'user_name']
      });
  }
    async checkUserSignedUp(){
        const model = this.ctx.request.body;
        const activityId = model.id
        const userId = model.open_id
        // 检查用户是否已经报名
        const isUserSignedUp = await this.ctx.db('user_activity_sign_up').findOne({
            where: {
                activity_id: activityId,
                open_id: userId
            }
        });

        return isUserSignedUp
    }

    async checkIn() {
        const { activityId, userId } = this.ctx.request.body;
        const activity = await this.ctx.db('media_activity').findByPk(activityId);
      
        if (!activity) {
          this.ctx.throw(404, '活动不存在');
        }
        let [startDateTimeStr, endDateTimeStr] = activity.activity_date.split(',');
        let startTimestamp = new Date(startDateTimeStr).getTime();
        let endTimestamp = new Date(endDateTimeStr).getTime();

        // 活动开始前半小时的时间戳
        let gracePeriodEndTimestamp = startTimestamp;

        // 检查当前时间是否在允许签到的时间范围内
        // 只在活动开始前半小时内允许签到
        const nowTimestamp = new Date().getTime();
        if (nowTimestamp < startTimestamp - 0.5 * 60 * 60 * 1000 || nowTimestamp > gracePeriodEndTimestamp) {
          this.ctx.throw(400, '不在签到时间内');
        }
      
        // 检查是否已经签到
        const isAlreadyCheckedIn = await this.ctx.db('user_activity_sign_up').findOne({
          where: {
            activity_id: activityId,
            open_id: userId,
            check_in: { [Op.not]: null }
          }
        });
      
        if (isAlreadyCheckedIn) {
          this.ctx.throw(400, '已经签到');
        }
      

        // 执行签到操作
        await this.ctx.db('user_activity_sign_up').update({
          check_in: new Date().toISOString()
        }, {
          where: {
            activity_id: activityId,
            open_id: userId
          }
        });
      
        return {
          success: true,
          message: '签到成功'
        };
      }
      
      async checkOut() {
        const { activityId, userId } = this.ctx.request.body;
        const activity = await this.ctx.db('media_activity').findByPk(activityId);
      
        if (!activity) {
          this.ctx.throw(404, '活动不存在');
        }
        
        let [startDateTimeStr, endDateTimeStr] = activity.activity_date.split(',');

        // 将字符串转换为时间戳
        let startTimestamp = new Date(startDateTimeStr).getTime();
        let endTimestamp = new Date(endDateTimeStr).getTime();

       // 活动结束后半小时的时间戳
        let end =  endTimestamp + 0.5 * 60 * 60 * 1000; // 活动结束后半小时转换为毫秒

        const nowTimestamp = new Date().getTime();
        // 检查当前时间是否在允许签退的时间范围内
        // 只在活动结束后半小时内允许签退
        if (nowTimestamp < end || nowTimestamp > endTimestamp) {
          this.ctx.throw(400, '不在签退时间内');
        }
      
        // 检查是否已经签退
        const isAlreadyCheckedOut = await this.ctx.db('user_activity_sign_up').findOne({
          where: {
            activity_id: activityId,
            open_id: userId,
            check_out: { [Op.not]: null }
          }
        });
      
        if (isAlreadyCheckedOut) {
          this.ctx.throw(400, '已经签退');
        }
      
        // 检查是否有签到记录
        const checkInOutRecord = await this.ctx.db('user_activity_sign_up').findOne({
          where: {
            activity_id: activityId,
            open_id: userId,
            check_in: { [Op.not]: null }
          }
        });
      
        if (!checkInOutRecord) {
          this.ctx.throw(400, '没有找到签到记录');
        }
      
        // 执行签退操作
        await this.ctx.db('user_activity_sign_up').update({
          check_out: new Date().toISOString()
        }, {
          where: {
            activity_id: activityId,
            open_id: userId
          }
        });
      
        return {
          success: true,
          message: '签退成功'
        };
      }

      // destroySignUp(){
      //   let {id} = this.ctx.request.body;
      //   return this.ctx.db("user_activity_sign_up").destroy({where: {id: id}});
      // }
      async destroySignUp() {
        const { id } = this.ctx.request.body;
        const signUpRecord = await this.ctx.db('user_activity_sign_up').findByPk(id);
    
        if (!signUpRecord) {
            this.ctx.throw(404, '报名记录不存在');
        }
    
        // 减少报名人数
        await this.ctx.db('media_activity').decrement('register_num', {
            by: 1,
            where: { id: signUpRecord.activity_id },
        });
        // 删除报名记录
        await this.ctx.db('user_activity_sign_up').destroy({
            where: { id: id },
        });
    
        return {
            success: true,
            message: '取消报名成功'
        };
    }

    async addHistory(){
      const { id, type, version, user_id, user_name } = this.ctx.query
      const query = {
        item_id: id,
        user_id: user_id
      };
      // 检查记录是否已经存在
      const exists = await this.ctx.db('media_flag').findOne({
        where: query
      });
      if (!exists) {
        // 如果记录不存在，则插入新记录
        const model = { item_id: id, type, version, user_id, created: helper.time(), user_name: user_name };
        await this.ctx.db('media_flag').create(model);
      }
      return {
        success: true,
        message: '操作成功'
      };
    }

    async getHistory(){
      const { type } = this.ctx.query
      return this.ctx.db('media_flag').findAndCountAll({
        where: { type: type }
      });
    }
}
