'use strict'

/**
 * 媒体资源
 */
module.exports = class extends Controller {
  save() {
    let { id } = this.getLogin()
    let { body } = this.ctx.request
    return this.ctx.logic('media.item', body.type).save(id, body)
  }

  // async get() {
  //   // let {id, type, version} = this.ctx.query;
  //   // return this.ctx.logic('media.item', type).getEditData(id, version);
  //   // try {
  //   const { id, type, version } = this.ctx.query

  //   // 验证必须的参数是否存在
  //   // if (!id || !type || !version) {
  //   //   throw new Error('缺少必要的参数。')
  //   // }
  //   // let created = helper.time()
  //   // const model = { item_id: id, type, version, user_id, created }

  //   // 将模型插入到 media_flag 表中
  //   // await this.ctx.db('media_flag').create(model)

  //   // 获取编辑后的媒体数据
  //   const editData = await this.ctx.logic('media.item', type).getEditData(id, version)

  //   return editData
  //   // } catch (error) {
  //   //   // 记录错误并返回用户友好的消息
  //   //   console.error('get() 方法中的错误:', error)
  //   //   throw new Error('处理您的请求时发生错误。')
  //   // }
  // }
  async get() {
    try {
      const { id, type, version, user_id, user_name } = this.ctx.query

      // 1. 验证关键参数
      if (!id || !type) {
        this.ctx.throw(400, '缺少必要的参数 id 或 type')
      }

      // 2. 如果提供了用户信息和版本号，则原子性地创建或查找浏览记录
      // 使用 findOrCreate 避免竞态条件，并确保 version 存在
      if (user_id && user_name && version) {
        await this.ctx.db('media_flag').findOrCreate({
          where: {
            item_id: id,
            user_id: user_id,
          },
          defaults: {
            type: type,
            version: version,
            user_name: user_name,
            created: helper.time(),
          },
        })
      }

      // 3. 获取并返回媒体数据
      return this.ctx.logic('media.item', type).getEditData(id, version)
    } catch (error) {
      // 4. 统一的错误处理和日志记录
      this.ctx.throw(500, '处理请求时发生内部错误')
    }
  }

  async getMediaFlag() {
    const { id, user_id } = this.ctx.query;
 
    // 确保提供了 id
    if (!id) {
      return '未获取到id';
    }
 
    // 构建查询条件
    let whereClause = { item_id: id };
 
    // 如果提供了 user_id，则添加到查询条件中
    if (user_id) {
      whereClause.user_id = user_id;
    }
 
    // 从 media_flag 表中查询匹配的记录
    const flag = await this.ctx.db('media_flag').findAll({
      where: whereClause,
    });
 
    // 检查记录是否找到
    if (!flag || flag.length === 0) {
      return '未找到匹配的记录';
    }
 
    return flag;
  }

  async getByItemId() {
    let { id } = this.ctx.query
    let mediaItem = await this.ctx.db('media_item').findByPk(id)
    if (mediaItem) {
      return this.ctx.logic('media.item', mediaItem.type).getEditData(id, mediaItem.version)
    }
    return '无效的id'
  }

  async info() {
    let { id, type, version } = this.ctx.query
    let mediaItem = await this.ctx.db('media_item').findByPk(id)
    if (mediaItem.pid > 0) {
      return this.ctx.logic('media.item', type).getEditData(mediaItem.pid, version)
    }
    return this.ctx.logic('media.item', type).getEditData(id, version)
  }

  publish() {
    let { id, type, version } = this.ctx.request.body
    return this.ctx.logic('media.item', type).applyPublish(id, version)
  }

  submitAudit() {
    let { id, type, version } = this.ctx.request.body
    return this.ctx.logic('media.item', type).submitAudit(id, version)
  }

  auditList() {
    let { query } = this.ctx
    return this.ctx.logic('media.item').queryAuditList(query)
  }

  audit() {
    let { id, type, version, status } = this.ctx.request.body
    return this.ctx.logic('media.item', type).audit(id, version, status)
  }

  offline() {
    let { id, type, version } = this.ctx.request.body
    return this.ctx.logic('media.item', type).applyOffline(id, version)
  }

  query() {
    let { query } = this.ctx
    return this.ctx.logic('media.item', query.type).queryEditorView(query)
  }

  queryByChannel() {
    return this.ctx.logic('media.item').queryEditorView(this.ctx.query)
  }

  getByChannel() {
    //党员id
    let { staff_id } = this.ctx.query
    let { id } = this.getLogin()
    this.ctx.query.owner = id
    this.ctx.query.staff_id = staff_id
    return this.ctx.logic('media.item').queryMediaList(this.ctx.query)
  }

  subList() {
    let { query } = this.ctx
    return this.ctx.logic('media.item').queryMediaList(query)
  }

  pList() {
    let { id } = this.getLogin()
    this.ctx.query.userId = id
    return this.ctx.logic('media.item').queryMediaList(this.ctx.query)
  }

  share() {
    let { query } = this.ctx
    return this.ctx.logic('media.item', query.type).getShareParams(query.id)
  }

  preview() {
    let { body } = this.ctx.request
    return this.ctx.logic('media.item', body.type).getPreviewUrl(body.id, body.version, body.prefix)
  }

  delete() {
    let { body } = this.ctx.request
    return this.ctx.logic('media.item', body.type).delete(body.id, body.version)
  }

  search() {
    let { id } = this.getLogin()
    return this.ctx.logic('media.item').quickSearch(id, this.ctx.query)
  }

  userTabs() {
    let { id } = this.getLogin()
    return this.ctx.logic('media.item').userTabs(id)
  }

  branchStatistics() {
    let { id, company_id } = this.getLogin()
    return this.ctx.logic('media.item').branchStatistics(id, company_id)
  }
}
