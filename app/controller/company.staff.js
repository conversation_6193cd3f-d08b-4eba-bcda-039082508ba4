"use strict";

/**
 * 企业成员管理
 */
module.exports = class extends Controller {

  get logic() {
    return this.ctx.logic('company.staff');
  }

  async search() {
    return this.logic.search(this.ctx.query);
  }

  async query() {
    let api = await this.logic.query(this.ctx.query);
    return {
      count: await api.count(),
      rows: await api.rows()
    }
  }

  async get() {
    let {id, company_id} = this.ctx.query;
    return this.logic.getEditData(id, company_id);
  }

  create() {
    return this.logic.create(this.ctx.request.body);
  }

  update() {
    switch (this.ctx.query.part) {
      case 'party':
        return this.logic.updateParty(this.ctx.request.body);
    }

    return this.logic.update(this.ctx.request.body);
  }

  delete() {
    return this.logic.delete(this.ctx.request.body.id);
  }

  sort() {
    return this.logic.saveSort(this.ctx.request.body);
  }
  async list () {
    let login = this.getLogin();
    let company_id = login.company_id;
    let ret = await this.ctx.db('company_staff').findAll(
        {
          attributes: ['id', 'name', 'position_name', 'party_title', 'headimg', 'sequence'],
          where: {
            company_id: company_id,
            status: 1
          }
        },
    );
    return ret;
  }

  async statistics () {
    let login = this.getLogin();
    let company_id = login.company_id;
    let company_group = await this.ctx.db('company_staff_tag').findAll({
      where: {
        company_id: company_id,
        hidden: 0,
      }
    });
    let staff = await this.ctx.db('company_staff').findAll(
        {
          attributes: ['id', 'name', 'position_name', 'party_title', 'headimg', 'sequence', 'party_join','grade'],
          where: {
            company_id: company_id,
            status: 1
          }
        },
    );
    let resumes = await this.ctx.db('company_recruit_staff').findAll(
        {
          attributes: ['id', 'name', 'stage'],
          where: {
            company_id: company_id,
          }
        },
    );
    let ret = {
      //预备党员
      reserve: 0,
      //发展党员
      development: 0,
      //积极分子
      activist: 0,
      excellent: 0,
      group: company_group.length,
      staff: staff.length,
      newJoin: 0
    };
    let nowYear = new Date().getFullYear();
    staff.forEach((item, i) => {
      let joinYear = new Date(item.party_join).getFullYear();
      if (item.grade === 1) {
        ret.excellent++;
      }
      if (nowYear === joinYear) {
        ret.newJoin++;
      }
    })
    resumes.forEach((item, i) => {
      let resume = item.stage;
      if (resume === 2) {
        ret.activist++;
      }
      if (resume === 3) {
        ret.development++;
      }
      if (resume === 4) {
        ret.reserve++;
      }
    })
    return ret;
  }

  async staffList () {
    let login = this.getLogin();
    let company_id = login.company_id;
    let staffs = await this.ctx.db('company_staff').findAndCountAll(
        {
          attributes: ['id', 'name', 'position_name', 'party_title', 'headimg', 'sequence', 'party_join', 'promise_image'],
          where: {
            company_id: company_id,
            status: 1
          }
        },
    );
    staffs.rows.map(staff => {
      staff.dataValues.party_standing = this.getDateYearSub(staff.party_join);
      staff.dataValues.party_birthday = helper.Date(new Date(new Date().getFullYear(), new Date(staff.party_join).getMonth(), new Date(staff.party_join).getDate())).toString('YYYY年MM月DD日');
      return staff;
    })

    return staffs;
  }

  async politicalBirthdayList () {
    let login = this.getLogin();
    let company_id = login.company_id;
    let staffs = await this.ctx.db('company_staff').findAndCountAll(
        {
          attributes: ['id', 'name', 'position_name', 'party_title', 'headimg', 'sequence', 'party_join'],
          where: {
            company_id: company_id,
            status: 1,
          }
        },
    );
    return staffs.rows.map(staff => {
      staff.dataValues.party_standing = this.getDateYearSub(staff.party_join);
      staff.dataValues.days_to_birthday = this.getDaysToBirthday(new Date(staff.party_join).getMonth(), new Date(staff.party_join).getDate());
      return staff;
    }).filter(item => {
      return item.dataValues.days_to_birthday <= 30;
    });
  }

  newJoinList () {
    let login = this.getLogin();
    let company_id = login.company_id;
    let now = new Date();
    return this.ctx.db('company_staff').findAndCountAll(
        {
          attributes: ['id', 'name', 'position_name', 'party_title', 'headimg', 'sequence', 'party_join'],
          where: {
            [Op.and]: [
              Sequelize.where(Sequelize.fn('YEAR', Sequelize.col('party_join')), now.getFullYear()),
              {company_id: company_id},
              {status: 1},
            ]
          }
        },
    );
  }

  excellentList () {
    let login = this.getLogin();
    let company_id = login.company_id;
    return this.ctx.db('company_staff').findAndCountAll(
        {
          attributes: ['id', 'name', 'position_name', 'party_title', 'headimg', 'sequence', 'party_join'],
          where: {
            company_id: company_id,
            status: 1,
            grade: 1
          }
        },
    );
  }

  async groupList () {
    let login = this.getLogin();
    let company_id = login.company_id;
    let ret = [];
    let company_group = await this.ctx.db('company_staff_tag').findAll({
      attributes: ['id', 'name'],
      where: {
        company_id: company_id,
        hidden: 0,
      }
    });
    company_group.forEach(item => {
      ret.push(item.dataValues)
    })
    let staffs = await this.ctx.db('company_staff').findAll(
        {
          attributes: ['id', 'name', 'position_name', 'party_title', 'headimg', 'sequence', 'party_join', 'tag'],
          where: {
            company_id: company_id,
            status: 1,
          }
        },
    );
    let staffArr = [];
    staffs.forEach(item => {
      staffArr.push(item.dataValues)
    })
    ret.forEach((item, i) => {
      item.staffList = staffArr ? staffArr.filter(i => i.tag === item.id.toString()) : [];
    })

    return ret;
  }

  async profile () {
    let login = this.getLogin();
    let company_id = login.company_id;
    let {staff_id} = this.ctx.query;
    let staff = await this.ctx.db('company_staff').findOne(
        {
          attributes: ['id', 'name', 'position_name', 'party_title', 'headimg', 'sequence', 'party_join', 'party_oath', 'grade', 'promise_image'],
          where: {
            company_id: company_id,
            status: 1,
            id: staff_id
          }
        },
    );
    if (staff) {
      staff.dataValues.party_standing = this.getDateYearSub(staff.party_join);
    }
    return staff;
  }

  getDateYearSub (startDate) {
    //startDate必须为"1995/6/15"这种字符串格式，不可为"2020-6-15"，这种格式在Safari中会报错
    const birthDate = new Date(startDate);
    const momentDate = new Date();
    momentDate.setHours(0, 0, 0, 0); //因为new Date()出来的时间是当前的时分秒。我们需要把时分秒重置为0。使后面时间比较更精确
    const thisYearBirthDate = new Date(
        momentDate.getFullYear(),
        birthDate.getMonth(),
        birthDate.getDate()
    );
    const aDate = thisYearBirthDate - birthDate;
    const bDate = momentDate - birthDate;
    let tempAge = momentDate.getFullYear() - birthDate.getFullYear();
    let age = null;
    if (bDate < aDate) {
      tempAge = tempAge - 1;
      age = tempAge < 0 ? 0 : tempAge;
    } else {
      age = tempAge;
    }
    return age;
  }

  //给出生日的月份和日期，计算还有多少天过生日
  getDaysToBirthday (month, day) {
    let nowTime = new Date();
    let thisYear = nowTime.getFullYear();
    //今年的生日
    // let birthday = new Date(thisYear, month - 1, day);
    let birthday = new Date(thisYear, month, day);

    //今年生日已过，则计算距离明年生日的天数
    if (birthday < nowTime) {
      birthday.setFullYear(nowTime.getFullYear() + 1);
    }
    let timeDec = birthday - nowTime;
    let days = timeDec / (24 * 60 * 60 * 1000);
    return Math.ceil(days);
  }

}
