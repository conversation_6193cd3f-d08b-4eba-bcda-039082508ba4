// ioc 社区概况详情接口
const Sequelize = require('sequelize');
const Op = Sequelize.Op;

module.exports = class extends Controller {

    // 获取小区详情
    async queryVillageInfo () {
        let {offset, limit, grid_code} = this.ctx.query;
        let where = {};
        where.type = 1;
        if (grid_code) {
            where.parent_code = grid_code;
        }
        return this.ctx.db("region").findAndCountAll({
            where: where,
            offset: offset ? Number(offset) : 0,
            limit: limit ? Number(limit) : 10,
            // order: [['sort', 'ASC']]
        });
    }

    // 获取总户数详情
    async queryHouseholdInfo () {
        let {offset, limit, type, code, householder_name} = this.ctx.query;
        let where = {};
        switch (type) {
            case '1':
                break;
            case '2':
                where.grid_code = code;
                break;
            case '3':
                where.house_code = code;
                break
        }
        where.family_relation = '户主';
        if (householder_name) {
            where.name = {
                [Op.like]: `%${householder_name}%`
            }
        }
        return this.ctx.db("ioc_user").findAndCountAll({
            where: where,
            offset: offset ? Number(offset) : 0,
            limit: limit ? Number(limit) : 10,
            // order: [['sort', 'ASC']]
        });
    }

    // 获取总人数详情页中外来人口、户籍人口数量（户籍类型）
    async queryNumByDomicileType() {
        let {type, code} = this.ctx.query;

        let huJi = 0;
        let waiLai = 0;

        if(type === '1') {
            huJi = await this.ctx.db("ioc_user").count({
                where: {domicile_from: "户籍"}
            })
    
            waiLai = await this.ctx.db("ioc_user").count({
                where: {domicile_from: "外来"}
            })
        }

        if(type === '2') {
            huJi = await this.ctx.db("ioc_user").count({
                where: {grid_code: code, domicile_from: "户籍"}
            })
    
            waiLai = await this.ctx.db("ioc_user").count({
                where: {grid_code: code, domicile_from: "外来"}
            })
        }

        if(type === '3') {
            huJi = await this.ctx.db("ioc_user").count({
                where: {house_code: code, domicile_from: "户籍"}
            })
    
            waiLai = await this.ctx.db("ioc_user").count({
                where: {house_code: code, domicile_from: "外来"}
            })
        }

        let ret = {
            huJiRenKou: huJi,
            waiLaiRenKou: waiLai
        }

        return ret;
    }

    // 获取总人数详情页中常住人口、流动人口数量（人员类型）
    async queryNumByPersonnelType() {
        let {type, code} = this.ctx.query;

        let changZhu = 0;
        let liuDong = 0;

        if(type === '1') {
            changZhu = await this.ctx.db("ioc_user").count({
                where: {domicile_type: "常住"}
            })
    
            liuDong = await this.ctx.db("ioc_user").count({
                where: {domicile_type: "流动人口"}
            })
        }

        if(type === '2') {
            changZhu = await this.ctx.db("ioc_user").count({
                where: {grid_code: code, domicile_type: "常住"}
            })
    
            liuDong = await this.ctx.db("ioc_user").count({
                where: {grid_code: code, domicile_type: "流动人口"}
            })
        }

        if(type === '3') {
            changZhu = await this.ctx.db("ioc_user").count({
                where: {house_code: code, domicile_type: "常住"}
            })
    
            liuDong = await this.ctx.db("ioc_user").count({
                where: {house_code: code, domicile_type: "流动人口"}
            })
        }

        let ret = {
            changZhuRenKou: changZhu,
            liuDongRenKou: liuDong
        }

        return ret;
    }

    // 获取总人数详情
    async queryPersonnelInfo () {
        let {offset, limit, type, code, personnel_type, name, political_outlook} = this.ctx.query;
        let where = {};
        switch (type) {
            case '1':
                break;
            case '2':
                where.grid_code = code;
                break;
            case '3':
                where.house_code = code;
                break;
        }
        switch (personnel_type) {
            case '1':
                where.domicile_from = "外来";
                break;
            case '2':
                where.domicile_type = "流动人口";
                break;
            case '3':
                where.domicile_from = "户籍";
                break;
            case '4':
                where.domicile_type = "常住";
                break;
        }
        if (name) {
            where.name = {
                [Op.like]: `%${name}%`
            }
        }
        if (political_outlook) {
            where.political = {
                [Op.like]: `%${political_outlook}%`
            }
        }
        return this.ctx.db("ioc_user").findAndCountAll({
            where: where,
            offset: offset ? Number(offset) : 0,
            limit: limit ? Number(limit) : 10
        })
    }
}