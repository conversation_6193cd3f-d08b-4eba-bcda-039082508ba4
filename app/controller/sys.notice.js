/**
 * 通知公告
 */
const Sequelize = require('sequelize');
const Op = Sequelize.Op;

module.exports = class extends Controller {
    async query () {
        let login = this.getLogin();
        let owner = login.id;
        let {offset, limit} = this.ctx.query;
        return this.ctx.db("cms_sys_notice").findAndCountAll({
            where: {user_id: owner, user_type: 0},
            offset: offset,
            limit: limit
        });
    }

    create () {
        let login = this.getLogin();
        let owner = login.id;
        let model = this.ctx.request.body;
        model.user_id = owner;
        model.user_type = 0;
        model.created = helper.time();
        return this.ctx.db("cms_sys_notice").create(model);
    }

    update () {
        let login = this.getLogin();
        let owner = login.id;
        let model = this.ctx.request.body;
        return this.ctx.db("cms_sys_notice").update(model, {where: {id: model.id, user_id: owner, user_type: 0}});
    }

    delete () {
        let login = this.getLogin();
        let owner = login.id;
        let {id} = this.ctx.request.body;
        return this.ctx.db("cms_sys_notice").destroy({where: {id: id, user_id: owner, user_type: 0}});
    }

    async publish () {
        let login = this.getLogin();
        let owner = login.id;
        let {id, userIds} = this.ctx.request.body;
        if (Array.isArray(userIds) && userIds.length > 0) {
            let notice = await this.ctx.db("cms_sys_notice").findOne({where: {id: id, user_id: owner, user_type: 0}});
            if (notice) {
                notice.dataValues.status = '1';
                notice.dataValues.pubdate = helper.time();
                await this.ctx.db().transaction(async (t) => {
                    await this.ctx.db('cms_sys_notice').update(notice.dataValues, {where: {id: notice.id}}, {transaction: t});
                    let noticeArr = [];
                    userIds.forEach((userId) => {
                        let _notice = Object.assign({}, notice.dataValues);
                        _notice.user_id = userId;
                        _notice.user_type = '1';
                        _notice.created = helper.time();
                        delete _notice.id;
                        noticeArr.push(_notice);
                    })
                    await this.ctx.db("cms_sys_notice").destroy({
                        where: {
                            user_id: {
                                [Op.in]: userIds
                            },
                            user_type: '1'
                        }
                    }, {transaction: t});
                    await this.ctx.db().getQueryInterface().bulkInsert("cms_sys_notice", noticeArr, {transaction: t});
                });
                return "发布成功";
            }
        } else {
            return "发布失败";
        }

    }

    get () {
        let login = this.getLogin();
        let owner = login.id;
        return this.ctx.db("cms_sys_notice").findOne({where: {user_id: owner, user_type: 1}});
    }

    getByUserId () {
        let {userId} = this.ctx.query;
        return this.ctx.db("cms_sys_notice").findOne({where: {user_id: userId, status: 1}, order: [['id', 'DESC']]});
    }
}
