// ioc 社区人员
const Sequelize = require('sequelize');
const Op = Sequelize.Op;

module.exports = class extends Controller {

    // 获取年龄结构构成详情接口
    async queryAgeStructureInfo () {
        let {offset, limit, type, code, name, beginAge, endAge} = this.ctx.query;
        let where = {};
        switch (type) {
            case '1':
                break;
            case '2':
                where.grid_code = code;
                break;
            case '3':
                where.house_code = code;
                break;
        }
        if (name) {
            where.name = {
                [Op.like]: `%${name}%`
            }
        }
        if(beginAge && endAge){
            where.age = {
                [Op.between]: [beginAge, endAge]
            }
        }
        if(beginAge && !endAge){
            where.age = {
                [Op.gte]: beginAge
            }
        }
        return this.ctx.db("ioc_user").findAndCountAll({
            where: where,
            offset: offset ? Number(offset) : 0,
            limit: limit ? Number(limit) : 10
        });
    }

    // 获取学生详情页
    async queryStudents () {
        let {offset, limit, type, code, student_type, name} = this.ctx.query;
        let where = {};
        if (type === '1') {
            // where.age = {
            //     [Op.or]: [
            //         {[Op.between]: [7, 12]},
            //         {[Op.between]: [13, 16]},
            //         {[Op.between]: [17, 20]},
            //         {[Op.between]: [21, 24]}
            //     ]
            // }
            if (name) {
                where.name = {[Op.like]: `%${name}%`};
            }
            if (!student_type) {
                where.age = {[Op.between]: [7, 24]};
            }
            if (student_type === '1') {
                where.age = {[Op.between]: [7, 12]};
            }
            if (student_type === '2') {
                where.age = {[Op.between]: [13, 16]};
            }
            if (student_type === '3') {
                where.age = {[Op.between]: [17, 20]};
            }
            if (student_type === '4') {
                where.age = {[Op.between]: [21, 24]};
            }
            let data = await this.ctx.db("ioc_user").findAndCountAll({
                where: where,
                offset: offset ? Number(offset) : 0,
                limit: limit ? Number(limit) : 10
            })
            let users = data.rows;
            users.forEach((item, index) => {
                if (item.age >= 7 && item.age <= 12) {
                    item.setDataValue('student_type', '小学');
                }
                if (item.age >= 13 && item.age <= 16) {
                    item.setDataValue('student_type', '初中');
                }
                if (item.age >= 17 && item.age <= 20) {
                    item.setDataValue('student_type', '高中');
                }
                if (item.age >= 21 && item.age <= 24) {
                    item.setDataValue('student_type', '大学');
                }
            });
            data.rows = users;
            return data;
        } 
        
        if (type === '2' || type === '3') {
            let where = {};
            switch (type) {
                case '2':
                    where.grid_code = code;
                    break;
                case '3':
                    where.house_code = code;
                    break;
            }
            if (name) {
                where.name = {[Op.like]: `%${name}%`};
            }
            if (student_type === '1') {
                where.age = {[Op.between]: [7, 12]};
            }
            if (student_type === '2') {
                where.age = {[Op.between]: [13, 16]};
            }
            if (student_type === '3') {
                where.age = {[Op.between]: [17, 20]};
            }
            if (student_type === '4') {
                where.age = {[Op.between]: [21, 24]};
            }
            return this.ctx.db("ioc_user").findAndCountAll({
                where: where,
                offset: offset ? Number(offset) : 0,
                limit: limit ? Number(limit) : 10
            });
        }
    }

    // 重点监管人群
    async queryFocusCrowd () {
        let {offset, limit, type, code, focus_type, name} = this.ctx.query;
        let where = {};
        if (type === '1') {
            if (name) {
                where.name = {[Op.like]: `%${name}%`};
            }
            if (!focus_type) {
                where.focus_type = {
                    [Op.and]: [
                        {[Op.not]: null},
                        {[Op.ne]: ''}
                    ]
                };
            }
            if (focus_type === '1') {
                where.focus_type = {[Op.like]: `%社区矫正%`};
            }
            if (focus_type === '2') {
                where.focus_type = {[Op.like]: `%刑满%`};
            }
            if (focus_type === '3') {
                where.focus_type = {[Op.like]: `%稳控%`};
            }
            if (focus_type === '4') {
                where.focus_type = {[Op.like]: `%吸毒%`};
            }
            let data = await this.ctx.db("ioc_user").findAndCountAll({
                where: where,
                offset: offset ? Number(offset) : 0,
                limit: limit ? Number(limit) : 10
            })
            let users = data.rows;
            if (users.length > 0) {
                users.forEach((item, index) => {
                    item.name = item.name.substr(0, 1) + '*' + '*';
                })
            }
            data.rows = users;
            return data;
        } 
        
        if (type === '2' || type === '3') {
            let where = {};
            switch (type) {
                case '2':
                    where.grid_code = code;
                    break;
                case '3':
                    where.house_code = code;
                    break;
            }
            if (name) {
                where.name = {[Op.like]: `%${name}%`};
            }
            if (focus_type === '1') {
                where.focus_type = {[Op.like]: `%社区矫正%`};
            }
            if (focus_type === '2') {
                where.focus_type = {[Op.like]: `%刑满%`};
            }
            if (focus_type === '3') {
                where.focus_type = {[Op.like]: `%稳控%`};
            }
            if (focus_type === '4') {
                where.focus_type = {[Op.like]: `%吸毒%`};
            }
            let data = await this.ctx.db("ioc_user").findAndCountAll({
                where: where,
                offset: offset ? Number(offset) : 0,
                limit: limit ? Number(limit) : 10
            });
            let users = data.rows;
            if (users.length > 0) {
                users.forEach((item, index) => {
                    item.name = item.name.substr(0, 1) + '*' + '*';
                })
            }
            data.rows = users;
            return data;
        }
    }

    // 获取特殊人群详情
    async querySpecialPopulationsInfo () {
        let {offset, limit, type, code, name, special_type} = this.ctx.query;
        let where = {};
        switch (type) {
            case '1':
                break;
            case '2':
                where.grid_code = code;
                break;
            case '3':
                where.house_code = code;
                break;
        }
        if (name) {
            where.name = {
                [Op.like]: `%${name}%`
            };
        }
        if (special_type === '1') {
            where.special_group = {
                [Op.like]: `%低保%`
            };
        }
        if (special_type === '2') {
            where.special_group = {
                [Op.like]: `%失独%`
            };
        }
        if (special_type === '3') {
            where.special_group = {
                [Op.like]: `%困境儿童%`
            };
        }
        if (special_type === '4') {
            where.special_group = {
                [Op.like]: `%孤寡%`
            };
        }
        if (special_type === '5') {
            where.special_group = {
                [Op.or]: [
                    {[Op.like]: `%一级%`},
                    {[Op.like]: `%二级%`},
                    {[Op.like]: `%三级%`},
                    {[Op.like]: `%四级%`}
                ]
            };
        }
        if (special_type === '6') {
            where.special_group = {
                [Op.like]: `%精神%`
            }
        }
        return this.ctx.db("ioc_user").findAndCountAll({
            where: where,
            offset: offset ? Number(offset) : 0,
            limit: limit ? Number(limit) : 10
        })
    }

    // 获取军人构成详情
    async querySoldierInfo () {
        let {offset, limit, type, code, name, soldier_type} = this.ctx.query;
        let where = {};
        switch (type) {
            case '1':
                break;
            case '2':
                where.grid_code = code;
                break;
            case '3':
                where.house_code = code;
                break;
        }
        if (name) {
            where.name = {
                [Op.like]: `%${name}%`
            };
        }
        if (soldier_type === '1') {
            where.soldier_type = {
                [Op.like]: `%退役%`
            };
        }
        if (soldier_type === '2') {
            where.soldier_type = {
                [Op.like]: `%现役%`
            };
        }
        if (soldier_type === '3') {
            where.soldier_type = {
                [Op.like]: `%优抚%`
            };
        }
        return this.ctx.db("ioc_user").findAndCountAll({
            where: where,
            offset: offset ? Number(offset) : 0,
            limit: limit ? Number(limit) : 10
        })
    }

    // 获取小区名人详情
    async queryFamousInfo () {
        let {offset, limit, type, code, name, famous_type} = this.ctx.query;
        let where = {};
        switch (type) {
            case '1':
                break;
            case '2':
                where.grid_code = code;
                break;
            case '3':
                where.house_code = code;
                break;
        }
        if (name) {
            where.name = {
                [Op.like]: `%${name}%`
            };
        }
        if (famous_type === '1') {
            where.famous_type = {
                [Op.like]: `%致富%`
            };
        }
        if (famous_type === '2') {
            where.famous_type = {
                [Op.like]: `%文化名流%`
            };
        }
        if (famous_type === '3') {
            where.famous_type = {
                [Op.like]: `%职业能人%`
            };
        }
        return this.ctx.db("ioc_user").findAndCountAll({
            where: where,
            offset: offset ? Number(offset) : 0,
            limit: limit ? Number(limit) : 10
        })
    }
}