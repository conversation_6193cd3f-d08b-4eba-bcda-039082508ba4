/**
 * 志愿者活动计划公示
 */
const Sequelize = require('sequelize');
const Op = Sequelize.Op;
module.exports = class extends Controller {
    query() {
        let {id} = this.ctx.query;
        return this.ctx.db("volunteer_activity_plan").findOne({
            where: {
                id: id
            }
        });
    }

    list(){
        return this.ctx.db("volunteer_activity_plan").findAndCountAll({
            order: [['id', 'ASC']],
        });
    }
    create() {
        let model = this.ctx.request.body;
        return this.ctx.db("volunteer_activity_plan").create(model);
    }
  
    update() {
        let model = this.ctx.request.body;
        return this.ctx.db("volunteer_activity_plan").update(model, {where: {id: model.id}});
    }
  
    delete() {
        let {id} = this.ctx.request.body;
        return this.ctx.db("volunteer_activity_plan").destroy({where: {id: id}});
    }
  }