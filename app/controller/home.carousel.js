/**
 * 首页轮播图
 */
const Sequelize = require('sequelize');
const Op = Sequelize.Op;

module.exports = class extends Controller {
    async query () {
        let {offset, limit} = this.ctx.query;
        return this.ctx.db("home_carousel").findAndCountAll({
            offset: offset,
            limit: limit
        });
    }

    create () {
        let login = this.getLogin();
        let model = this.ctx.request.body;
        return this.ctx.db("home_carousel").create(model);
    }

    update () {
        let login = this.getLogin();
        let model = this.ctx.request.body;
        return this.ctx.db("home_carousel").update(model, {where: {id: model.id}});
    }

    delete () {
        let login = this.getLogin();
        let {id} = this.ctx.request.body;
        return this.ctx.db("home_carousel").destroy({where: {id: id}});
    }

    get () {
        let login = this.getLogin();
        let {id} = this.ctx.query;
        return this.ctx.db("home_carousel").findByPk(id);
    }

    list () {
        let {type} = this.ctx.query;
        let where = {status: "1"};
        if (type) {
            where.type = type;
        }
        return this.ctx.db("home_carousel").findAll({
            attributes: ['type', 'address', 'sort', 'cover_url'],
            where: where,
            order: [['sort', 'ASC']]
        })
    }
}
