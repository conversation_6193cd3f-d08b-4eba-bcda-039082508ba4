"use strict";

/**
 * 媒体频道管理
 */
module.exports = class extends Controller {

    get logic () {
        return this.ctx.logic('media.channel')
    }

    async list () {
        return this.logic.getList(this.ctx.query);
    }

    async platform () {
        return this.logic.platform();
    }

    async create () {
        return this.logic.create(this.ctx.request.body);
    }

    async update () {
        return this.logic.update(this.ctx.request.body);
    }

    async delete () {
        return this.logic.delete(this.ctx.request.body.id);
    }

    async sort () {
        return this.logic.saveSort(this.ctx.request.body);
    }

    async listByPid () {
        let {pChannel} = this.ctx.query;
        return this.ctx.db('media_channel')
            .findAll({
                where: {pid: pChannel, type: 1},
                order: [['sort', 'ASC'], ['id', 'ASC']]
            });
    }
}
