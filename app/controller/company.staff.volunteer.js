/**
 * 志愿者服务
 */
const Sequelize = require('sequelize');
const Op = Sequelize.Op;

module.exports = class extends Controller {
    async list () {
        let login = this.getLogin();
        let company_id = login.company_id;
        let {offset, limit, staff_id} = this.ctx.query;
        offset = /^\d+$/.test(offset) ? parseInt(offset) : 0;
        limit = /^\d+$/.test(limit) ? parseInt(limit) : 100;
        let whereObj = {company_id: company_id};
        if (staff_id) {
            whereObj.staff_id = staff_id;
        }
        return this.ctx.db("company_staff_volunteer").findAndCountAll({
            where: whereObj,
            order: [['sort', 'ASC'], ['id', 'ASC']],
            offset: offset,
            limit: limit
        });
    }

    create () {
        let login = this.getLogin();
        let company_id = login.company_id;
        let model = this.ctx.request.body;
        model.company_id = company_id;
        model.created = helper.time();
        return this.ctx.db("company_staff_volunteer").create(model);
    }

    update () {
        let login = this.getLogin();
        let company_id = login.company_id;
        let model = this.ctx.request.body;
        return this.ctx.db("company_staff_volunteer").update(model, {where: {id: model.id, company_id: company_id}});
    }

    delete () {
        let login = this.getLogin();
        let company_id = login.company_id;
        let {id} = this.ctx.request.body;
        return this.ctx.db("company_staff_volunteer").destroy({where: {id: id, company_id: company_id}});
    }
}
