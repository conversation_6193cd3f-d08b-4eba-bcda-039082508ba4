/**
 * 发展党员
 */
const Sequelize = require('sequelize');
const Op = Sequelize.Op;

module.exports = class extends Controller {
    async list () {
        let login = this.getLogin();
        let company_id = login.company_id;
        let {offset, limit} = this.ctx.query;
        offset = /^\d+$/.test(offset) ? parseInt(offset) : 0;
        limit = /^\d+$/.test(limit) ? parseInt(limit) : 100;
        return this.ctx.db("company_recruit_staff").findAndCountAll({
            attributes: {
                include: [
                    [Sequelize.fn('date_format', Sequelize.col('created'), '%Y-%m-%d %H:%i:%s'), 'created'],
                ]
            },
            where: {company_id: company_id},
            order: [['id', 'ASC']],
            offset: offset,
            limit: limit
        });
    }

    create () {
        let login = this.getLogin();
        let company_id = login.company_id;
        let model = this.ctx.request.body;
        model.company_id = company_id;
        model.created = new Date();
        return this.ctx.db("company_recruit_staff").create(model);
    }

    update () {
        let login = this.getLogin();
        let company_id = login.company_id;
        let model = this.ctx.request.body;
        return this.ctx.db("company_recruit_staff").update(model, {where: {id: model.id, company_id: company_id}});
    }

    delete () {
        let login = this.getLogin();
        let company_id = login.company_id;
        let {id} = this.ctx.request.body;
        return this.ctx.db("company_recruit_staff").destroy({where: {id: id, company_id: company_id}});
    }

    async statistics () {
        let login = this.getLogin();
        let company_id = login.company_id;
        let staffs = await this.ctx.db('company_recruit_staff').findAll(
            {
                attributes: ['id', 'name', 'stage'],
                where: {
                    company_id: company_id,
                }
            },
        );
        let staffArr = [];
        staffs.forEach(item => {
            staffArr.push(item.dataValues)
        })
        let ret = {
            stageOne: staffArr.filter((i => i.stage === 1)),
            stageTwo: staffArr.filter((i => i.stage === 2)),
            stageThree: staffArr.filter((i => i.stage === 3)),
            stageFour: staffArr.filter((i => i.stage === 4))
        };
        return ret;
    }
}
