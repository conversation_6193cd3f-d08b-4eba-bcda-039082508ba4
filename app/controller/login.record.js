/**
 * mini 登录记录
 */
const Sequelize = require('sequelize');
const Op = Sequelize.Op;

module.exports = class extends Controller {

    list () {
        let {open_id} = this.ctx.query;
        let list = this.ctx.db("login_record").findAndCountAll({
            where: {
                open_id: open_id
            }
        });
        return list
    }

    create () {
        // let model = this.ctx.request.body;
        // return this.ctx.db("login_record").create(model);

        let model = this.ctx.request.body;
        
        // 使用findOrCreate方法，如果记录已存在则不创建，直接返回
        return this.ctx.db("login_record").findOrCreate({
            where: {
                open_id: model.open_id,
                flag: model.flag
            },
            defaults: model // 如果记录不存在，这是要插入的默认值
        }).then(((record, created) => {
            // 如果created为true，则表示记录是新创建的
            if (created) {
                return record;
            } else {
                // 如果记录已存在，返回已存在的记录
                return record;
            }
        }));
    }

    delete () {
        let {id} = this.ctx.request.body;
        return this.ctx.db("login_record").destroy({where: {id: id}});
    }
   
}
