/**
 * 阶段履历
 */
const Sequelize = require('sequelize');
const Op = Sequelize.Op;

module.exports = class extends Controller {
    async list () {
        let login = this.getLogin();
        let company_id = login.company_id;
        let {offset, limit} = this.ctx.query;
        offset = /^\d+$/.test(offset) ? parseInt(offset) : 0;
        limit = /^\d+$/.test(limit) ? parseInt(limit) : 100;
        return this.ctx.db("company_honor").findAndCountAll({
            attributes: {
                include: [
                    [Sequelize.fn('date_format', Sequelize.col('honor_date'), '%Y-%m-%d'), 'honor_date'],
                    [Sequelize.fn('date_format', Sequelize.col('created'), '%Y-%m-%d %H:%i:%s'), 'created'],
                ]
            },
            where: {company_id: company_id},
            order: [['honor_date', 'ASC'], ['id', 'ASC']],
            offset: offset,
            limit: limit
        });
    }

    create () {
        let login = this.getLogin();
        let company_id = login.company_id;
        let model = this.ctx.request.body;
        model.company_id = company_id;
        model.created = new Date();
        return this.ctx.db("company_honor").create(model);
    }

    update () {
        let login = this.getLogin();
        let company_id = login.company_id;
        let model = this.ctx.request.body;
        return this.ctx.db("company_honor").update(model, {where: {id: model.id, company_id: company_id}});
    }

    delete () {
        let login = this.getLogin();
        let company_id = login.company_id;
        let {id} = this.ctx.request.body;
        return this.ctx.db("company_honor").destroy({where: {id: id, company_id: company_id}});
    }
}
