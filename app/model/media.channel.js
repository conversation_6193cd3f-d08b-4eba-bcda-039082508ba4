"use strict";

/**
 * media_channel
 */
module.exports = class extends Model {

    async create (data) {
        data.leaf = 1;
        data.created = helper.time();
        let {dataValues} = await this.db('media_channel').create(data);

        if (data.pid > 0) {
            await this.update(data.pid, {leaf: 0});
        }

        return dataValues;
    }

    async update (id, data) {
        await this.db('media_channel').update(data, {
            where: {id: id}
        });
    }

    async delete (id) {
        await this.db('media_channel').destroy({
            where: {id: id}
        });
    }

    async getBaseInfo (id) {
        let sql = 'SELECT id, type, pid, level, parents,leaf, item_count, owner FROM media_channel WHERE id=?';
        let [row] = await this.db().query(sql, {type: 'SELECT', replacements: [id]});
        return row;
    }

    async getChildNum (id) {
        let sql = `SELECT COUNT(*) AS num FROM media_channel WHERE pid=${id}`;
        let rows = await this.db().query(sql, {type: 'SELECT'});
        return rows.length < 1 ? -1 : rows[0].num;
    }

    /**
     * 增加、减少内容数量
     */
    async updateItemCount (parents, num) {
        if (!num) return;
        let count = 'item_count' + (num > 0 ? '+' + num : num);
        let sql = `UPDATE media_channel SET item_count=${count} WHERE id IN (${parents})`;
        await this.db().query(sql, {type: 'UPDATE'});
    }

    /**
     * 重置内容数量
     */
    async resetItemCount (vid, num) {
        let count = 'IF(item_count>0,item_count' + (num > 0 ? '+' + num : num) + ',item_count' + (num > 0 ? '+' + num : '-' + 0) + ')';
        let sql = `SELECT id, parents FROM media_channel WHERE id IN(SELECT cid FROM media_column WHERE vid=${vid})`;
        let db = this.db();
        let rows = await db.query(sql, {type: 'SELECT'});

        if (rows.length == 0) return;

        let ids = rows.reduce(function (ids, item) {
            (item.parents + (item.parents ? ',' : '') + item.id).split(',').forEach(id => {
                if (ids.indexOf(id) == -1) ids.push(id);
            });

            return ids;
        }, [])

        sql = 'UPDATE media_channel SET item_count=' + count + ' WHERE id IN(' + ids.join(',') + ')';
        await db.query(sql, {type: 'UPDATE'});
    }

    /**
     * 重新判定是否有下级
     */
    async updateLeaf (id, owner) {
        let db = this.db();
        let sql = `SELECT id FROM media_channel WHERE owner=${owner} AND pid=${id} LIMIT 1`;
        let res = await db.query(sql, {type: 'SELECT'});
        let leaf = res.length > 0 ? 0 : 1;

        sql = `UPDATE media_channel SET leaf=${leaf} WHERE id=` + id;
        await db.query(sql, {type: 'UPDATE'});
    }

    /**
     * 变更部门级别
     */
    async updateParents (id, oldParents, newParents, owner) {
        let num = newParents.split(',').length - oldParents.split(',').length;
        let level = 'level' + (num < 0 ? num : '+' + num);
        let parents = `REPLACE(parents, '${oldParents},', '${newParents},')`;
        let sql = `UPDATE media_channel SET level=${level}, parents=${parents} WHERE owner=${owner} AND FIND_IN_SET('${id}', parents)`;
        await this.db().query(sql, {type: 'UPDATE'});
    }

    async getListByOwner (id) {
        let owner = await this.db('user').findByPk(id);
        if (owner && owner.pid > 0 && owner.dataValues.home_datasource === 1) {
            let sql = 'SELECT * FROM media_channel WHERE owner in (?,?,?) ORDER BY level, sort, id';
            return this.db().query(sql, {type: 'SELECT', replacements: [1, id, owner.pid]});
        }
        let sql = 'SELECT * FROM media_channel WHERE owner=? or owner=1 ORDER BY level, sort, id';
        return this.db().query(sql, {type: 'SELECT', replacements: [id]});

    }

    async getLeafId (id) {
        const db = this.db();
        let sql = "SELECT id, type, owner, parents FROM media_channel WHERE id=" + id;
        let rows = await db.query(sql, {type: 'SELECT'});
        if (!rows.length) return;

        let item = rows[0];
        if (item.type != 0) return [item.id];

        sql = `SELECT id FROM media_channel WHERE owner=${item.owner} AND leaf=1 AND FIND_IN_SET('${item.id}', parents)`;
        rows = await db.query(sql, {type: 'SELECT'});

        if (!rows.length) return;

        return rows.map(item => item.id);
    }

    async getItemQuery (id) {
        const db = this.db();
        let res = {in: [id], first_sort: 1, second_sort: 1};
        let sql = "SELECT id, type, owner, parents, first_sort, second_sort FROM media_channel WHERE id=" + id;
        let {item} = await db.query(sql, {type: 'SELECT'});
        if (!item || item.type != 0) return res;

        res.first_sort = item.first_sort;
        res.second_sort = item.second_sort;

        sql = `SELECT id FROM media_channel WHERE owner=${item.owner} AND leaf=1 AND FIND_IN_SET('${item.id}', parents)`;
        let rows = await db.query(sql, {type: 'SELECT'});

        if (!rows.length) return res;

        res.in = rows.map(item => item.id);

        return res;
    }

    async saveSort (list) {
        const db = this.db();
        await Promise.all(list.map(item => {
            return db.query(`UPDATE media_channel SET sort=${item.val} WHERE id=${item.key}`, {type: 'UPDATE'});
        }));
    }
}
