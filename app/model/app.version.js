"use strict";

module.exports = class extends Model {

  getList(q) {
    /*
    let where = [];
    if (/^\d+$/.test(q.platform)) {
      where.push('v.platform=' + parseInt(q.platform));
    }
     */

    let sql = ' SELECT * FROM app_version ORDER BY id DESC LIMIT 10';
    return this.db().query(sql, {type: 'SELECT'});
  }

  async getLastVersion(platform) {
    let sql = ' SELECT * FROM app_version WHERE platform=? ORDER BY code DESC, id DESC LIMIT 1';
    let [data] = await this.db().query(sql, {type: 'SELECT', replacements: [platform]});
    return data;
  }
}