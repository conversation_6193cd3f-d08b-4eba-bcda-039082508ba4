"use strict";

/**
 * company表
 */
module.exports = class extends Model {

  async create(body) {
    delete body.id;
    body.created = body.modified = helper.time();
    let res = await this.db('company').create(body);
    return res.dataValues;
  }

  async update(id, body) {
    body.modified = helper.time();
    await this.db('company').update(body, {where: {id: id}});
  }

  async exists(name) {
    let sql = 'SELECT id FROM company WHERE name=?';
    let rows = await this.db().query(sql, {type: 'SELECT', replacements: [name]});
    return rows.length > 0;
  }

  /**
   * 获取部门和员工数量
   */
  async getDeptStaffNum(id) {
    let sql = `SELECT id, status, dept_num, staff_num
               FROM company
               WHERE id = ?`;
    let rows = await this.db().query(sql, {type: 'SELECT', replacements: [id]});
    return rows[0];
  }

  async getUpdateData(id, prevTime) {
    let sql = `SELECT id, name, logo, intro FROM company WHERE id=${id} AND modified>${prevTime}`;
    let [row] = await this.db().query(sql, {type: 'SELECT'});
    return row;
  }
}