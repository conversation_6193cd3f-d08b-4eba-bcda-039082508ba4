"use strict";

const BaseModel = require('./media.item');

module.exports = class extends BaseModel {

    async getEditData (id, version) {
        let where = `WHERE item.id=${id} AND item.type='news'`;
        let info = !version ? 'info.id=item.version' : helper.is_id(version) ? 'info.id=' + version : 'info.id=item.draft';
        let sql = `SELECT item.id, item.type, item.status, item.creator,item.staff_id,item.display_date,
                 info.id AS version, item.draft, info.editor, info.title,
                 info.source, info.subtitle, info.abstract, info.cover_url, info.pubdate,
                 info.year, info.area, info.language, info.province_code, info.city_code, info.district_code,
                 info.detail_url, con.content
               FROM media_item AS item
               INNER JOIN media_info AS info ON ${info} AND info.item_id=item.id
               INNER JOIN media_content AS con ON con.id=info.id
               ${where}
               LIMIT 1`;
        let db = this.db();
        let [media] = await db.query(sql, {type: 'SELECT'});

        if (!media) return media;

        let vid = media.version;

        media.channels = await this.getBindChannel(vid);

        return media;
    }

    async getUpdateData (ownerId, selfId, prevTime, offset, limit) {
        let sql = `SELECT item.id, item.type, item.version, item.is_top, item.created, item.modified,
                 info.title, info.subtitle, info.abstract, info.cover_url, info.pubdate,
                 info.detail_url, info.top_left_corner, info.lower_right_corner, content.content,
                 mc.sort
               FROM media_item AS item
               INNER JOIN media_info AS info ON info.id=item.version
               INNER JOIN media_content AS content ON content.id=item.version
               INNER JOIN media_column AS mc ON mc.vid=info.id
               WHERE (item.creator=${ownerId} or item.creator= ${selfId}) AND item.type='news' AND item.modified>${prevTime} AND item.status='online'
               ORDER BY item.id DESC
               LIMIT ${offset}, ${limit}`;
        let db = this.ctx.db();
        let rows = await db.query(sql, {type: 'SELECT'});

        let objs = {}, vid;
        let ids = rows.map((item, index) => {
            vid = item.version;
            delete item.version;

            objs[vid] = index;
            return vid;
        });

        let task = [];
        while (ids.length > 0) {
            sql = 'SELECT vid, cid, cover_url, title, sort FROM media_column WHERE vid IN (' + ids.splice(0, 50).join(',') + ')';
            task.push(db.query(sql, {type: 'SELECT'}));
        }

        let media;
        let values = await Promise.all(task);

        values.forEach(list => {
            list.forEach(item => {
                media = rows[objs[item.vid]];
                delete item.vid;

                if (!media.channels) {
                    media.channels = [];
                }

                media.channels.push(item);
            });
        });

        ids = null;
        objs = null;

        return rows;
    }

    async getUpdateDataByChannel (ownerId, selfId, offset, limit, channelId) {
        let db = this.ctx.db();
        let sql = `SELECT
	                    c.cid,c.vid,c.title as display_title, 
	                    i.detail_url,i.title,i.item_id,
	                    mc.sort 
                    FROM
	                    media_column c 
	                    INNER JOIN media_info i ON c.vid = i.id 
	                    INNER JOIN media_column AS mc ON mc.vid=i.id
                    WHERE
	                    c.vid IN (
                        SELECT
		                    info.id 
	                    FROM
		                    media_info info
		                    INNER JOIN media_item item 
	                    WHERE
		                    (
			                    info.id = item.version 
			                    AND item.type = 'news' 
			                    AND item.STATUS = 'online' 
		                        AND info.id IN ( SELECT vid FROM media_column WHERE cid = ${channelId} )))`;
        return await db.query(sql, {type: 'SELECT'});
    }
}
