"use strict";

/**
 * company_dept表
 */
module.exports = class extends Model {

  async getListByCompanyId(companyId) {
    let db = this.ctx.db();
    let sql = `SELECT d.id,
                      d.company_id,
                      d.code,
                      d.name,
                      d.sort,
                      d.leader_id,
                      u.name AS leader_name,
                      d.phone,
                      d.people,
                      d.created,
                      d.pid
               FROM company_dept AS d
                        LEFT JOIN user AS u ON u.id = d.leader_id
               WHERE d.company_id = ?
               ORDER BY d.level, d.sort, d.id`;
    return db.query(sql, {type: 'SELECT', replacements: [companyId]});
  }

  /**
   * 获取跟部门
   */
  async getRoot(companyId) {
    let sql = 'SELECT id, name FROM company_dept WHERE company_id=? AND level=1 LIMIT 1';
    let rows = await this.db().query(sql, {type: 'SELECT', replacements: [companyId]});
    return rows[0];
  }

  async getBaseInfo(id) {
    let sql = 'SELECT id, name, pid, parents, level, company_id, leaf, people FROM company_dept WHERE id=?';
    let rows = await this.db().query(sql, {type: 'SELECT', replacements: [id]});
    return rows[0];
  }

  async getParents(id, containSelf) {
    let sql = 'SELECT parents FROM company_dept WHERE id=?';
    let rows = await this.db().query(sql, {type: 'SELECT', replacements: [id]});
    if (rows.length == 0) return '';

    let parents = rows[0].parents;

    return containSelf ? (parents ? parents + ',' + id : '' + id) : parents;
  }

  async getChildNum(id, companyId) {
    let company = companyId ? companyId : `(SELECT company_id FROM company_dept WHERE id=${id})`
    let sql = `SELECT COUNT(*) AS num FROM company_dept WHERE company_id=${company} pid=${id}`;
    let rows = await this.db().query(sql, {type: 'SELECT'});
    return rows.length < 1 ? -1 : rows[0].num;
  }

  async create(body) {
    body.created = helper.time();
    body = await this.ctx.db('company_dept').create(body);
    body = body.dataValues;

    if (body.pid > 0) {
      await this.update(body.pid, {leaf: 0});
    }

    return body;
  }

  async update(id, body) {
    await this.ctx.db('company_dept').update(body, {where: {id: id}});
  }

  /**
   * 增加、减少人数
   */
  async updatePeople(parents, num) {
    if (!num) return;

    let people = 'people' + (num > 0 ? '+' + num : num);
    let sql = `UPDATE company_dept SET people=${people} WHERE id IN (${parents})`;
    await this.db().query(sql, {type: 'UPDATE'});
  }

  /**
   * 重新判定是否有下级
   */
  async updateLeaf(id, companyId) {
    let db = this.db();
    let sql = `SELECT id FROM company_dept WHERE company_id=${companyId} AND pid=${id} LIMIT 1`;
    let res = await db.query(sql, {type: 'SELECT'});
    let leaf = res.length > 0 ? 0 : 1;

    sql = `UPDATE company_dept SET leaf=${leaf} WHERE id=` + id;
    await db.query(sql, {type: 'UPDATE'});
  }

  /**
   * 变更部门级别
   */
  async updateParents(id, oldParents, newParents, companyId) {
    let num = newParents.split(',').length - oldParents.split(',').length;
    let level = 'level' + (num < 0 ? num : '+' + num);
    let parents = `REPLACE(parents, '${oldParents},', '${newParents},')`;
    let sql = `UPDATE company_dept SET level=${level}, parents=${parents} WHERE company_id=${companyId} AND FIND_IN_SET('${id}', parents)`;
    await this.db().query(sql, {type: 'UPDATE'});
  }

  async delete(id) {
    let sql = 'DELETE FROM company_dept WHERE id=' + id;
    let db = this.db();
    await db.query(sql, {type: 'DELETE'});
  }
}