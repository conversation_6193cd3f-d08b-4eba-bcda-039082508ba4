'use strict'

const Sequelize = require('sequelize')
const Op = Sequelize.Op

module.exports = class extends Model {
  async create(body) {
    body.created = body.modified = helper.time()
    let { dataValues } = await this.db('app_home').create(body)
    return dataValues
  }

  async update(id, body) {
    body.modified = helper.time()
    await this.db('app_home').update(body, { where: { id: id } })
  }

  async getByOwnerId(id) {
    let data = await this.db('app_home').findOne({ where: { owner: id } })
    let ownerObj = await this.ctx.db('user').findByPk(id)
    let ret = []
    console.log('----------ownerObj-----------:' + JSON.stringify(ownerObj))
    if (ownerObj && ownerObj.pid > 0 && ownerObj.dataValues.home_datasource === 1) {
      let pData = await this.db('app_home').findOne({ where: { owner: ownerObj.pid } })
      let pOwnerObj = await this.ctx.db('user').findByPk(ownerObj.pid)
      pData.dataValues.menus.map((item) => {
        if (pOwnerObj.dataValues.private_menu_name.split('|').indexOf(item.title) === -1) {
          item.accessFlag = false
          ret.push(item)
        }
      })
      data.dataValues.menus.map((item) => {
        if (ownerObj.dataValues.private_menu_name.split('|').indexOf(item.title) > -1) {
          item.accessFlag = true
          ret.splice(ret.length - 1, 0, item)
        }
      })
      data.dataValues.menus = ret
    }
    return data && data.dataValues
  }

  async getUpdateData(ownerId, prevTime) {
    let data = await this.db('app_home').findOne({ where: { owner: ownerId } })
    let owner = await this.db('user').findByPk(ownerId)
    if (owner && owner.pid > 0 && owner.dataValues.home_datasource === 1) {
      let pData = await this.db('app_home').findOne({
        where: {
          owner: owner.pid,
          modified: {
            [Op.gt]: prevTime,
          },
        },
      })
      if (!pData) {
        return pData
      }
      let pOwner = await this.db('user').findByPk(owner.pid)
      data.dataValues.menus.forEach((item) => {
        if (owner.dataValues.private_menu_name.split('|').indexOf(item.title) > -1) {
          pData.dataValues.menus.unshift(item)
        }
      })
      data.dataValues.menus = pData.dataValues.menus.filter((item) => pOwner.dataValues.private_menu_name.split('|').indexOf(item.title) === -1)
    }
    return data && data.dataValues
    // let sql = 'SELECT * FROM app_home WHERE owner = ? AND modified > ?';
    // let rows = await this.db().query(sql, {type: 'SELECT', replacements: [ownerId, prevTime]});
    // return rows[0];
  }

  async exists(ownerId) {
    let sql = 'SELECT id FROM app_home WHERE owner=? LIMIT 1'
    let rows = await this.db().query(sql, { type: 'SELECT', replacements: [ownerId] })
    return rows.length > 0 ? rows[0].id : 0
  }
}
