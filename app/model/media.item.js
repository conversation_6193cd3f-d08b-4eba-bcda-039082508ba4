"use strict";

module.exports = class extends Model {

    async insertItem (body, transaction) {
        let {dataValues} = await this.db('media_item').create(body, {transaction: transaction});
        return dataValues;
    }

    async insertInfo (body, transaction) {
        let {dataValues} = await this.db('media_info').create(body, {transaction: transaction});
        return dataValues;
    }

    async insertChannel (vid, list, transaction) {
        if (list.length == 0) return;

        let sql = 'INSERT INTO media_column(vid, cid, cover_url, title, sort) VALUES';
        let rps = [];

        list.forEach((item, index) => {
            sql += (index ? ',' : '') + `(${vid}, ${item.cid}, ?, ?, ${item.sort})`;
            rps.push(item.cover_url, item.title);
        });

        await this.db().query(sql, {type: 'UPDATE', replacements: rps, transaction: transaction});
    }

    async insertContent (vid, content, transaction) {
        let sql = `INSERT INTO media_content SET id=${vid}, content=?`;
        await this.db().query(sql, {type: 'UPDATE', replacements: [content], transaction: transaction});
    }

    async insertKeyword (vid, word, transaction) {
        let sql = `INSERT INTO media_search SET id=${vid}, kw=?`;
        await this.db().query(sql, {type: 'UPDATE', replacements: [word], transaction: transaction});
    }

    async updateItem (id, body, transaction) {
        await this.db('media_item').update(body, {where: {id: id}, transaction: transaction});
    }

    async getBindChannel (vid) {
        let sql = 'SELECT cid, cover_url, title, sort FROM media_column WHERE vid=?';
        return this.db().query(sql, {type: 'SELECT', replacements: [vid]});
    }

    async getOnPublishInfo (id) {
        let sql = `SELECT info.id, info.item_id, info.title, item.type, item.status, item.version, item.draft
               FROM media_info AS info
               INNER JOIN media_item AS item ON item.id=info.item_id
               WHERE info.id=${id}`;
        let [item] = await this.db().query(sql, {type: 'SELECT'});
        return item;
    }

    async getCoreById (id, type) {
        let sql = "SELECT id, type, status, creator, version, draft FROM media_item WHERE id=?" + (type ? ' AND type=?' : '');
        let [media] = await this.db().query(sql, {type: 'SELECT', replacements: [id, type]});
        return media;
    }

    async getCoreByVersion (id, type) {
        let sql = `SELECT id, type, status, creator, version, draft
               FROM media_item
               WHERE id=(SELECT item_id FROM media_info WHERE id=${id})` + (type ? ' AND type=?' : '');
        let [media] = await this.db().query(sql, {type: 'SELECT', replacements: [type]});
        return media;
    }

    async getVersions (itemId, exclude) {
        let sql = 'SELECT id FROM media_info WHERE item_id=' + itemId;
        let rows = await this.db().query(sql, {type: 'SELECT'});
        let list = [];

        rows.forEach(item => {
            if (item.id != exclude) list.push(item.id);
        });

        return list;
    }

    async deleteById (id) {
        const db = this.db();
        let sql = `INSERT INTO media_deleted (
                 item_id,type,creator,version,pv,uv,praise_num,reply_num,share_num,
                 collect_num,reward_num,allow_reply,created,modified,deleted)
               SELECT id,type,creator,version,pv,uv,praise_num,reply_num,share_num,
                 collect_num,reward_num,allow_reply,created,modified,UNIX_TIMESTAMP()
               FROM media_item WHERE id=${id}`;
        await db.query(sql, {type: 'UPDATE'});

        sql = 'DELETE FROM media_item WHERE id=' + id;
        if (id > 0) {
            sql = sql + ' OR pid=' + id;
        }
        await db.query(sql, {type: 'DELETE'});
    }

    async deleteVersion (id) {
        let db = this.db();
        let opt = {type: 'DELETE'};
        let ids = Array.isArray(id) ? id : [id];
        let eqs = ids.length > 1 ? 'IN(' + ids.join(',') + ')' : '=' + ids[0];

        return Promise.all([
            db.query(`DELETE FROM media_info WHERE id${eqs}`, opt),
            db.query(`DELETE FROM media_content WHERE id${eqs}`, opt),
            db.query(`DELETE FROM media_search WHERE id${eqs}`, opt),
            db.query(`DELETE FROM media_column WHERE vid${eqs}`, opt)
        ])
    }

    async getDeleteUpdate (ownerId, prevTime, type) {
        let sql = `SELECT item_id AS id, type, 'deleted' AS status FROM media_deleted WHERE creator=${ownerId} AND type='${type}' AND deleted>${prevTime}`;
        return this.db().query(sql, {type: 'SELECT'});
    }
}
