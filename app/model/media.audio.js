"use strict";

const BaseModel = require('./media.item');

module.exports = class extends BaseModel {

    async insertAudio (body, transaction) {
        let {dataValues} = await this.db('media_audio').create(body, {transaction: transaction});
        return dataValues;
    }

    async updateAudio (id, body, transaction) {
        await this.db('media_audio').update(body, {where: {id: id}, transaction: transaction});
    }

    async getEditData (id, version) {
        let where = `WHERE item.id=${id} AND item.type='audio'`;
        let info = !version ? 'info.id=item.version' : helper.is_id(version) ? 'info.id=' + version : 'info.id=item.draft';
        let sql = `SELECT item.id, item.type, item.status, item.creator,item.staff_id,item.display_date,
                 info.id AS version, item.draft, info.editor, info.title, info.lower_right_corner,
                 info.source, info.subtitle, info.abstract, info.cover_url, info.pubdate,
                 info.detail_url, con.content, media.duration, media.address, media.file_size
               FROM media_item AS item
               INNER JOIN media_info AS info ON ${info} AND info.item_id=item.id
               INNER JOIN media_content AS con ON con.id=info.id
               INNER JOIN media_audio AS media ON media.id=item.id
               ${where}
               LIMIT 1`;
        let db = this.db();
        let [media] = await db.query(sql, {type: 'SELECT'});

        if (!media) return media;

        let vid = media.version;

        media.channels = await this.getBindChannel(vid);

        return media;
    }

    queryEditorView (q) {
        let where = ["item.type='audio'"];
        let replace = [];
        let joins = ["INNER JOIN media_audio AS media ON media.id=item.id"];
        let orderBy = "item.is_top DESC, item.id DESC";
        let cover = 'info.cover_url';
        let title = 'info.title';
        let kws = [];
        let group = 0;
        let count;
        let qopt = {replacements: replace, type: 'SELECT'};
        let db = this.db();

        if (q.id) where.push('item.id IN (' + q.id + ')');
        if (q.creator) where.push('item.creator=' + q.creator);

        if (q.status) {
            where.push("item.status=?");
            replace.push(q.status);
        }

        if (q.status == 'online') {
            joins.push('INNER JOIN media_info AS info ON info.id=item.version');
        } else {
            joins.push('INNER JOIN media_info AS info ON info.id=item.draft');
        }

        if (Array.isArray(q.channel) && q.channel.length > 0) {
            let con;
            if (q.channel.length == 1) {
                con = '=' + q.channel[0];
            } else {
                con = ' IN(' + q.channel.join(',') + ')';
                group = 1;
                cover = "IF(mc.cover_url='', info.cover_url, mc.cover_url) AS cover_url";
                title = "IF(mc.title='', info.title, mc.title) AS title";
            }

            joins.push('INNER JOIN media_column AS mc ON mc.vid=info.id AND mc.cid' + con);

            orderBy = 'item.is_top DESC, mc.sort ' + (q.first_sort ? 'DESC' : 'ASC') + ', item.id ' + (q.second_sort ? 'DESC' : 'ASC');
        }

        if (Array.isArray(q.pubdate)) {
            where.push(`info.pubdate BETWEEN ${q.pubdate[0]} AND ${q.pubdate[1]}`);
        }

        if (Array.isArray(q.kw) && q.kw.length > 0) {
            kws = kws.concat('+' + q.kw.join('>'));
            orderBy = '';
        }

        if (kws.length > 0) {
            joins.push(`INNER JOIN media_search AS search ON search.id=info.id`);
            where.push("MATCH(search.kw) against('" + kws.join('') + "' IN boolean MODE)");
        }

        joins = joins.join(`\r`);
        where = 'WHERE ' + where.join(' AND ');
        orderBy = orderBy ? 'ORDER BY ' + orderBy : '';

        return {
            async count () {
                let sql = `SELECT COUNT(${group ? 'DISTINCT item.id' : '*'}) AS total
                   FROM media_item AS item
                   ${joins}
                   ${where}`;
                let rows = await db.query(sql, qopt);
                return count = rows[0].total;
            },
            async rows (offset, limit) {
                if (count === 0) return [];

                let sql = `SELECT
                     item.id, item.type, info.id AS version, item.status, item.created,
                     ${title}, ${cover}, item.pv, item.praise_num, item.reply_num,
                     info.pubdate, info.detail_url, info.top_left_corner, info.lower_right_corner,
                     media.address
                   FROM media_item AS item
                   ${joins}
                   ${where}
                   ${group ? 'GROUP BY item.id' : ''}
                   ${orderBy}
                   LIMIT ${offset}, ${limit}`;
                return db.query(sql, qopt);
            }
        }
    }

    async getUpdateData (ownerId, selfId, prevTime, offset, limit) {
        let sql = `SELECT item.id, item.type, item.version, item.is_top, item.created, item.modified,
                 info.title, info.subtitle, info.abstract, info.cover_url, info.pubdate,
                 info.detail_url, info.top_left_corner, info.lower_right_corner,
                 audio.duration, audio.address,
                 mc.sort
               FROM media_item AS item
               INNER JOIN media_info AS info ON info.id=item.version
               INNER JOIN media_audio AS audio ON audio.id=item.id
               INNER JOIN media_column AS mc ON mc.vid=info.id
               WHERE (item.creator=${ownerId} or item.creator=${selfId}) AND item.type='audio' AND item.modified>${prevTime} AND item.status='online'
               ORDER BY item.id DESC
               LIMIT ${offset}, ${limit}`;
        let db = this.ctx.db();
        let rows = await db.query(sql, {type: 'SELECT'});

        let objs = {}, vid;
        let ids = rows.map((item, index) => {
            vid = item.version;
            delete item.version;

            objs[vid] = index;
            return vid;
        });

        let task = [];
        while (ids.length > 0) {
            sql = 'SELECT vid, cid, cover_url, title, sort FROM media_column WHERE vid IN (' + ids.splice(0, 50).join(',') + ')';
            task.push(db.query(sql, {type: 'SELECT'}));
        }

        let media;
        let values = await Promise.all(task);

        values.forEach(list => {
            list.forEach(item => {
                media = rows[objs[item.vid]];
                delete item.vid;

                if (!media.channels) {
                    media.channels = [];
                }

                media.channels.push(item);
            });
        });

        ids = null;
        objs = null;

        return rows;
    }

    async getUpdateDataByChannel (ownerId, selfId, offset, limit, channelId) {
        let db = this.ctx.db();
        let sql = `SELECT
	                    c.cid,c.vid,c.title as display_title, 
	                    i.detail_url,i.title,i.item_id,
	                    mc.sort 
                    FROM
	                    media_column c 
	                    INNER JOIN media_info i ON c.vid = i.id
	                    INNER JOIN media_column AS mc ON mc.vid=i.id 
                    WHERE
	                    c.vid IN (
                        SELECT
		                    info.id 
	                    FROM
		                    media_info info
		                    INNER JOIN media_item item 
	                    WHERE
		                    (
			                    info.id = item.version 
			                    AND item.type = 'audio' 
			                    AND item.STATUS = 'online' 
		                        AND info.id IN ( SELECT vid FROM media_column WHERE cid = ${channelId} )))`;
        // task.push(db.query(sql, {type: 'SELECT'}));
        // let values = await Promise.all(task);
        return await db.query(sql, {type: 'SELECT'});
    }
}
