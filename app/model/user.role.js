"use strict";

/**
 * 用户角色
 */
module.exports = class extends Model {

  /**
   * 获取用户角色
   */
  get(userId) {
    let sql = `SELECT ur.role_id AS id, sr.name, ur.created, sr.enabled
               FROM cms_user_role AS ur
               INNER JOIN cms_sys_role AS sr ON sr.id=ur.role_id
               WHERE ur.user_id=${userId}`;
    return this.ctx.db().query(sql, {type: 'SELECT'});
  }

  async reset(userId, roles) {
    this.ctx.assert(Array.isArray(roles));

    let sql = 'SELECT id, role_id FROM cms_user_role WHERE user_id=' + userId;
    const db = this.ctx.db();
    const clear = [];
    const rows = await db.query(sql, {type: 'SELECT'});

    rows.forEach(item => {
      let i = roles.indexOf(item.role_id);

      if (i == -1) {
        clear.push(item.id);
      } else {
        roles.splice(i, 1);
      }
    });

    if (clear.length > 0) {
      await this.delete(userId, clear);
    }

    if (roles.length > 0) {
      await this.append(userId, roles);
    }
  }

  /**
   * 给用户添加角色
   */
  async append(userId, roleId) {
    let sql = `INSERT INTO cms_user_role(user_id, role_id, created)`;

    (Array.isArray(roleId) ? roleId : [roleId]).forEach((i, id) => {
      this.ctx.assert(/^\d+$/.test(id), '无效的角色ID');
      sql += (i > 0 ? ',' : '') + `(${userId}, ${id}, UNIX_TIMESTAMP())`;
    });

    await this.ctx.db.query(sql, {type: 'UPDATE'});
  }

  /**
   * 删除用户角色
   */
  async delete(userId, roleId) {
    let sql = `DELETE FROM cms_user_role WHERE user_id=${userId}`;

    if (roleId) {
      const values = (Array.isArray(roleId) ? roleId : [roleId]).map(id => {
        this.ctx.assert(/^\d+$/.test(id), '无效的角色ID');
        return id;
      });

      sql += ' AND role_id IN (' + values.join(',') + ')';
    }

    await this.ctx.db().query(sql, {type: 'DELETE'});
  }

  /**
   * 是否存在角色
   */
  async exists(userId, roleId) {
    const sql = `SELECT 1 FROM cms_user_role WHERE user_id=${userId} AND role_id=${roleId}`;
    const rows = await this.ctx.db().query(sql, {type: 'SELECT'});
    return rows.length > 0;
  }

}
