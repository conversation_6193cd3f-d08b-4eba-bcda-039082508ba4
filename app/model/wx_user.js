"use strict";

module.exports = class extends Model {

  db(name) {
    return this.ctx.db(name);
  }

  async create(body) {
    await this.db('wx_user').create({});
  }

  async update(id, body) {
    await this.db('wx_user').update({});
  }

  async get(openid, appid) {
    let sql = 'SELECT * FROM wx_user WHERE openid=? AND appid=? LIMIT 1';
    let rows = await this.db().query(sql, {type: 'SELECT', replacements: [openid, appid]});
    return rows[0];
  }

  async getUserId(openid) {
    let sql = 'SELECT uid FROM wx_user WHERE openid=? LIMIT 1';
    let rows = await this.db().query(sql, {type: 'SELECT', replacements: [openid]});
    return rows.length > 0 ? rows[0].uid : null;
  }

  async setUserId(openid, userId) {
    await this.db().query('UPDATE wx_user SET uid=? WHERE openid=?', {type: 'UPDATE', replacemetns: [userId, openid]});
  }
}