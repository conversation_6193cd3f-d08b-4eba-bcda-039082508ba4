"use strict";

module.exports = class extends Model {

  query (q) {
    let where = [];
    let rps = [];
    let scope = this;

    if (q.id) {
      where.push('u.id=?');
      rps.push(q.id);
    }

    if (q.sex) {
      where.push('u.sex=?');
      rps.push(q.sex);
    }

    if (Array.isArray(q.created)) {
      where.push('u.created BETWEEN ? AND ?');
      rps.push(q.created[0], q.created[1]);
    }

    if (q.province_code) {
      where.push('u.province_code=?');
      rps.push(q.province_code);
    }

    if (q.city_code) {
      where.push('u.city_code=?');
      rps.push(q.city_code);
    }

    if (q.district_code) {
      where.push('u.district_code=?');
      rps.push(q.district_code);
    }

    if (q.nickname) {
      where.push('u.nickname LIKE ?');
      rps.push('%' + q.nickname + '%');
    }

    if (q.name) {
      where.push('u.name LIKE ?');
      rps.push('%' + q.name + '%');
    }

    if (q.mobile) {
      where.push('u.mobile=?');
      rps.push(q.mobile);
    }
    if (q.audit_status) {
      where.push('u.audit_status=?');
      rps.push(q.audit_status);
    }
    if (q.status) {
      where.push('u.status=?');
      rps.push(q.status);
    }

    where = where.length > 0 ? 'WHERE ' + '' + where.join(' AND ') : '';
    let total = -1;
    return {
      async count () {
        let sql = `SELECT COUNT(*) AS total FROM user AS u ${where}`;
        let rows = await scope.db().query(sql, {type: 'SELECT', replacements: rps});
        return total = rows[0].total;
      },
      async rows (offset = q.offset, limit = q.limit) {
        if (total == 0) return [];

        let sql = `SELECT u.id, u.nickname, u.headimg, u.name, u.sex, u.mobile, u.mobile_area,
                     u.username, u.email, u.last_login, u.realname_status,
                     u.province_code, u.city_code, u.district_code, u.nation,
                     u.created,u.audit_status,u.status,u.company_id,
                     c.name as c_name,c.alias,c.logo,
                     r.id as role_id,r.name as role_name
                   FROM user AS u inner join company c on u.company_id = c.id
                   inner join cms_user_access a on a.user_id = u.id
                   inner join cms_sys_role r on r.id = a.roles
                   ${where}
                   ORDER BY id DESC
                   LIMIT ${offset}, ${limit}`;
        return scope.db().query(sql, {type: 'SELECT', replacements: rps});
      }
    }

  }

  async update (id, data) {
    await this.db('user').update(data, {where: {id: id}});
  }

  /**
   * 获取昵称
   */
  async getNickname (id) {
    let rows = await this.db().query('SELECT nickname FROM user WHERE id=?', {type: 'SELECT', replacements: [id]});
    return rows[0] ? rows[0].nickname : null;
  }

  /**
   * 设置昵称
   */
  async setNickname (id, value) {
    await this.db().query('UPDATE user SET nickname=? WHERE id=?', {type: 'UPDATE', replacements: [value, id]});
  }

  /**
   * 是否存在昵称
   */
  async hasNickname (value, notUid) {
    let sql = 'SELECT id FROM user WHERE nickname=? LIMIT 1';
    let rows = await this.db().query(sql, {type: 'SELECT'});
    return rows.length > 0 ? rows[0].id != notUid : false;
  }

  /**
   * 获取头像
   */
  async getHeadimg (uid) {
    let sql = 'SELECT headimg FROM user WHERE id=?';
    let rows = await this.db().query(sql, {type: 'SELECT', replacements: [uid]});
    return rows.length > 0 ? rows[0].headimg : '';
  }

  /**
   * 设置头像
   */
  async setHeadimg (uid, value) {
    let sql = 'UPDATE user SET headimg=? WHERE id=?';
    await this.db().query(sql, {type: 'UPDATE', replacements: [value, uid]});
  }

  /**
   * 获取手机号
   */
  async getMobile (uid) {
    let sql = 'SELECT mobile, mobile_area FROM user WHERE id=?';
    let rows = await this.db().query(sql, {type: 'SELECT', replacements: [uid]});
    return rows[0];
  }

  /**
   * 设置手机号
   */
  async setMobile (uid, value, area) {
    return this.update(uid, {mobile: value, mobile_area: area});
  }

  /**
   * 修改性别
   */
  async setSex (uid, val) {
    let sex = val.toString();
    sex = sex == '1' ? 1 : sex == '2' ? 2 : 0;

    let sql = `UPDATE user SET sex='${sex}' WHERE id=` + uid;
    await this.db().query(sql, {type: 'UPDATE'});
  }

  /**
   * 设置区域
   */
  async setAddress (uid, address) {
    return this.update(uid, address);
  }

  async equPassword (uid, password) {
    let sql = 'SELECT password FROM user WHERE id=?';
    let rows = await this.db().query(sql, {type: 'SELECT', replacements: [uid]});
    return rows.length > 0 ? rows[0].password == password : false;
  }

  async setPassword (uid, password) {
    return this.update(uid, {password: password});
  }

  async setCompanyId (uid, cid) {
    return this.update(uid, {company_id: cid});
  }
}
