"use strict";

/**
 * 企业员工分组管理
 */
module.exports = class extends Model {

  async getList(companyId, visible) {
    let where = "WHERE company_id=?";

    if (visible) {
      where += ' AND hidden=0';
    }

    let sql = "SELECT * FROM company_staff_tag " + where + " ORDER BY sort, id";
    return this.db().query(sql, {type: 'SELECT', replacements: [companyId]});
  }

  async create(body) {
    let sql = "INSERT INTO company_staff_tag SET company_id=?, name=?, sort=?, people=0, hidden=?, created=UNIX_TIMESTAMP();";
    return this.db().query(sql, {
      type: 'CREATE', replacements: [body.company_id, body.name, body.sort, body.hidden]
    });
  }

  async update(id, body) {
    let i = 0;
    let sql = "UPDATE company_staff_tag SET ";
    let rps = [];

    for (let field in body) {
      if (field == 'name' || field == 'sort' || field == 'hidden') {
        sql += (i++ > 0 ? ',' : '') + field + '=?';
        rps.push(body[field]);
      }
    }

    rps.push(id);

    return this.db().query(sql + ' WHERE id=?', {
      type: 'UPDATE', replacements: rps
    });
  }

  async delete(id) {
    await this.db().query('DELETE FROM company_staff_tag WHERE id=?', {type: 'DELETE', replacements: [id]});
  }

  async find(id) {
    let rows = await this.db().query('SELECT * FROM company_staff_tag WHERE id=?', {type: 'SELECT', replacements: [id]});
    return rows[0];
  }

  async clearUser(companyId, tagId) {
    let sql = `UPDATE company_staff SET tag=TRIM(BOTH ',' FROM replace(concat(',', tag, ','), ',${tagId},', ',')) WHERE company_id=${companyId} AND FIND_IN_SET(${tagId}, tag)`;
    await this.db().query(sql, {type: 'UPDATE'});
  }

  async adjustPeople(id, val) {
    id = Array.isArray(id) ? id.join(',') : id;
    let sql = `UPDATE company_staff_tag SET people=people${val} WHERE id IN(${id})`;
    await this.db().query(sql, {type: 'UPDATE'});
  }

  async saveSort(list) {
    const db = this.db();
    await Promise.all(list.map(item => {
      return db.query(`UPDATE company_staff_tag SET sort=${item.val} WHERE id=${item.key}`, {type: 'UPDATE'});
    }));
  }

  async getUpdateData(companyId) {
    let sql = `SELECT id, company_id, name, sort
               FROM company_staff_tag
               WHERE company_id = ${companyId} AND hidden = 0
               ORDER BY sort, id`;
    return this.db().query(sql, {type: 'SELECT'});
  }
}