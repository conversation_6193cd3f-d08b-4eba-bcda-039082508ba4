"use strict";

/**
 * 用户账号
 * type:
 * 0自定义账号
 * 1手机号
 * 2邮箱
 */
module.exports = class extends Model {

    get db () {
        return this.ctx.db;
    }

    /**
     * 创建登录账号
     */
    async createAccount (userId, username, type) {
        const sql = `INSERT INTO cms_user_account SET username=?, type=${type}, uid=${userId}`;
        await this.db().query(sql, {type: 'UPDATE', replacements: [username]});
    }

    /**
     * 删除登录账号
     */
    async deleteAccount (userId, username, type) {
        let rps;
        let sql = `DELETE FROM cms_user_account WHERE uid=${userId}`;

        if (username) {
            sql += ' AND username=?';
            rps = [username];
        }

        if (!isNaN(type)) {
            sql += ' AND type=?';
            rps = [type];
        }

        await this.ctx.db().query(sql, {type: 'DELETE', replacements: rps});
    }

    /**
     * 是否存在账号
     */
    async existsAccount (username, isNotUid) {
        const sql = `SELECT uid FROM cms_user_account WHERE username=? LIMIT 1`;
        const rows = await this.ctx.db().query(sql, {type: 'SELECT', replacements: [username]});
        return rows.length > 0 && rows[0].uid != isNotUid;
    }

    /**
     * 获取登录账号
     */
    async getAccount (username) {
        let sql = `SELECT uid, username, type FROM cms_user_account WHERE username=? LIMIT 1`;
        let rows = await this.ctx.db().query(sql, {type: 'SELECT', replacements: [username]});
        return rows[0];
    }

    /**
     * 根据用户id获取登录所需信息
     */
    async getLoginUserById (id) {
        return this.getLoginUser('u.id=?', [id]);
    }

    /**
     * 根据登录账号获取登录所需信息
     */
    async getLoginUserByUsername (username) {
        const where = 'u.id=(SELECT uid FROM cms_user_account WHERE username=? LIMIT 1)'
        return this.getLoginUser(where, [username]);
    }

    /**
     * 获取登录所需的信息
     */
    async getLoginUser (where, replacements) {
        let sql = `SELECT u.id, u.nickname, u.headimg, u.name, u.sex, u.mobile, u.mobile_area, u.created,u.home_datasource,
               u.username, u.password, u.pid, u.company_id, company.name AS company_name,u.audit_status,u.status
               FROM user AS u
               LEFT JOIN company ON company.id=u.company_id
               WHERE ${where}`;
        let rows = await this.ctx.db().query(sql, {type: 'SELECT', replacements: replacements});
        return rows[0];
    }

    /**
     * 往user表插入一条记录
     */
    async createUser (params) {
        const user = {
            pid: params.pid || 0,
            nickname: params.nickname || '',
            headimg: params.headimg || '',
            name: params.name || '',
            sex: params.sex || 0,
            province_code: params.province_code || 0,
            city_code: params.city_code || 0,
            district_code: params.district_code || 0,
            birthday: params.birthday || '0000-00-00',
            mobile: params.mobile || '',
            mobile_area: params.mobile ? params.mobile_area : '',
            email: params.email || '',
            username: params.username || '',
            password: params.password || '',
            status: 1,
            realname_status: 0,
            intro: params.intro || '',
            created: helper.time(),
            school_name: params.school_name || '',
            education: params.education || 0,
            join_work: params.join_work || '0000-00-00',
            nation: params.nation || 0,
            company_id: params.company_id || 0
        };

        const res = await this.db('user').create(user);
        user.id = res.id;

        return user;
    }
}
