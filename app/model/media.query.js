"use strict";

module.exports = class extends Model {

    // queryEditorView (q) {
    //     let where = [];
    //     let replace = [];
    //     let joins = [];
    //     let orderBy = "item.is_top DESC, item.id DESC";
    //     let cover = 'info.cover_url';
    //     let title = 'info.title';
    //     let kws = [];
    //     let group = 0;
    //     let count;
    //     let qopt = {replacements: replace, type: 'SELECT'};
    //     let db = this.db();
    //
    //     if (q.id) where.push('item.id IN (' + q.id + ')');
    //     if (q.creator) where.push('item.creator=' + q.creator);
    //
    //     if (q.type) {
    //         where.push("item.type=?");
    //         replace.push(q.type);
    //     }
    //
    //     if (q.status) {
    //         where.push("item.status=?");
    //         replace.push(q.status);
    //     }
    //
    //     if (q.status === 'online') {
    //         joins.push('INNER JOIN media_info AS info ON info.id=item.version');
    //     } else {
    //         joins.push('INNER JOIN media_info AS info ON info.id=item.draft');
    //     }
    //
    //     if (Array.isArray(q.channel) && q.channel.length > 0) {
    //         let con;
    //         if (q.channel.length === 1) {
    //             con = '=' + q.channel[0];
    //         } else {
    //             con = ' IN(' + q.channel.join(',') + ')';
    //             group = 1;
    //             cover = "IF(mc.cover_url='', info.cover_url, mc.cover_url) AS cover_url";
    //             title = "IF(mc.title='', info.title, mc.title) AS title";
    //         }
    //
    //         joins.push('INNER JOIN media_column AS mc ON mc.vid=info.id AND mc.cid' + con);
    //         orderBy = 'item.is_top DESC, mc.sort ' + (q.first_sort ? 'DESC' : 'ASC') + ', item.id ' + (q.second_sort ? 'DESC' : 'ASC');
    //     }
    //
    //     if (Array.isArray(q.pubdate)) {
    //         where.push(`info.pubdate BETWEEN ${q.pubdate[0]} AND ${q.pubdate[1]}`);
    //     }
    //
    //     if (Array.isArray(q.kw) && q.kw.length > 0) {
    //         kws = kws.concat('+' + q.kw.join('>'));
    //         orderBy = '';
    //     }
    //
    //     if (kws.length > 0) {
    //         joins.push(`INNER JOIN media_search AS search ON search.id=info.id`);
    //         where.push("MATCH(search.kw) against('" + kws.join('') + "' IN boolean MODE)");
    //     }
    //
    //     joins = joins.join(`\r`);
    //     where = 'WHERE ' + where.join(' AND ');
    //     orderBy = 'ORDER BY ' + orderBy;
    //
    //     return {
    //         async count () {
    //             let sql = `SELECT COUNT(${group ? 'DISTINCT item.id' : '*'}) AS total
    //                FROM media_item AS item
    //                ${joins}
    //                ${where}`;
    //             let rows = await db.query(sql, qopt);
    //             return count = rows[0].total;
    //         },
    //         async rows (offset, limit) {
    //             if (count === 0) return [];
    //
    //             let sql = `SELECT
    //                  item.id, item.type, info.id AS version, item.status, item.created,
    //                  ${title}, ${cover}, item.pv, item.praise_num, item.reply_num,
    //                  info.pubdate, info.detail_url, info.top_left_corner, info.lower_right_corner
    //                FROM media_item AS item
    //                ${joins}
    //                ${where}
    //                ${group > 0 ? 'GROUP BY item.id' : ''}
    //                ${orderBy}
    //                LIMIT ${offset}, ${limit}`;
    //             return db.query(sql, qopt);
    //         }
    //     }
    // }
    queryEditorView (q) {
        let where = [];
        let replace = [];
        let joins = [];
        let orderBy = "item.is_top DESC, item.id DESC";
        let cover = 'info.cover_url';
        let title = 'info.title';
        let kws = [];
        let group = 0;
        let count;
        let qopt = {replacements: replace, type: 'SELECT'};
        let db = this.db();

        if (q.id) where.push('item.id IN (' + q.id + ')');
        if (q.creator) where.push('item.creator=' + q.creator);

        if (q.type) {
            where.push("item.type=?");
            replace.push(q.type);
        }

        if (q.status) {
            where.push("item.status=?");
            replace.push(q.status);
        }

        if (q.status == 'online') {
            joins.push('INNER JOIN media_info AS info ON info.id=item.version');
        } else {
            joins.push('INNER JOIN media_info AS info ON info.id=item.draft');
        }

        if (Array.isArray(q.channel) && q.channel.length > 0) {
            let con;
            if (q.channel.length == 1) {
                con = '=' + q.channel[0];
            } else {
                con = ' IN(' + q.channel.join(',') + ')';
                group = 1;
                cover = "IF(mc.cover_url='', info.cover_url, mc.cover_url) AS cover_url";
                title = "IF(mc.title='', info.title, mc.title) AS title";
            }

            joins.push('INNER JOIN media_column AS mc ON mc.vid=info.id AND mc.cid' + con);
            orderBy = 'item.is_top DESC, mc.sort ' + (q.first_sort ? 'DESC' : 'ASC') + ', item.id ' + (q.second_sort ? 'DESC' : 'ASC');
        }

        if (Array.isArray(q.pubdate)) {
            where.push(`info.pubdate BETWEEN ${q.pubdate[0]} AND ${q.pubdate[1]}`);
        }

        if (Array.isArray(q.kw) && q.kw.length > 0) {
            kws = kws.concat('+' + q.kw.join('>'));
            orderBy = '';
        }

        if (kws.length > 0) {
            joins.push(`INNER JOIN media_search AS search ON search.id=info.id`);
            where.push("MATCH(search.kw) against('" + kws.join('') + "' IN boolean MODE)");
        }

        joins = joins.join(`\r`);
        where = 'WHERE ' + where.join(' AND ');
        orderBy = orderBy ? 'ORDER BY ' + orderBy : '';

        return {
            async count () {
                let sql = `SELECT COUNT(${group ? 'DISTINCT item.id' : '*'}) AS total
                   FROM media_item AS item
                   ${joins}
                   ${where}`;
                let rows = await db.query(sql, qopt);
                return count = rows[0].total;
            },
            async rows (offset, limit) {
                if (count === 0) return [];

                let sql = `SELECT
                     item.id, item.type, info.id AS version, item.status, item.created,
                     ${title}, ${cover}, item.pv, item.praise_num, item.reply_num,
                     info.pubdate, info.detail_url, info.top_left_corner, info.lower_right_corner
                   FROM media_item AS item
                   ${joins}
                   ${where}
                   ${group ? 'GROUP BY item.id' : ''}
                   ${orderBy}
                   LIMIT ${offset}, ${limit}`;
                return db.query(sql, qopt);
            }
        }
    }
    async queryMediaList (q) {
        let where = [];
        let replace = [];
        let joins = [];
        let limit = '';
        let orderBy = "item.is_top DESC, item.id DESC";
        let cover = 'info.cover_url';
        let title = 'info.title';
        let kws = [];
        let group = 0;
        let qopt = {replacements: replace, type: 'SELECT'};
        let db = this.db();

        if (q.id) where.push('item.id IN (' + q.id + ')');
        //上级账号获取下级资讯
        if (q.creator) {
            let subUsers = await this.db("user").findAll({where: {pid: q.creator}});
            let idArr = [];
            subUsers.forEach((user) => {
                idArr.push(user.id);
            })
            if (idArr.length === 0) return [];
            where.push('(item.creator IN (' + idArr.join(',') + ') or (item.top_expired > 0))');
            where.push('item.pid =' + 0);
        }
        //下级账号获取上级资讯
        if (q.userId) {
            where.push('((item.creator =' + q.userId + ' and item.pid>0) or (item.top_expired > 0))');
            // where.push('item.pid >' + 0);
        }
        //发布人
        if (q.owner) {
            where.push('(item.creator =' + q.owner + ' and item.pid=0)');
            joins.push('INNER JOIN company_staff AS staff ON staff.id=item.staff_id');
            let channelObj = await this.db("media_channel").findOne({where: {intro: q.channelType}});
            if (channelObj) {
                q.channel = [channelObj.id];
            }
            if (q.staff_id) {
                where.push('item.staff_id =' + q.staff_id);
            }
        }
        //获取指定下级资讯
        if (q.subUserId) {
            where.push('item.creator =' + q.subUserId);
            where.push('item.pid =' + 0);
        }
        where.push("item.status = 'online'");
        joins.push('INNER JOIN media_info AS info ON info.id=item.version');
        joins.push('INNER JOIN user AS user ON user.id=item.creator');
        if (q.userId) {
            joins.push('LEFT JOIN user AS p_user ON p_user.id=user.pid');
            joins.push('LEFT JOIN company AS company ON IF(p_user.id>0,company.id=p_user.company_id,company.id=user.company_id)');
        } else {
            joins.push('INNER JOIN company AS company ON company.id=user.company_id');
        }
        if (Array.isArray(q.channel) && q.channel.length > 0) {
            let con;
            if (q.channel.length == 1) {
                con = '=' + q.channel[0];
            } else {
                con = ' IN(' + q.channel.join(',') + ')';
                group = 1;
                cover = "IF(mc.cover_url='', info.cover_url, mc.cover_url) AS cover_url";
                title = "IF(mc.title='', info.title, mc.title) AS title";
            }

            joins.push('INNER JOIN media_column AS mc ON mc.vid=info.id AND mc.cid' + con);
            orderBy = 'item.display_date ASC, item.is_top DESC, mc.sort ' + (q.first_sort ? 'DESC' : 'ASC') + ', item.id ' + (q.second_sort ? 'DESC' : 'ASC');
        }

        if (Array.isArray(q.pubdate)) {
            where.push(`info.pubdate BETWEEN ${q.pubdate[0]} AND ${q.pubdate[1]}`);
        }

        if (Array.isArray(q.kw) && q.kw.length > 0) {
            kws = kws.concat('+' + q.kw.join('>'));
            orderBy = '';
        }

        if (kws.length > 0) {
            joins.push(`INNER JOIN media_search AS search ON search.id=info.id`);
            where.push("MATCH(search.kw) against('" + kws.join('') + "' IN boolean MODE)");
        }

        joins = joins.join(`\r`);
        where = 'WHERE ' + where.join(' AND ');
        orderBy = orderBy ? 'ORDER BY ' + orderBy : '';
        if (q.offset && q.limit) {
            limit = `LIMIT ${q.offset}, ${q.limit}`;
        }
        let sql = `SELECT
                     item.id, item.type, info.id AS version, item.status, item.created,
                     ${title}, ${cover}, item.pv, item.praise_num, item.reply_num, item.staff_id, item.display_date,
                     info.pubdate, info.detail_url, info.top_left_corner, info.lower_right_corner,
                     company.alias${q.owner > 0 ? ',staff.name as staff_name' : ''}
                   FROM media_item AS item
                   ${joins}
                   ${where}
                   ${group ? 'GROUP BY item.id' : ''}
                   ${orderBy}
                   ${limit}`;
        return await db.query(sql, qopt);
    }

    async queryAuditList (q) {
        let where = [];
        let replace = [];
        let joins = [];
        let limit = '';
        let orderBy = "case when item.status = 'auditing' then 0 else 1 end,item.id DESC";
        let cover = 'info.cover_url';
        let title = 'info.title';
        let group = 0;
        let count;
        let qopt = {replacements: replace, type: 'SELECT'};
        let db = this.db();

        let role = await this.ctx.db("cms_sys_role").findOne({where: {name: "平台运营"}});
        if (!role) {
            return [];
        }
        let users = await this.ctx.db("cms_user_access").findAll({where: {roles: role.id}});
        let idArr = [];
        users.forEach((user) => {
            idArr.push(user.user_id);
        })
        where.push('item.creator IN (' + idArr.join(',') + ')');
        where.push('item.pid =' + 0);
        where.push("(item.status = 'rejected' or item.status = 'auditing')")
        joins.push('INNER JOIN media_info AS info ON info.id=item.version');
        joins.push('INNER JOIN user AS user ON user.id=item.creator');
        joins.push('INNER JOIN company AS company ON company.id=user.company_id');
        joins = joins.join(`\r`);
        where = 'WHERE ' + where.join(' AND ');
        orderBy = 'ORDER BY ' + orderBy;
        if (q.offset && q.limit) {
            limit = `LIMIT ${q.offset}, ${q.limit}`;
        }
        return {
            async count () {
                let sql = `SELECT COUNT(${group ? 'DISTINCT item.id' : '*'}) AS total
                   FROM media_item AS item
                   ${joins}
                   ${where}`;
                let rows = await db.query(sql, qopt);
                return count = rows[0].total;
            },
            async rows () {
                if (count === 0) return [];

                let sql = `SELECT
                     item.id, item.type, info.id AS version, item.status, item.created,
                     ${title}, ${cover}, item.pv, item.praise_num, item.reply_num,
                     info.pubdate, info.detail_url, info.top_left_corner, info.lower_right_corner,
                     company.alias
                   FROM media_item AS item
                   ${joins}
                   ${where}
                   ${group > 0 ? 'GROUP BY item.id' : ''}
                   ${orderBy}
                   ${limit}`;
                return db.query(sql, qopt);
            }
        }
    }

    /**
     * 快速搜索
     */
    async quickSearch (id, q) {
        let where = "WHERE item.status='online' and pid = 0 and creator=" + id;
        let order = '';
        let join = '';
        let limit;
        let rps;

        if (q.id) {
            where = "WHERE item.id=?";
            limit = 'LIMIT 1';
            rps = q.id;
        } else {
            limit = `LIMIT ${q.offset}, ${q.limit}`;

            if (q.kw && q.kw !== '+') {
                join = 'INNER JOIN media_search AS search ON search.id=item.version';
                where += ' AND AND MATCH(search.kw) against(? IN boolean MODE)';
                rps = q.kw;
            } else {
                order = 'ORDER BY info.id DESC';
            }
        }

        let sql = `SELECT item.id, item.type, info.title, info.cover_url
               FROM media_item AS item
               ${join}
               INNER JOIN media_info AS info ON info.id=item.version
               ${where} ${order}`;
        // ${where} ${order} ${limit}`;
        return this.db().query(sql, {type: 'SELECT', replacements: [rps]});
    }

    async getUpdateDelete (ownerId, pid, prevTime) {
        let sql1 = `SELECT item_id AS id, type FROM media_deleted WHERE (creator=${ownerId} or creator=${pid}) AND deleted>${prevTime}`;
        let sql2 = `SELECT id, type FROM media_item WHERE (creator=${ownerId} or creator=${pid}) AND modified>${prevTime} AND status IN ('offline', 'expired')`;
        return this.db().query(sql1 + ' UNION ' + sql2, {type: 'SELECT'});
    }

    async userTabs (id) {
        let sql = `SELECT u.id,c.alias,count(1) media_sum from user u INNER JOIN company c ON u.company_id = c.id 
        JOIN media_item m ON m.creator=u.id WHERE u.id in(SELECT id from user WHERE pid = ${id})  AND m.status = 'online' AND m.pid=0 GROUP BY c.alias,u.id;`;
        return this.db().query(sql, {type: 'SELECT'});
    }

    async branchStatistics (id, company_id) {
        let sql = `SELECT
                     item.id, item.type, item.version, item.status, item.created,
                     ch.intro,ch.name,ch.id as cid
                    FROM media_item as item 
                    INNER JOIN media_column AS mc ON mc.vid=item.version AND mc.cid NOT IN(SELECT id from media_channel WHERE owner = ${id})
                    INNER JOIN media_channel AS ch ON ch.id = mc.cid 
                     where item.creator = ${id};`;
        let medias = await this.db().query(sql, {type: 'SELECT'});
        let volunteers = await this.ctx.db("company_staff_volunteer").findAndCountAll({
            where: {
                company_id: company_id
            }
        });
        let ret = {
            zbdydh: 0,
            zbwyh: 0,
            dxzh: 0,
            dk: 0,
            ztdr: 0,
            zzshh: 0,
            mzpydy: 0,
            zyfw: 0
        }
        ret.zyfw = volunteers.count;
        medias.forEach(item => {
            if (item.intro === '支部委员会') {
                ret.zbwyh++;
            }
            if (item.intro === '支部党员大会') {
                ret.zbdydh++;
            }
            if (item.intro === '党小组会') {
                ret.dxzh++;
            }
            if (item.intro === '党课') {
                ret.dk++;
            }
            if (item.intro === '主题党日') {
                ret.ztdr++;
            }
            if (item.intro === '组织生活会') {
                ret.zzshh++;
            }
            if (item.intro === '民主评议党员') {
                ret.mzpydy++;
            }
        })
        return ret;
    }
}
