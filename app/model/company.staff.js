"use strict";

/**
 * company表
 */
module.exports = class extends Model {

  async search(q) {
    let where = ' WHERE company_id=? AND status != 0';
    let params = [q.company_id];

    if (q.kw && q.kw.length < 5) {
      where += ' AND (`name` LIKE ? OR party_title LIKE ? OR position_name LIKE ?)';
      let kw = '%' + q.kw + '%'
      params.push(kw, kw, kw);
    }

    if (/^\d+$/.test(q.party_code)) {
      where += ' AND party_code=' + q.party_code;
    }

    if (/^\d+$/.test(q.sex)) {
      where += ' AND sex=' + q.sex;
    }

    let sql = "SELECT id, name, sex, party_code, party_title, status, position_name" +
      ' FROM company_staff' + where +
      " ORDER BY sequence,id" +
      ` LIMIT ${q.offset}, ${q.limit}`;
    return this.db().query(sql, {type: 'SELECT', replacements: params});
  }

  async query(q) {
    let where = ' WHERE company_id=? AND status != 0';
    let params = [q.company_id];

    if (q.kw && q.kw.length < 5) {
      where += ' AND (`name` LIKE ? OR party_title LIKE ? OR position_name LIKE ?)';
      let kw = '%' + q.kw + '%'
      params.push(kw, kw, kw);
    }

    if (/^\d+$/.test(q.party_code)) {
      where += ' AND party_code=' + q.party_code;
    }

    if (/^\d+$/.test(q.sex)) {
      where += ' AND sex=' + q.sex;
    }

    let total = -1;
    const db = this.db();

    return {
      async count() {
        let sql = "SELECT COUNT(*) AS total FROM company_staff" + where;
        let rows = await db.query(sql, {type: 'SELECT', replacements: params});
        return total = rows[0].total;
      },
      rows(offset, limit) {
        if (total === 0) return [];

        offset = /^\d+$/.test(offset) ? parseInt(offset) : 0;
        limit = /^\d+$/.test(limit) ? parseInt(limit) : 15;

        let sql = "SELECT id, headimg, name, sex, join_work," +
          " party_code, party_title, party_join, nation, education, birthday," +
          ' company_id, status, position_name, created, sequence' +
          ' FROM company_staff' + where +
          " ORDER BY sequence,id" +
          ` LIMIT ${q.offset}, ${q.limit}`;
        return db.query(sql, {type: 'SELECT', replacements: params});
      }
    }
  }

  async exists(companyId, field, value) {
    let sql = `SELECT id FROM company_staff WHERE company_id=? AND ${field}=?`;
    let rows = await this.db().query(sql, {type: 'SELECT', replacements: [companyId, value]});
    return rows.length > 0 ? rows[0].id : 0;
  }

  async create(data) {
    let now = helper.time();
    let {dataValues} = await this.ctx.db('company_staff').create({
      company_id: data.company_id,
      name: data.name,
      sex: data.sex,
      headimg: data.headimg,
      status: data.status,
      sequence: data.sequence,
      school_name: data.school_name,
      education: data.education,
      join_work: data.join_work,
      nation: data.nation,
      position_name: data.position_name,
      birthday: data.birthday,
      created: now,
      modified: now
    });
    return dataValues;
  }

  async update(id, data) {
    data.modified = helper.time();
    await this.ctx.db('company_staff').update(data, {where: {id: id}});
  }

  async getEditData(id) {
    let sql = "SELECT * FROM company_staff WHERE id=?";
    let [item] = await this.db().query(sql, {type: 'SELECT', replacements: [id]});

    if (item) {
      if (item.birthday == '0000-00-00') item.birthday = null;
      if (item.party_join == '0000-00-00') item.party_join = null;
      if (item.join_work == '0000-00-00') item.join_work = null;

      item.tag = item.tag != '' ? item.tag.split(',').map(id => {
        return parseInt(id);
      }) : [];
    }

    return item;
  }

  async getBeforeUpdate(id) {
    let sql = "SELECT company_id, dept_id FROM company_staff WHERE id=?";
    let rows = await this.db().query(sql, {type: 'SELECT', replacements: [id]});
    return rows[0];
  }

  getUpdateData(companyId, prevTime) {
    let sql = 'SELECT * FROM company_staff WHERE company_id=? AND modified>?';
    return this.db().query(sql, {type: 'SELECT', replacements: [companyId, prevTime]});
  }

  async getUserTags(id) {
    let sql = 'SELECT tag FROM company_staff WHERE id=?';
    let [data] = await this.db().query(sql, {type: 'SELECT', replacements: [id]});

    if (!data) return;

    return data.tag == '' ? [] : data.tag.split(',').map(id => {
      return parseInt(id);
    });
  }

  async setUserTags(id, list) {
    return this.update(id, {tag: list.join(',')});
  }

  async saveSort(list) {
    const db = this.db();
    await Promise.all(list.map(item => {
      return db.query(`UPDATE company_staff SET sequence=${item.val} WHERE id=${item.key}`, {type: 'UPDATE'});
    }));
  }

  /**
   * 性别统计
   */
  getSexCount(companyId) {
    let sql = `SELECT sex AS \`key\`, COUNT(*) AS val FROM company_staff WHERE company_id=${companyId} AND status=1 GROUP BY sex`;
    return this.db().query(sql, {type: 'SELECT'});
  }

  /**
   * 学历统计
   */
  getEducationCount(companyId) {
    let sql = `SELECT education AS \`key\`, COUNT(*) AS val FROM company_staff WHERE company_id=${companyId} AND status=1 GROUP BY education`;
    return this.db().query(sql, {type: 'SELECT'});
  }

  /**
   * 年龄统计
   */
  getAgeCount(companyId, rangeTime) {
    let one;
    let sql = rangeTime.map((date, index) => {
      one = `SELECT ${index} AS \`key\`, COUNT(*) AS val FROM company_staff WHERE company_id=${companyId} AND status=1 AND birthday`;
      if (date[0] && date[1]) {
        one += ` BETWEEN '${date[0]}' AND '${date[1]}'`;
      } else if (date[0]) {
        one += `>='${date[0]}'`;
      } else if (date[1]) {
        one += `<='${date[1]}'`;
      }

      return one;
    }).join(`\r\nUNION ALL\r\n`);

    return this.db().query(sql, {type: 'SELECT'});
  }

  /**
   * 党龄统计
   */
  getPartyAgeCount(companyId, rangeTime) {
    let one;
    let sql = rangeTime.map((date, index) => {
      one = `SELECT ${index} AS \`key\`, COUNT(*) AS val FROM company_staff WHERE company_id=${companyId} AND status=1 AND party_code=1 AND party_join`;
      if (date[0] && date[1]) {
        one += ` BETWEEN '${date[0]}' AND '${date[1]}'`;
      } else if (date[0]) {
        one += `>='${date[0]}'`;
      } else if (date[1]) {
        one += `<='${date[1]}'`;
      }

      return one;
    }).join(`\r\nUNION ALL\r\n`);

    return this.db().query(sql, {type: 'SELECT'});
  }
}