module.exports = {
  domain: '*',
  base: '/',
  routes: [
    {
      path: ':version(v\\d+)',
      middleware: ['access'],
      children: [
        // 登录账号
        { name: 'user.account.login', method: 'POST' },
        { name: 'user.account.register', method: 'POST' },
        { name: 'user.account.captcha', method: 'POST' },
        { name: 'user.account.check', method: 'POST' },
        { name: 'user.account.query', method: 'GET', access: 'user:account:info' },
        { name: 'user.account.create', method: 'POST' },
        { name: 'user.account.password', method: 'POST' },
        { name: 'user.account.headimg', method: 'POST' },
        { name: 'user.account.treeData', method: 'GET', access: 'user:account:info' },
        { name: 'user.account.unassign', method: 'GET', access: 'user:account:put' },
        { name: 'user.account.assignSub', method: 'POST', access: 'user:account:put' },
        { name: 'user.account.subUsers', method: 'GET' },
        { name: 'user.account.verify', method: 'POST', access: 'user:account:put' },
        { name: 'user.account.update', method: 'POST', access: 'user:account:put' },
        { name: 'user.account.isParentNode', method: 'GET' },
        { name: 'user.account.delete', method: 'POST', access: 'user:account:del' },

        // 公共数据
        { name: 'data.center.city', method: 'GET' },
        { name: 'data.center.update', method: 'GET' },
        { name: 'data.center.updateFromParent', method: 'GET' },
        { name: 'data.center.getDataByType', method: 'GET' },
        { name: 'data.center.getChannelData', method: 'GET' },
        { name: 'data.center.getMediaDataByChannel', method: 'GET' },
        // 菜单管理
        { name: 'sys.menu.query', method: 'GET', access: 'sys:menu:get' },
        { name: 'sys.menu.create', method: 'POST', access: 'sys:menu:add' },
        { name: 'sys.menu.update', method: 'POST', access: 'sys:menu:put' },
        { name: 'sys.menu.delete', method: 'POST', access: 'sys:menu:del' },

        // 角色管理
        { name: 'sys.role.query', method: 'GET', access: 'sys:role:get' },
        { name: 'sys.role.create', method: 'POST', access: 'sys:role:add' },
        { name: 'sys.role.update', method: 'POST', access: 'sys:role:put' },
        { name: 'sys.role.delete', method: 'POST', access: 'sys:role:del' },

        // 授权管理
        { name: 'sys.access.menu', method: 'GET', access: 2 },
        // 按钮权限
        { name: 'sys.access.nodes', method: 'GET', access: 2 },
        // 友情链接
        { name: 'friend.link.query', method: 'GET', access: 'friend:link:get' },
        { name: 'friend.link.create', method: 'POST', access: 'friend:link:add' },
        { name: 'friend.link.update', method: 'POST', access: 'friend:link:put' },
        { name: 'friend.link.delete', method: 'POST', access: 'friend:link:del' },
        { name: 'friend.link.sort', method: 'POST', access: 'friend:link:put' },
        // 数据字典
        { name: 'sys.dictionary.types', method: 'GET' },
        { name: 'sys.dictionary.items', method: 'GET' },
        { name: 'sys.dictionary.create', method: 'POST', access: 'sys:dict:add' },
        { name: 'sys.dictionary.update', method: 'POST', access: 'sys:dict:put' },
        { name: 'sys.dictionary.delete', method: 'POST', access: 'sys:dict:del' },
        { name: 'sys.dictionary.refresh', method: 'POST', access: 'sys:dict:put' },
        // 企业信息
        { name: 'company.info.get', method: 'GET', access: 'company:info:get' },
        { name: 'company.info.update', method: 'POST', access: 'company:info:put' },
        { name: 'company.info.profile', method: 'GET' },
        // 组织架构
        { name: 'company.dept.list', method: 'GET', access: 'company:dept:get' },
        { name: 'company.dept.create', method: 'POST', access: 'company:dept:add' },
        { name: 'company.dept.update', method: 'POST', access: 'company:dept:put' },
        { name: 'company.dept.delete', method: 'POST', access: 'company:dept:del' },
        // 员工管理
        { name: 'company.staff.search', method: 'GET', access: 'staff:get' },
        { name: 'company.staff.query', method: 'GET', access: 'staff:get' },
        { name: 'company.staff.get', method: 'GET', access: 'staff:get' },
        { name: 'company.staff.create', method: 'POST', access: 'staff:add' },
        { name: 'company.staff.update', method: 'POST', access: 'staff:put' },
        { name: 'company.staff.delete', method: 'POST', access: 'staff:del' },
        { name: 'company.staff.sort', method: 'POST', access: 'staff:put' },
        { name: 'company.staff.list', method: 'GET' },
        { name: 'company.staff.statistics', method: 'GET' },
        { name: 'company.staff.staffList', method: 'GET' },
        { name: 'company.staff.newJoinList', method: 'GET' },
        { name: 'company.staff.excellentList', method: 'GET' },
        { name: 'company.staff.groupList', method: 'GET' },
        { name: 'company.staff.profile', method: 'GET' },
        { name: 'company.staff.politicalBirthdayList', method: 'GET' },
        // 分组管理
        { name: 'company.staff.tag.list', method: 'GET', access: 'staff:tag:get' },
        { name: 'company.staff.tag.create', method: 'POST', access: 'staff:tag:add' },
        { name: 'company.staff.tag.update', method: 'POST', access: 'staff:tag:put' },
        { name: 'company.staff.tag.delete', method: 'POST', access: 'staff:tag:del' },
        { name: 'company.staff.tag.sort', method: 'POST', access: 'staff:tag:put' },
        // 党员阶段管理
        { name: 'company.staff.resume.list', method: 'GET' },
        { name: 'company.staff.resume.create', method: 'POST', access: 'staff:add' },
        { name: 'company.staff.resume.update', method: 'POST', access: 'staff:put' },
        { name: 'company.staff.resume.delete', method: 'POST', access: 'staff:del' },
        // 组织荣誉管理
        { name: 'company.honor.list', method: 'GET' },
        { name: 'company.honor.create', method: 'POST', access: 'company:honor:add' },
        { name: 'company.honor.update', method: 'POST', access: 'company:honor:put' },
        { name: 'company.honor.delete', method: 'POST', access: 'company:honor:del' },

        // 发展党员管理
        { name: 'company.recruit.staff.list', method: 'GET', access: 'staff:recruit:get' },
        { name: 'company.recruit.staff.create', method: 'POST', access: 'staff:recruit:add' },
        { name: 'company.recruit.staff.update', method: 'POST', access: 'staff:recruit:put' },
        { name: 'company.recruit.staff.delete', method: 'POST', access: 'staff:recruit:del' },
        { name: 'company.recruit.staff.statistics', method: 'GET' },

        // 志愿服务管理
        { name: 'company.staff.volunteer.list', method: 'GET' },
        { name: 'company.staff.volunteer.create', method: 'POST', access: 'staff:volunteer:add' },
        { name: 'company.staff.volunteer.update', method: 'POST', access: 'staff:volunteer:put' },
        { name: 'company.staff.volunteer.delete', method: 'POST', access: 'staff:volunteer:del' },
        // 频道管理
        { name: 'media.channel.list', method: 'GET', access: 'media:channel:get' },
        { name: 'media.channel.create', method: 'POST', access: 'media:channel:add' },
        { name: 'media.channel.update', method: 'POST', access: 'media:channel:put' },
        { name: 'media.channel.delete', method: 'POST', access: 'media:channel:del' },
        { name: 'media.channel.sort', method: 'POST', access: 'media:channel:put' },
        { name: 'media.channel.listByPid', method: 'GET' },
        // 媒体资源
        { name: 'media.upload.config', method: 'GET' },
        { name: 'media.upload.request', method: 'POST' },
        { name: 'media.upload.submit', method: 'POST' },
        { name: 'media.upload.success', method: 'POST' },
        { name: 'media.upload.images', method: 'GET' },
        { name: 'media.upload.privateUrl', method: 'POST' },
        { name: 'media.item.save', method: 'POST', access: 'media:item:put' },
        { name: 'media.item.get', method: 'GET' },
        { name: 'media.item.getMediaFlag', method: 'GET' },
        { name: 'media.item.getByItemId', method: 'GET' },
        { name: 'media.item.info', method: 'GET', access: 'media:item:get' },
        { name: 'media.item.publish', method: 'POST', access: 'media:item:put' },
        { name: 'media.item.offline', method: 'POST', access: 'media:item:put' },
        { name: 'media.item.submitAudit', method: 'POST', access: 'media:item:operation' },
        { name: 'media.item.audit', method: 'POST', access: 'media:item:audit' },
        { name: 'media.item.auditList', method: 'GET', access: 'media:item:audit' },
        { name: 'media.item.share', method: 'GET' },
        { name: 'media.item.preview', method: 'POST' },
        { name: 'media.item.query', method: 'GET', access: 'media:item:get' },
        { name: 'media.item.delete', method: 'POST', access: 'media:item:del' },
        { name: 'media.item.search', method: 'GET', access: 'media:item:get' },
        { name: 'media.item.subList', method: 'GET' },
        { name: 'media.item.pList', method: 'GET' },
        { name: 'media.item.userTabs', method: 'GET' },

        { name: 'media.item.queryByChannel', method: 'GET' },
        { name: 'media.item.getByChannel', method: 'GET' },
        { name: 'media.item.branchStatistics', method: 'GET' },

        // 通知管理
        { name: 'sys.notice.query', method: 'GET', access: 'sys.notice:get' },
        { name: 'sys.notice.create', method: 'POST', access: 'sys.notice:add' },
        { name: 'sys.notice.update', method: 'POST', access: 'sys.notice:put' },
        { name: 'sys.notice.delete', method: 'POST', access: 'sys.notice:del' },
        { name: 'sys.notice.publish', method: 'POST', access: 'sys.notice:put' },
        { name: 'sys.notice.get', method: 'GET' },
        { name: 'sys.notice.getByUserId', method: 'GET' },

        // 轮播管理
        { name: 'home.carousel.query', method: 'GET', access: 'home:carousel:get' },
        { name: 'home.carousel.create', method: 'POST', access: 'home:carousel:add' },
        { name: 'home.carousel.update', method: 'POST', access: 'home:carousel:put' },
        { name: 'home.carousel.delete', method: 'POST', access: 'home:carousel:del' },
        { name: 'home.carousel.get', method: 'GET', access: 'home:carousel:get' },
        { name: 'home.carousel.list', method: 'GET' },


        // 小区管理
        { name: 'region.info.query', method: 'GET' },
        { name: 'region.info.create', method: 'POST', access: 'region:info:add' },
        { name: 'region.info.update', method: 'POST', access: 'region:info:put' },
        { name: 'region.info.delete', method: 'POST', access: 'region:info:del' },
        { name: 'region.info.get', method: 'GET', access: 'region:info:get' },
        { name: 'region.info.select', method: 'GET', access: 'region:info:get' },

        // 经济结构
        { name: 'economic.info.query', method: 'GET' },
        { name: 'economic.info.create', method: 'POST', access: 'economic:info:add' },
        { name: 'economic.info.update', method: 'POST', access: 'economic:info:put' },
        { name: 'economic.info.delete', method: 'POST', access: 'economic:info:del' },
        { name: 'economic.info.get', method: 'GET', access: 'economic:info:get' },
        { name: 'economic.info.select', method: 'GET', access: 'economic:info:get' },

        // ioc居民管理
        { name: 'ioc.user.query', method: 'GET', access: 'ioc:user:get' },
        { name: 'ioc.user.create', method: 'POST', access: 'ioc:user:add' },
        { name: 'ioc.user.update', method: 'POST', access: 'ioc:user:put' },
        { name: 'ioc.user.delete', method: 'POST', access: 'ioc:user:del' },
        { name: 'ioc.user.get', method: 'GET', access: 'ioc:user:get' },
        { name: 'ioc.user.batchInsert', method: 'POST', access: 'ioc:user:add' },
        { name: 'ioc.user.statisticalData', method: 'GET' },
        { name: 'ioc.user.specialUserData', method: 'GET' },
        { name: 'ioc.user.famousUserData', method: 'GET' },
        { name: 'ioc.user.focusUserData', method: 'GET' },

        // ioc 经济结构
        { name: 'ioc.economic.query', method: 'GET' },

        // ioc 社区概况
        { name: 'ioc.community.profile.queryVillageInfo', method: 'GET' },
        { name: 'ioc.community.profile.queryHouseholdInfo', method: 'GET' },
        { name: 'ioc.community.profile.queryNumByDomicileType', method: 'GET' },
        { name: 'ioc.community.profile.queryNumByPersonnelType', method: 'GET' },
        { name: 'ioc.community.profile.queryPersonnelInfo', method: 'GET' },

        //社区人员
        { name: 'ioc.community.personnel.queryAgeStructureInfo', method: 'GET' },
        { name: 'ioc.community.personnel.queryStudents', method: 'GET' },
        { name: 'ioc.community.personnel.queryFocusCrowd', method: 'GET' },
        { name: 'ioc.community.personnel.querySpecialPopulationsInfo', method: 'GET' },
        { name: 'ioc.community.personnel.querySoldierInfo', method: 'GET' },
        { name: 'ioc.community.personnel.queryFamousInfo', method: 'GET' },


         // 活动及通知
         {name: 'media.activity.list', method: 'GET'},
         {name: 'media.activity.create', method: 'POST'},
         {name: 'media.activity.update', method: 'POST'},
         {name: 'media.activity.delete', method: 'POST'},

         // 活动浏览记录
         {name: 'media.activity.addHistory', method: 'GET'},
         {name: 'media.activity.getHistory', method: 'GET'},
         
         // 活动 带状态
         {name: 'media.activity.listStatus', method: 'GET'},
         
         // 活动报名人列表
         {name: 'media.activity.userSignUpList', method: 'GET'},
         {name: 'media.activity.getSignUpsByActivityId', method: 'GET'},
         
         // 活动报名
         {name: 'media.activity.signUp', method: 'POST'},
         // 状态查询
         {name: 'media.activity.checkUserSignedUp', method: 'POST'},
         // 活动签到
         {name: 'media.activity.checkIn', method: 'POST'},
         // 活动签退
         {name: 'media.activity.checkOut', method: 'POST'},
         // 删除活动报名人
         {name: 'media.activity.destroySignUp', method: 'POST'},

          // 每天进入麓点通记录
          {name: 'login.record.list', method: 'GET'},
          {name: 'login.record.create', method: 'POST'},

          // 志愿者一般人申请
          {name: 'mini.volunteers.list', method: 'GET'},
          {name: 'mini.volunteers.listAll', method: 'GET'},
          {name: 'mini.volunteers.query', method: 'GET'},
          {name: 'mini.volunteers.create', method: 'POST'},
          {name: 'mini.volunteers.update', method: 'POST'},
          {name: 'mini.volunteers.delete', method: 'POST'},
          {name: 'mini.volunteers.audit', method: 'POST'},

          // 志愿者中小学生申请
          {name: 'mini.volunteers.child.list', method: 'GET'},
          {name: 'mini.volunteers.child.query', method: 'GET'},
          {name: 'mini.volunteers.child.create', method: 'POST'},
          {name: 'mini.volunteers.child.update', method: 'POST'},
          {name: 'mini.volunteers.child.delete', method: 'POST'},
          {name: 'mini.volunteers.child.audit', method: 'POST'},

          // 志愿者活动计划
          {name: 'mini.volunteer.activity.plan.query', method: 'GET'},
          {name: 'mini.volunteer.activity.plan.list', method: 'GET'},
          {name: 'mini.volunteer.activity.plan.create', method: 'POST'},
          {name: 'mini.volunteer.activity.plan.update', method: 'POST'},
          {name: 'mini.volunteer.activity.plan.delete', method: 'POST'},


        // 首页配置
        { name: 'app.home.get', method: 'GET', access: 'app:home:get' },
        { name: 'app.home.set', method: 'POST', access: 'app:home:put' },
        // APP版本
        { name: 'app.version.newest', method: 'GET' },
      ],
    },
  ],
}
