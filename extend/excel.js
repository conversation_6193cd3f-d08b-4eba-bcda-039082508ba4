"use strict";

module.exports = class EXCEL
{

	// 导出excel
	async exportExcle(data,cols,fileName = false){
		const nodeExcel = require('excel-export');
		let conf = {
			name: "mysheet", //表格名
			rows: data, //填充数据
			cols: cols
		};
		let result = nodeExcel.execute(conf);
		let res = new Buffer(result, 'binary');
		let name = fileName ? fileName : helper.date('YYYY-MM-DD-HH-mm') + '.xlsx';
		this.ctx.set('Content-Type', 'application/vnd.openxmlformats');
		this.ctx.set("Content-Disposition", "attachment; filename=" + name);
		this.ctx.body = res;
	}
}
