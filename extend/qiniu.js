const qiniu = require('qiniu');
const Pili = require('piliv2');
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const MAC = Symbol('mac');
const CREDENTIALS = Symbol('credentials');
const UPLOADCONFIG = Symbol('uploadConfig');
const BUCKETMANAGER = Symbol('bucketManager');
const OPERMANAGER = Symbol('operManager');

class QiNiu {

  static instance = {};

  static getInstance(cnf) {
    if (!QiNiu.instance[cnf.bucket]) {
      QiNiu.instance[cnf.bucket] = new QiNiu(cnf);
    }
    return QiNiu.instance[cnf.bucket];
  }

  constructor(cnf) {
    this.cnf = cnf;
  }

  get mac() {
    if (!this[MAC]) {
      this[MAC] = new qiniu.auth.digest.Mac(this.cnf.accessKey, this.cnf.secretKey);
    }
    return this[MAC];
  }

  get credentials() {
    if (!this[CREDENTIALS]) {
      this[CREDENTIALS] = new Pili.Credentials(this.cnf.accessKey, this.cnf.secretKey);
    }
    return this[CREDENTIALS];
  }

  get uploadConfig() {
    if (!this[UPLOADCONFIG]) {
      this[UPLOADCONFIG] = new qiniu.conf.Config({
        zone: qiniu.zone['Zone_' + this.cnf.zone], // 空间对应的机房
        useHttpsDomain: false, // 是否使用https域名
        useCdnDomain: false // 上传是否使用cdn加速
      });
    }
    return this[UPLOADCONFIG];
  }

  get bucketManager() {
    if (!this[BUCKETMANAGER]) {
      this[BUCKETMANAGER] = new qiniu.rs.BucketManager(this.mac, this.uploadConfig);
    }
    return this[BUCKETMANAGER];
  }

  get operManager() {
    if (!this[OPERMANAGER]) {
      let config = new qiniu.conf.Config();
      config.zone = qiniu.zone['Zone_' + this.cnf.zone];
      this[OPERMANAGER] = new qiniu.fop.OperationManager(this.mac, config);
    }
    return this[OPERMANAGER];
  }

  /**
   * 发送单条短信
   */
  sendSingleMessage(template, mobile, params) {
    let id;
    switch (template) {
      case 'login':
        id = '1280422320422273024';
        break;
      case 'unbind':
        id = '1280422212645437440';
        break;
      case 'password':
        id = '1280422051441557504';
        break;
    }

    return new Promise((resolve, reject) => {
      qiniu.sms.message.sendSingleMessage({
        template_id: id,
        mobile: mobile,
        parameters: params
      }, this.mac, function (err, data, res) {
        if (err) return reject(err);

        try {
          data = JSON.parse(data.toString());
        } catch (e) {
          return reject('云短信解析失败');
        }

        if (res.statusCode === 200) {
          resolve(data.message_id);
        } else {
          reject(data.message);
        }
      });
    });
  }

  /**
   * 生成客户端上传凭证
   */
  uploadToken(callbackUrl, callbackBody) {
    let body = {
      key: '$(key)',
      hash: '$(etag)',
      name: '$(fname)',
      size: '$(fsize)',
      mime: '$(mimeType)',
      bucket: '$(bucket)'
    };

    if (callbackBody) {
      for (let field in callbackBody) {
        body[field] = callbackBody[field];
      }
    }

    body = JSON.stringify(body);

    let putPolicy = new qiniu.rs.PutPolicy({
      // 空间名称
      scope: this.cnf.bucket,
      // 凭证有效期
      expires: 3600,
      // 上传后七牛返回的信息格式
      // returnBody: body,
      // 上传后七牛通知服务器结果
      callbackUrl: callbackUrl,
      callbackBody: body,
      callbackBodyType: 'application/json'
    });

    return putPolicy.uploadToken(this.mac);
  }

  isQiniuCallback(requestURI, reqBody, callbackAuth) {
    return callbackAuth === qiniu.util.generateAccessToken(this.mac, requestURI, reqBody);
  }

  /**
   * 上传本地文件
   * @param local - 本地文件名称
   * @param server - 存储路径（不能以“/”开头）
   * @param token - 上传凭证规则
   */
  async uploadFile(local, server, token) {
    let {cnf} = this;
    let loader = new qiniu.form_up.FormUploader(this.uploadConfig);
    await this.runUpload(loader, local, server, token || this.uploadToken());
    return 'http' + (cnf.https ? 's' : '') + '://' + cnf.domain + '/' + server;
  }

  /**
   * 执行文件上传
   */
  runUpload(loader, local, server, token) {
    let putExtra = new qiniu.form_up.PutExtra();
    return new Promise((resolve, reject) => {
      loader.putFile(token, server, local, putExtra, function (err, data, res) {
        if (err) {
          reject(err);
        } else if (res.statusCode == 200) {
          resolve(data.key);
        } else {
          reject(data.error);
        }
      });
    });
  }

  /**
   * 上传目录
   * @param source 本地文件夹
   * @param target 目标文件夹
   */
  uploadDir(source, target, token, loader) {
    if (!token) token = this.uploadToken();
    if (!loader) loader = new qiniu.form_up.FormUploader(this.uploadConfig);

    return new Promise((resolve, reject) => {
      fs.readdir(source, async (err, files) => {
        if (err) reject('遍历文件夹失败');

        let name, file, stat;

        try {
          for (let i = 0, len = files.length; i < len; i++) {
            name = files[i];
            file = path.join(source, name);
            stat = fs.statSync(file);

            if (stat && stat.isDirectory()) {
              await this.uploadDir(file, target + name + '/', token, loader);
            } else {
              await this.runUpload(loader, file, target + name, token);
            }
          }
          resolve();
        } catch (e) {
          reject(e);
        }
      });
    });
  }

  /**
   * 授权客户端上传
   */
  authClientUpload(prefix, callbackUrl, callbackBody) {
    let {cnf} = this;
    let protocol = `http${cnf.https ? 's' : ''}://`;
    let remote;

    switch (cnf.zone) {
      case 'z0':
        remote = 'upload.qiniup.com';
        break;
      case 'z1':
        remote = 'upload-z1.qiniup.com';
        break;
      case 'z2':
        remote = 'upload-z2.qiniup.com';
        break;
      case 'cn-east-2':
        remote = 'up-cn-east-2.qiniup.com';
        break;
      default:
        throw new Error('不支持的zone');
    }

    return {
      type: 1,
      remote: protocol + remote + '/',
      origin: protocol + cnf.domain + '/',
      prefix: prefix,
      zone: cnf.zone,
      token: this.uploadToken(callbackUrl, callbackBody)
    };
  }

  /**
   * 生成直播推流地址
   */
  generateLivePush(hubName, streamName, expireIn) {
    let domain = this.cnf.livePush;
    return expireIn ? Pili.publishURL(
      this.credentials,
      domain, // 推流域名
      hubName, // 直播空间名称（固定死，七牛云后台设置）
      streamName.toString(), // 流名
      expireIn // 推流地址有效期
    ) : `rtmp://${domain}/${hubName}/${streamName}`;
  }

  /**
   * 生成直播拉流地址
   */
  generateLivePull(hubName, streamName, transcode) {
    let {cnf} = this;
    let prefix = `http${cnf.https ? 's' : ''}://`;
    let suffix = '/' + hubName + '/' + streamName;
    let rtmp = 'rtmp://' + cnf.livePull + suffix;
    let m3u8 = prefix + cnf.liveHLS + suffix;
    let flv = prefix + cnf.liveHDL + suffix;

    if (!transcode || transcode.length == 0) {
      return {
        rtmp: rtmp,
        m3u8: m3u8 + '.m3u8',
        flv: flv + '.flv'
      }
    }

    let resolution = [];

    if (transcode.indexOf('480') !== -1) {
      resolution.push({
        text: '标清',
        quality: 1,
        resolution: '480',
        rtmp: rtmp + '@480p',
        m3u8: m3u8 + '@480p.m3u8',
        flv: flv + '@480p.flv'
      });
    }

    if (transcode.indexOf('720') !== -1) {
      resolution.push({
        text: '高清',
        quality: 2,
        resolution: '720',
        rtmp: rtmp + '@720p',
        m3u8: m3u8 + '@720p.m3u8',
        flv: flv + '@720p.flv'
      });
    }

    resolution.push({
      text: '超清',
      quality: 3,
      resolution: '1080',
      rtmp: rtmp,
      m3u8: m3u8 + '.m3u8',
      flv: flv + '.flv'
    });

    return resolution;
  }

  request(method, path, data) {
    let dataString = JSON.stringify(data);
    let options = {
      host: 'pili.qiniuapi.com',
      //port: this.port,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': dataString.length
      }
    };

    options.headers['Authorization'] = this.credentials.generateAccessToken(options, dataString);

    return axios({
      method: options.method,
      url: 'http://' + options.host + options.path,
      data: data,
      headers: options.headers
    }).then(res => {
      return res.data;
    }).catch(e => {
      let res = e.response;
      return Promise.reject({
        status: res.status,
        error: res.data.error
      })
    });
  }

  /**
   * 创建直播流
   */
  createLiveStream(hubName, streamName) {
    let hub = new Pili.Hub(this.credentials, hubName);

    return new Promise((resolve, reject) => {
      hub.createStream(streamName.toString(), function (err, stream) {
        return err ? reject('创建直播流失败：' + err.errorCode) : resolve();
      })
    })
  }

  /**
   * 创建直播转码流
   */
  createLiveTranscode(hub, stream, transcode) {
    if (!transcode || transcode.length == 0) {
      return;
    }

    stream = Buffer.from(stream.toString()).toString('base64');
    let data = {converts: transcode.map(r => r + 'p')};
    let path = '/v2/hubs/' + hub + '/streams/' + stream + '/converts';
    return this.request('POST', path, data);
  }

  /**
   * 获取直播流
   */
  getLiveStream(hubName, streamName) {
    let hub = new Pili.Hub(this.credentials, hubName);
    return hub.newStream(streamName);
  }

  /**
   * 保存直播回放
   */
  saveLivePlayback(hubName, streamName, startTime, endTime, fname) {
    let stream = this.getLiveStream(hubName, streamName.toString());
    let prams = {start: startTime, end: endTime, format: 'm3u8', fname: fname, pipeline: this.cnf.pipeline};

    return new Promise((resolve, reject) => {
      stream.savePlayback(prams, function (err, filename) {
        err ? reject('直播录制失败：' + err.errorCode) : resolve(filename);
      });
    })
  }

  /**
   * 获取直播状态
   */
  getLiveStatus(hubName, streamName) {
    let stream = this.getLiveStream(hubName, streamName);
    return this.getLiveStreamStatus(stream);
  }

  /**
   * 获取直播流状态
   * startAt: 1586847277,
   * clientIP: '***************:1772',
   * bps: 2211696,
   * fps: { audio: 45, video: 25, data: 0 }
   */
  getLiveStreamStatus(stream) {
    return new Promise((resolve, reject) => {
      stream.liveInfo(function (err, status) {
        if (err) {
          switch (err.httpCode) {
            case 404:
              return resolve('unfound');
            case 619:
              return resolve('finished');
            default:
              return reject('获取直播状态失败：' + err.errorCode);
          }
        } else {
          resolve('pushing');
        }
      })
    })
  }

  /**
   * 获取推流记录
   */
  getLiveHistory(hubName, streamName, startTime, endTime) {
    let stream = this.getLiveStream(hubName, streamName);
    return this.getLiveStreamHistory(stream, startTime, endTime);
  }

  /**
   * 获取直播流记录
   */
  getLiveStreamHistory(stream, startTime, endTime) {
    return new Promise((resolve, reject) => {
      stream.publishHistory({start: startTime, end: endTime}, function (err, list) {
        if (err) {
          switch (err.httpCode) {
            case 612:
              return resolve([]);
            default:
              return reject('获取直播状态失败：' + err.errorCode);
          }
        } else {
          resolve(list);
        }
      })
    })
  }

  /**
   * 获取直播信息
   */
  async getLiveInfo(hubName, streamName, startTime, endTime) {
    let stream = this.getLiveStream(hubName, streamName);
    let [status, history] = await Promise.all([
      this.getLiveStreamStatus(stream),
      this.getLiveStreamHistory(stream, startTime, endTime)
    ]);
    return {status, history};
  }

  /**
   * 更改文件状态
   */
  setFileStatus(key, status) {
    if (key.startsWith('http')) {
      key = key.split('//')[1];
    }

    return new Promise((resolve, reject) => {
      this.bucketManager.updateObjectStatus(this.cnf.bucket, key, status, function (err) {
        err ? reject(err.errorCode) : resolve();
      });
    });
  }

  /**
   * 授权访问资源
   */
  privateUrl(key, expireIn = 1800) {
    if (key.startsWith('http')) {
      key = key.split('//')[1];
    }

    let {cnf} = this;
    let domain = `http${cnf.https ? 's' : ''}://${cnf.domain}`;
    let deadline = parseInt(Date.now() / 1000) + expireIn; // 过期时间
    return this.bucketManager.privateDownloadUrl(domain, key, deadline);
  }

  /**
   * 视频转码
   * resolution：480x320
   */
  videoTranscode(resolution, source, target) {
    let {operManager, cnf} = this;

    //处理指令集合
    let fops = [
      'avthumb/mp4/s/' + resolution + '/vb/1m|saveas/' + qiniu.util.urlsafeBase64Encode(cnf.bucket + ':' + source),
    ];

    if (!target) {
      let i = source.lastIndexOf('.') - 1;
      target = source.substr(0, i) + resolution + source.substr(i);
    }

    let options = {
      'notifyURL': 'http://api.example.com/pfop/callback',
      'force': true
    };

    //持久化数据处理返回的是任务的persistentId，可以根据这个id查询处理状态
    return new Promise((resolve, reject) => {
      operManager.pfop(cnf.bucket, target, fops, cnf.pipeline, options, function (err, respBody, respInfo) {
        err ? reject('创建转码任务失败：' + err) : respInfo.statusCode == 200 ? resolve({
          id: respBody.persistentId,
          source: source,
          target: target,
          resolution: resolution
        }) : reject('创建转码任务失败：' + respInfo.statusCode);
      });
    });
  }
}

module.exports = QiNiu;
