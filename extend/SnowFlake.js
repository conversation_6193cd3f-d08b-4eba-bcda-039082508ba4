/**
 * 雪花算法
 */
class SnowFlake {

  // 第一次生成ID时间，一旦使用不可修改
  get twepoch() {
    return 1588262400000n;
  }

  // 毫秒内自增位
  get sequenceBits() {
    return 12n;
  }

  // 机器标识位数
  get workerIdBits() {
    return 5n;
  }

  get datacenterIdBits() {
    return 5n;
  }

  constructor(workerId = 0, datacenterId = 0, sequence = 0) {
    this.workerId = BigInt(workerId);// 工作机器ID(0~31)
    this.datacenterId = BigInt(datacenterId);// 数据中心ID(0~31)
    this.sequence = BigInt(sequence);// 毫秒内序列(0~4095)
    this.lastTimestamp = -1n; // 上次生产id时间戳
    this.maxWorkerId = -1n ^ (-1n << this.workerIdBits); // 机器ID最大值31
    this.maxDatacenterId = -1n ^ (-1n << this.datacenterIdBits); // 数据中心ID最大值31
    this.sequenceMask = -1n ^ (-1n << this.sequenceBits); // 生成序列的掩码4095
    this.workerIdShift = this.sequenceBits; // 机器ID偏左移12位
    this.datacenterIdShift = this.sequenceBits + this.workerIdBits; // 数据中心ID左移17位
    this.timestampLeftShift = this.sequenceBits + this.workerIdBits + this.datacenterIdBits; // 时间毫秒左移22位

    if (workerId > this.maxWorkerId || workerId < 0n) {
      throw new Error(`worker Id can't be greater than ${this.maxWorkerId} or less than 0`);
    }

    if (datacenterId > this.maxDatacenterId || datacenterId < 0n) {
      throw new Error(`datacenter Id can't be greater than ${this.maxDatacenterId} or less than 0`);
    }
  }

  nextId() {
    let timestamp = this.timeGen();

    if (timestamp < this.lastTimestamp) {
      let diffTimestamp = this.lastTimestamp - timestamp;
      throw new Error(`Clock moved backwards.  Refusing to generate id for ${diffTimestamp} milliseconds`);
    }

    if (this.lastTimestamp == timestamp) {
      this.sequence = (this.sequence + 1n) & this.sequenceMask;

      if (this.sequence == 0n) {
        timestamp = this.tilNextMillis(this.lastTimestamp);
      }
    } else {
      this.sequence = 0n;
    }

    this.lastTimestamp = timestamp;
    return (((timestamp - this.twepoch) << this.timestampLeftShift) |
      (this.datacenterId << this.datacenterIdShift) |
      (this.workerId << this.workerIdShift) |
      this.sequence).toString();
  }

  tilNextMillis(lastTimestamp) {
    let timestamp = this.timeGen();
    while (timestamp <= lastTimestamp) {
      timestamp = this.timeGen();
    }
    return timestamp;
  }

  timeGen() {
    return BigInt(Date.now());
  }
}

let snowFlake = new SnowFlake();
let id;

for (let i = 0; i < 10; i++) {
  id = snowFlake.nextId();
  console.log(id.length, id);
}