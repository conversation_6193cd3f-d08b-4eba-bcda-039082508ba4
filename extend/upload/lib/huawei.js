const BaseStorage = require('./base');
const axios = require('axios');
const crypto = require('crypto');

class HuaWeiStorage extends BaseStorage {

  async getToken() {
    // 需要在控制台设置
    // SETX NODE_OPTIONS '--max-http-header-size=16384'

    const key = 'huaweitoken:' + this.cnf.username;

    let val = await this.getCache(key);

    if (!!val) return val;

    val = await axios({
      url: 'https://iam.myhuaweicloud.com/v3/auth/tokens?nocatalog=true',
      method: 'post',
      responseType: '',
      data: {
        auth: {
          identity: {
            methods: ['password'],
            password: {
              user: {
                domain: {name: this.cnf.username},
                name: this.cnf.username,
                password: this.cnf.password
              }
            }
          },
          scope: {
            project: {name: this.cnf.zone}
          }
        }
      }
    }).then(res => {
      return res.headers['x-subject-token'];
    });

    await this.setCache(key, val);

    return val;
  }

  /**
   * 上传策略签名
   */
  async getPolicyBase64(params) {
    const token = await this.getToken();

    let policyJson = JSON.stringify({
      'expiration': helper.gmt_iso8601(parseInt(Date.now() / 1000) + params.expire),
      'conditions': [
        {'x-obs-acl': 'public-read'},
        {'x-obs-security-token': token},
        {'bucket': this.cnf.bucket},
        ['starts-with', '$key', params.key]
      ]
    });

    return Buffer.from(policyJson).toString('base64');
  }

  async policyToSignature(base64) {
    return crypto.createHmac('sha1', this.cnf.secretKey).update(base64).digest('base64');
  }

  /**
   * 授权客户端上传
   */
  async authClientUpload(key) {
    return {
      accessKey: this.cnf.accessKey,
      policy: await this.getPolicy({key: key, expire: 3600}),
      acl: 'public-read',
      signature: '',
    }
  }
}

module.exports = HuaWeiStorage;