const Local = require('./lib/local');
const QiNiu = require('./lib/qiniu');
const HuaWei = require('./lib/huawei');
const AliYun = require('./lib/aliyun');

module.exports = class Upload {

  static getInstance(ctx) {
    switch (ctx.env.upload.type) {
      case 1: // 七牛云存储
        return QiNiu.getInstance(ctx);
      case 2: // 阿里云存储
        return AliYun.getInstance(ctx);
      case 3: // 华为云存储
        return new HuaWei(ctx);
      default: // 本机存储
        return Local.getInstance(ctx);
    }
  }

}