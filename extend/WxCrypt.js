"use strict";

const crypto = require('crypto');

class WxCrypt {

  constructor(opts) {
    this.token = opts.token;
    this.appid = opts.appid;
    this.aesKey = Buffer.from(opts.aeskey + '=', 'base64');
    this.IV = this.aesKey.slice(0, 16)
  }

  encrypt(xml) {
    let random16 = crypto.pseudoRandomBytes(16);
    let msg = Buffer.from(xml);
    let msgLength = Buffer.alloc(4);
    msgLength.writeUInt32BE(msg.length, 0);

    let corpId = Buffer.from(this.appid);
    let raw_msg = Buffer.concat([random16, msgLength, msg, corpId]);
    let cipher = crypto.createCipheriv('aes-256-cbc', this.aesKey, this.IV);
    cipher.setAutoPadding(false);//重要，autopadding填充的内容无法正常解密
    raw_msg = this.PKCS7Encode(raw_msg);

    return Buffer.concat([cipher.update(/*encoded*/raw_msg), cipher.final()]).toString('base64');
  }

  decrypt(text) {
    let decipher = crypto.Decipheriv('aes-256-cbc', this.aesKey, this.IV);
    decipher.setAutoPadding(false);//重要

    let decipheredBuff = Buffer.concat([decipher.update(text, 'base64'), decipher.final()]);
    decipheredBuff = this.PKCS7Decode(decipheredBuff);

    let len_netOrder_corpid = decipheredBuff.slice(16);
    let msg_len = len_netOrder_corpid.slice(0, 4).readUInt32BE(0);

    return len_netOrder_corpid.slice(4, msg_len + 4).toString('utf-8');
  }

  PKCS7Decode(buff) {
    let padContent = buff[buff.length - 1];
    if (padContent < 1 || padContent > 32) {
      padContent = 0;
    }

    return buff.slice(0, buff.length - padContent)
  }

  PKCS7Encode(buff) {
    let blockSize = 32;
    let needPadLen = 32 - buff.length % 32;
    if (needPadLen == 0) {
      needPadLen = blockSize;
    }
    let pad = new Buffer(needPadLen);
    pad.fill(needPadLen);
    return Buffer.concat([buff, pad]);
  }

  validSignature(signature, timestamp, nonce) {
    let str = [this.token, timestamp, nonce].sort().join('');
    let sha = crypto.createHash('sha1');

    try {
      return sha.update(str).digest('hex') === signature;
    } catch (e) {
      return false;
    }
  }
}

module.exports = WxCrypt;