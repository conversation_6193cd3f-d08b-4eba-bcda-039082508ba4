"use strict";

const crypto = require('crypto');
const moment = require('moment');
const BigNumber = require('bignumber.js');
const OSS = require('ali-oss');

module.exports = class AliOss {

    constructor (options) {
        this.options = Object.assign({
            protocol: 'https',
            location: 'oss-cn-beijing',
            internal: false,
            secure: false,
            timeout: 60000, // 60s
            bucket: null,
            endpoint: null,
            cname: false,
            isRequestPay: false
        }, options);
    }

    /**
     * 授权客户端上传
     */
    authClientUpload (path, opt) {
        if (path.startsWith('/')) {
            path = path.substr(1);
        }

        let oss = this.options;
        opt = Object.assign({size: 307200, expire: 600}, opt || {});

        let expire = helper.time() + opt.expire;

        /*
        let $callback = Buffer.from(JSON.stringify({
          callbackUrl : 'https://api.diasiatvr.com/media.callback',
          callbackBody : 'bucket=${bucket}&size=${size}&object=${object}',
          callbackBodyType : "application/x-www-form-urlencoded"
        })).toString('base64');
        */

        let $arr = JSON.stringify({
            'expiration': helper.gmt_iso8601(expire),
            'conditions': [
                {'bucket': oss.bucket},
                //{'callback': $callback},
                ['content-length-range', 0, opt.size],
                ['starts-with', '$key', path]
            ]
        });

        let $policy = Buffer.from($arr).toString('base64');
        let $signature = crypto.createHmac('sha1', oss.accessKeySecret).update($policy).digest("base64");

        return {
            type: 1,
            accessid: oss.accessKeyId,
            remote: this.getLocationUrl(),
            policy: $policy,
            //callback: $callback,
            signature: $signature,
            expire: expire,
            prefix: path,
            size: opt.size,
            domain: oss.domain,
            protocol: oss.protocol
        };
    }

    getLocationUrl (params = {}) {
        let opts = this.options;
        let bucket = params.bucket || opts.bucket;
        let region = params.region || params.location || opts.location;
        return opts.protocol + '://' + bucket + '.' + region + '.aliyuncs.com';
    }

    getReqUrl (params) {
        let opts = this.options;
        let bucket = params.bucket || opts.bucket;
        let region = params.region || params.location || opts.location;
        return opts.protocol + '://' + bucket + '.' + region + '.aliyuncs.com';
    }

    /**
     * 给文件签名
     */
    sign (url, expiresIn) {
        if (!url) {
            return '';
        }

        let oss = this.options;
        let path;

        if (url.startsWith('http')) {
            path = url.split('/');
            if (path[2] !== oss.domain) {
                return url;
            }

            path = '/' + path.slice(3).join('/');
        } else {
            path = url;
        }

        let expires = parseInt(Date.now() / 1000) + (expiresIn || 180);
        let policy = "GET\n\n\n" + expires + "\n\/" + oss.bucket + path.replace(/\//g, "\/");
        let signature = crypto.createHmac('sha1', oss.accessKeySecret).update(policy).digest("base64");

        return oss.protocol + '://' + oss.domain + path + '?' +
            'OSSAccessKeyId=' + encodeURIComponent(oss.accessKeyId) + '&' +
            'Expires=' + expires + '&' +
            'Signature=' + encodeURIComponent(signature);
    }

    buildCanonicalizedResource (resourcePath, params) {
        let canonicalizedResource = `${resourcePath}`;
        let separatorString = '?';

        if (params instanceof String && params.trim() !== '') {
            canonicalizedResource += separatorString + params;
        } else if (params instanceof Array) {
            params.sort();
            canonicalizedResource += separatorString + params.join('&');
        } else if (params) {
            let compareFunc = (entry1, entry2) => {
                if (entry1[0] > entry2[0]) {
                    return 1;
                } else if (entry1[0] < entry2[0]) {
                    return -1;
                }
                return 0;
            };

            let processFunc = (key) => {
                canonicalizedResource += separatorString + key;
                if (params[key]) {
                    canonicalizedResource += `=${params[key]}`;
                }
                separatorString = '&';
            };

            Object.keys(params).sort(compareFunc).forEach(processFunc);
        }

        return canonicalizedResource;
    }

    canonicalString (method, resourcePath, request, expires) {
        request = request || {};
        let headers = request.headers || {};
        let ossHeaders = [];
        let headersToSign = {};

        let signContent = [
            method.toUpperCase(),
            headers['Content-Md5'] || '',
            headers['Content-Type'] || headers['Content-Type'.toLowerCase()],
            expires || headers['x-oss-date']
        ];

        Object.keys(headers).forEach((key) => {
            let lowerKey = key.toLowerCase();
            if (lowerKey.indexOf('x-oss-') === 0) {
                headersToSign[lowerKey] = String(headers[key]).trim();
            }
        });

        Object.keys(headersToSign).sort().forEach((key) => {
            ossHeaders.push(`${key}:${headersToSign[key]}`);
        });

        signContent = signContent.concat(ossHeaders);

        signContent.push(this.buildCanonicalizedResource(resourcePath, request.params));

        return signContent.join('\n');
    }

    computeSignature (accessKeySecret, canonicalString) {
        let buffer = Buffer.from(canonicalString, 'utf8');
        return crypto.createHmac('sha1', accessKeySecret).update(buffer).digest('base64');
    }

    /**
     * 生成签名
     */
    authorization (method, resource, subres, headers) {
        let stringToSign = this.canonicalString(method, resource, {
            headers,
            parameters: subres
        });

        let signature = this.computeSignature(this.options.accessKeySecret, stringToSign);
        return `OSS ${this.options.accessKeyId}:${signature}`;
    }

    request (params) {
        let headers = {
            'Content-Type': 'application/octet-stream',
            'x-oss-date': moment().utcOffset(0).format('ddd, DD MMM YYYY HH:mm:ss') + ' GMT',
            //'x-oss-request-payer': 'requester',
            //'x-oss-security-token': ''
        };

        if (params.content) {
            headers['Content-Md5'] = crypto
                .createHash('md5')
                .update(Buffer.from(params.content, 'utf8'))
                .digest('base64');
            if (!headers['Content-Length']) {
                headers['Content-Length'] = params.content.length;
            }
        }

        // 访问资源
        let resource = '/';
        if (params.bucket) resource += params.bucket + '/';
        if (params.object) resource += params.object;

        // 生成签名
        headers.authorization = this.authorization(params.method, resource, params.subres, headers);

        let url = this.getReqUrl(params);

        return helper.request({
            uri: url,
            method: params.method,
            headers: headers,
            form: params.data
        }, 'xml').then(function (res) {

            if (res.errcode) {
                return res;
            }

            if (res.Error) {
                return helper.error(res.Error.Message);
            }

            return res;
        });

    }

    bucketObjects (params, callback) {
        let scope = this;

        return new Promise(function (resolve) {

            let options = {
                method: 'GET',
                bucket: params.bucket || scope.options.bucket,
                data: {
                    marker: '/',
                    'max-keys': 1000
                }
            };

            let count = 0;
            let size = new BigNumber(0);

            function format (list) {
                count += list.length;

                list.forEach(item => {

                    size = size.plus(item.Size);

                    callback({
                        key: item.Key,
                        modified: moment(item.LastModified).format('X'),
                        etag: item.ETag,
                        type: item.Type,
                        size: item.Size
                    });
                });

                list.length = 0;
                list = null;
            }

            async function request (marker) {

                options.data.marker = marker;
                let res = await scope.request(options);

                if (res.errcode) {
                    return resolve(res);
                }

                let obj = res.ListBucketResult;

                format(obj.Contents);

                if (obj.IsTruncated === 'false') {
                    resolve({count: count, size: size.toString()});
                } else {
                    request(obj.NextMarker);
                }
            }

            request(params.marker || '/');
        });
    }

    async oneHourSize (bucket, startTime) {
        let endTime = startTime + 3600 - 1;

        let count = 0;
        let size = new BigNumber(0);

        let res = await this.bucketObjects({bucket: bucket, marker: '/'}, function (item) {
            item.modified = parseInt(item.modified);

            if (item.modified >= startTime && item.modified <= endTime) {
                count++;
                size = size.plus(item.size);
            }
        });

        if (res.errcode) {
            return res;
        }

        return {count: count, size: size.toString(), bucket: res}
    }

    put (object, filename) {
        let options = this.options;
        let client = new OSS({
            region: options.location,
            accessKeyId: options.accessKeyId,
            accessKeySecret: options.accessKeySecret,
            bucket: options.bucket,
            timeout: 3000000
        });

        return client.put(object, filename).then(_ => {
            return options.protocol + '://' + options.domain + '/' + object;
        });
    }
}
