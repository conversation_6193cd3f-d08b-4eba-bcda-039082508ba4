"use strict";

const BigNumber = require('bignumber.js');
const crypto = require('crypto');
const moment = require('moment');

module.exports = class AliCDN{

  constructor(opts) {

    this.opts = opts;
    this.commonParams = {
      Format: 'JSON',
      Version: '2018-05-10',
      AccessKeyId: opts.accessKeyId,
      SignatureMethod: 'HMAC-SHA1',
      get Timestamp(){
        return moment().toISOString().replace(/\.\d{3}/, '')
      },
      SignatureVersion: '1.0',
      get SignatureNonce(){
        return Date.now().toString() + Math.round(Math.random() * 1000)
      }
    };
  }

  actionInvoke(action, method, args) {
    let url = this.getUrl(action, method, args);

    return helper.request({method: method, uri: url, timeout: 10 * 1000}, 'json').then(function(res){
      if(res.errcode || res.Code){
        return helper.error(res.errmsg || res.Message);
      }

      return res;
    });
  }

  getUrl(action, method, args) {
    let params = this.sortObjectKeys(Object.assign({}, this.commonParams, args, {
      Action: action
    }));

    let headerString = '';
    Object.keys(params).forEach(key => headerString += `${key}=${encodeURIComponent(params[key])}&`);
    headerString = headerString.slice(0, -1);

    let stringToSign = method.toUpperCase() + '&%2F&' + encodeURIComponent(headerString);
    let sign = crypto.createHmac('SHA1', this.opts.accessKeySecret + '&').update(stringToSign).digest('base64');
    return  'https://cdn.aliyuncs.com/?' + 'Signature=' + encodeURIComponent(sign) + '&' + headerString;
  }

  sortObjectKeys(obj) {
    let res = {};
    Object
      .keys(obj || {})
      .sort((a, b) => { return a < b ? -1 : 1; })
      .forEach(key => {
        res[key] = obj[key];
      });
    return res;
  }

  /**
   * 获取加速域名最小1小时粒度的 UV页面独立访问统计。
   * https://help.aliyun.com/document_detail/91109.html?spm=5176.11065259.1996646101.searchclickresult.6dc5751a59ZTBs
   */
  async describeDomainUvData(startTime, endTime, domainName){
    let res = await this.actionInvoke('DescribeDomainUvData', 'GET', {
      DomainName: domainName || '',
      StartTime: moment(startTime * 1000).toISOString().replace(/\.\d{3}/, ''),
      EndTime: moment(endTime && endTime * 1000).toISOString().replace(/\.\d{3}/, '')
    });

    if(res.errcode){
      return res;
    }

    return res.UvDataInterval.UsageData.map(item => {
      return {
        time: moment(item.TimeStamp).format('X'),
        value: item.Value
      }
    });
  }

  /**
   * 获取某域名1小时内的PV
   */
  async oneHourUV(startTime, domainName, retry = true){
    let res = await this.actionInvoke('DescribeDomainUvData', 'GET', {
      DomainName: domainName || '',
      StartTime: moment(startTime).toISOString().replace(/\.\d{3}/, ''),
      EndTime: moment(startTime + 3600000 - 1).toISOString().replace(/\.\d{3}/, ''),
    });

    if(res.errcode){
      return retry ? this.oneHourUV(startTime, domainName, false) : res;
    }

    let num = new BigNumber(0);
    res.UvDataInterval.UsageData.forEach(item => {
      num = num.plus(item.Value);
    });
    return num;
  }

  /**
   * 调用DescribeDomainPvData接口获取加速域名最小1小时粒度的 PV页面访问统计。
   * https://help.aliyun.com/document_detail/27215.html?spm=5176.11065259.1996646101.searchclickresult.30085afbr2NDcx
   */
  async describeDomainPvData(startTime, endTime, domainName){
    let res = await this.actionInvoke('DescribeDomainPvData', 'GET', {
      DomainName: domainName || '',
      StartTime: moment(startTime * 1000).toISOString().replace(/\.\d{3}/, ''),
      EndTime: moment(endTime && endTime * 1000).toISOString().replace(/\.\d{3}/, '')
    });

    if(res.errcode){
      return res;
    }

    return res.PvDataInterval.UsageData.map(item => {
      return {
        time: moment(item.TimeStamp).format('X'),
        value: item.Value
      }
    });
  }

  async oneHourPV(startTime, domainName, retry = true){
    let res = await this.actionInvoke('DescribeDomainPvData', 'GET', {
      DomainName: domainName || '',
      StartTime: moment(startTime).toISOString().replace(/\.\d{3}/, ''),
      EndTime: moment(startTime + 3600000 - 1).toISOString().replace(/\.\d{3}/, '')
    });

    if(res.errcode){
      return retry ? this.oneHourPV(startTime, domainName, false) : res;
    }

    let num = new BigNumber(0);
    res.PvDataInterval.UsageData.forEach(item => {
      num = num.plus(item.Value);
    });
    return num;
  }

  /**
   * 获取加速域名的网络流量监控数据，单位：byte。
   * https://help.aliyun.com/document_detail/91045.html?spm=a2c4g.11186623.2.40.caa0593dWZKxYt#reference4822
   */
  async describeDomainTrafficData(startTime, endTime, domainName){
    let res = await this.actionInvoke('DescribeDomainTrafficData', 'GET', {
      DomainName: domainName || '',
      StartTime: moment(startTime * 1000).toISOString().replace(/\.\d{3}/, ''),
      EndTime: moment(endTime && endTime * 1000).toISOString().replace(/\.\d{3}/, ''),
      Interval: 3600,
      //LocationNameEn: '',
      //IspNameEn: ''
    });

    if(res.errcode){
      return res;
    }

    return res.TrafficDataPerInterval.DataModule.map(item => {
      return {
        time: moment(item.TimeStamp).format('X'),
        value: item.Value,
        domestic: item.DomesticValue,
        overseas: item.OverseasValue,
        https: item.HttpsValue,
        httpsdomestic: item.HttpsDomesticValue,
        httpsoverseas: item.HttpsOverseasValue
      }
    });
  }

  async oneHourTraffic(startTime, domainName, retry = true){
    let res = await this.actionInvoke('DescribeDomainTrafficData', 'GET', {
      DomainName: domainName || '',
      StartTime: moment(startTime).toISOString().replace(/\.\d{3}/, ''),
      EndTime: moment(startTime + 3600000 - 1).toISOString().replace(/\.\d{3}/, ''),
      Interval: 3600
    });

    if(res.errcode){
      return retry ? this.oneHourTraffic(startTime, domainName, false) : res;
    }

    let num = new BigNumber(0);
    res.TrafficDataPerInterval.DataModule.forEach(item => {
      num = num.plus(item.Value);
    });
    return num;
  }
}