'use strict';

const UploadCNF = require(ROOT_PATH + '/config/upload');
const crypto = require('crypto');
const {URL} = require('url');
const moment = require('moment');
const BigNumber = require('bignumber.js');

/**
 * 转码
 */
module.exports = class Jobs {

  constructor(opts) {
    this.opts = opts;
    this.commonParams = {
      Format: 'JSON',
      Version: '2014-06-18',
      AccessKeyId: opts.accessKeyId,
      SignatureMethod: 'HMAC-SHA1',
      SignatureVersion: '1.0',
    };
  }

  actionInvoke(action, method, args) {
    let url = this.getUrl(action, method, args);

    return helper.request({method: method, uri: url, timeout: 10 * 1000}, 'json').then(function (res) {
      return res.Code ? Promise.reject(res.Message) : res;
    });
  }

  getUrl(action, method, args) {
    let params = sortObjectKeys(Object.assign({}, this.commonParams, args, {
      Action: action,
      Timestamp: (new Date().toISOString()).replace(/\.\d{3}/, ''),
      SignatureNonce: Date.now().toString() + Math.round(Math.random() * 1000),
    }));

    let headerString = '';
    Object.keys(params).forEach(key => headerString += `${key}=${encodeURIComponent(params[key])}&`);
    headerString = headerString.slice(0, -1);

    let stringToSign = method.toUpperCase() + '&%2F&' + encodeURIComponent(headerString);
    let sign = crypto.createHmac('SHA1', this.opts.accessKeySecret + '&').update(stringToSign).digest('base64');
    return 'http://mts.cn-beijing.aliyuncs.com?' + 'Signature=' + encodeURIComponent(sign) + '&' + headerString;
  }

  /**
   *  提交转码作业
   *  https://help.aliyun.com/document_detail/29226.html?spm=a2c4g.11186623.6.655.2dd06d8fTCJusn
   */
  async submitJobs(orginalUrl, templates, outputBucket, outputLocation) {
    let url = new URL(orginalUrl);
    let inputObject = url.pathname.substr(1);

    // 转码格式
    let output = templates.map(item => {
      return {
        TemplateId: item.id,
        OutputObject: UploadCNF.filename('video', item.format == 'm3u8' ? '' : item.format).substr(1)
      };
    });

    let opts = this.opts;
    let res = await this.actionInvoke('SubmitJobs', 'GET', {
      PipelineId: 'a49ca956ecbe4e25835c827d9ebcca53',
      Input: JSON.stringify({Bucket: opts.bucket, Location: opts.location, Object: inputObject}),
      OutputBucket: outputBucket || opts.bucket,
      OutputLocation: outputLocation || opts.location,
      Outputs: JSON.stringify(output)
    });

    let result = {};
    res.JobResultList.JobResult.forEach(item => {
      this.pushJobResult(item.Job, result);
    });
    return result;
  }

  /**
   * 查询转码作业
   * https://help.aliyun.com/document_detail/29228.html?spm=a2c4g.11186623.6.657.72a4430cESuxP9
   */
  async queryJobList(jobIds) {
    let res = await this.actionInvoke('QueryJobList', 'GET', {
      JobIds: jobIds instanceof Array ? jobIds.join(',') : jobIds
    });

    let result = {};
    res.JobList.Job.forEach(item => {
      this.pushJobResult(item, result);
    });
    return result;
  }

  getObjectUrl(object) {
    let {opts} = this;
    return opts.protocol + '://' + opts.domain + '/' + object;
  }

  /**
   * 解析转码作业结果
   */
  pushJobResult(item, obj) {
    let res = {job: item.JobId, percent: item.Percent, address: this.getObjectUrl(item.Output.OutputFile.Object)};

    switch (item.State) {
      case 'Submitted':
        res.state = 0; // 排队中
        break;
      case 'Transcoding':
        res.state = 1; // 转码中
        break;
      case 'TranscodeSuccess':
        res.state = 2; // 转码成功
        break;
      case 'TranscodeFail': // 转码失败
        res.state = 3;
        break;
      case 'TranscodeCancelled':
        res.state = 4; // 取消转码
        break;
      default:
        res.state = 5; // 未知异常
        break;
    }

    obj[item.Output.TemplateId] = res;
  }
};

function sortObjectKeys(obj) {
  let res = {};

  Object.keys(obj || {}).sort((a, b) => {
    return a < b ? -1 : 1;
  }).forEach(key => {
    res[key] = obj[key];
  });

  return res;
}