'use strict';

const crypto = require('crypto');
const BigNumber = require('bignumber.js');
const { URL } = require('url');
const moment = require('moment');
moment.locale('zh-cn');

module.exports = class live {

  constructor(opts) {
    this.opts = opts;
    this.commonParams = {
      Format: 'JSON',
      Version: '2016-11-01',
      AccessKeyId: opts.accessKeyId,
      SignatureMethod: 'HMAC-SHA1',
      SignatureVersion: '1.0',
    };
  }

  actionInvoke(action, method, args) {
    const url = this.getUrl(action, method, args);

    return helper.request({method: method, uri: url, timeout: 10 * 1000}, 'json').then(function(res){
      let errcode = res.errcode || res.Code;
      if(errcode){
        return {errcode: errcode, errmsg: res.errmsg || res.Message};
      }

      return res;
    });
  }

  getUrl(action, method, args) {

    let params = Object.assign({}, this.commonParams, args, {
      Action: action,
      Timestamp: moment().toISOString().replace(/\.\d{3}/, ''),
      SignatureNonce: Date.now().toString() + Math.round(Math.random() * 1000)
    });

    params = sortObjectKeys(params);

    let headerString = '';
    Object.keys(params).forEach(key => headerString += `${key}=${encodeURIComponent(params[key])}&`);
    headerString = headerString.slice(0, -1);

    const stringToSign = method.toUpperCase() + '&%2F&' + encodeURIComponent(headerString);
    const sign = crypto.createHmac('SHA1', this.opts.accessKeySecret + '&').update(stringToSign).digest('base64');
    return  'https://live.aliyuncs.com?' + 'Signature=' + encodeURIComponent(sign) + '&' + headerString;
  }

  /**
   * 添加直播录制
   * https://help.aliyun.com/document_detail/35416.html?spm=a2c4g.11186623.6.770.54a27375k87O8u
   */
  addRecord(liveId, startTime){
    let opts = this.opts;
    let object = this.getObject(liveId, startTime);

    return this.actionInvoke('AddLiveAppRecordConfig', 'GET', {
      AppName: opts.bucket,
      StreamName: liveId,
      DomainName: opts.live_play,
      OssBucket:  opts.bucket,
      OssEndpoint: opts.location + '.aliyuncs.com',
      'RecordFormat.1.CycleDuration': 3600 * 6,
      'RecordFormat.1.Format': 'm3u8',
      "RecordFormat.1.OssObjectPrefix": object + '{EscapedStartTime}_{EscapedEndTime}',
      "RecordFormat.1.SliceOssObjectPrefix": object +'{UnixTimestamp}_{Sequence}'
    });
  }

  getObject(liveId, startTime){
    return 'live/' + moment(startTime * 1000).format('YYYYMMDD') + '/' + liveId + '/';
  }

  /**
   * 查询录制索引文件
   * https://help.aliyun.com/document_detail/35423.html?spm=a2c4g.11186623.6.777.e9cd29f4TXsL6B
   */
  getRecordFiles(liveId, liveStart, liveEnd){
    let opts = this.opts;
    let scope = this;

    return this.actionInvoke('DescribeLiveStreamRecordIndexFiles', 'GET', {
      AppName: opts.bucket,
      StreamName: liveId,
      DomainName: opts.live_play,
      StartTime: moment(liveStart * 1000).toISOString().replace(/\.\d{3}/, ''),
      EndTime: moment(liveEnd ? liveEnd * 1000 + 180000 : undefined).toISOString().replace(/\.\d{3}/, ''),
      PageNum: 1,
      PageSize: 30
    }).then(function(res){
      if(res.errcode){
        if(res.errcode == 'InvalidAccessKeyId.NotFound'){
          return {count: 0, rows: [], duration: 0, width: 0, height: 0, start_time: 0, end_time: 0};
        }
        return res;
      }

      let body = {count: res.TotalNum, rows: [], duration: 0, width: 0, height: 0, start_time: 0, end_time: 0};
      let list = res.RecordIndexInfoList.RecordIndexInfo;
      if(list.length == 0){
        return body;
      }

      let duration = new BigNumber(0);
      let startTime, endTime;

      list.forEach(item => {
        startTime = parseInt(moment(item.StartTime).format('X'));
        endTime = parseInt(moment(item.EndTime).format('X'));

        body.rows.push({
          id: item.RecordId,
          url: scope.getObjectUrl(item.OssObject),
          record_url: item.RecordUrl,
          domain: item.DomainName,
          app_name: item.AppName,
          stream_name: item.StreamName,
          start_time: startTime,
          end_time: endTime,
          duration: item.Duration,
          height: item.Height,
          width: item.Width,
          created: moment(item.CreateTime).format('X'),
          bucket: item.OssBucket,
          location: item.OssEndpoint,
          object: item.OssObject,
        });

        if(body.height === 0 || item.Height < body.height){
          body.height = item.Height;
          body.width = item.Width;
        }

        if(!body.start_time || startTime < body.start_time){
          body.start_time = startTime;
        }

        if(endTime > body.end_time){
          body.end_time = endTime;
        }

        duration = duration.plus(item.Duration);
      });

      body.duration = parseFloat(duration.toFixed(3));
      return body;
    });
  }

  /**
   * 合并录制索引文件
   * https://help.aliyun.com/document_detail/35417.html?spm=a2c4g.11186623.4.3.3fc651f5hisGQp
   */
  mergeRecordFiles(liveId, liveStart, ext){
    let scope = this;
    let opts = this.opts;
    let object = this.getObject(liveId, liveStart) + 'merge.' + (ext || 'm3u8');

    return this.actionInvoke('CreateLiveStreamRecordIndexFiles', 'GET', {
      AppName: opts.bucket,
      StreamName: liveId,
      DomainName: opts.live_play,
      OssBucket: opts.bucket,
      StartTime: moment(liveStart * 1000).toISOString().replace(/\.\d{3}/, ''),
      EndTime: moment().toISOString().replace(/\.\d{3}/, ''),
      OssEndpoint: opts.location + '.aliyuncs.com',
      OssObject: object
    }).then(function(res){
      if(res.errcode){
        return res;
      }

      res = res.RecordInfo;

      return {
        url: scope.getObjectUrl(object),
        start_time: parseInt(moment(res.StartTime).format('X')),
        end_time: parseInt(moment(res.EndTime).format('X')),
        duration: res.Duration,
        height: res.Height,
        width: res.Width
      };
    });
  }

  getObjectUrl(object){
    let opts = this.opts;
    return opts.protocol + '://' + opts.domain + '/' + object;
  }

  /**
   * 生成直播地址
   */
  createLiveAddress(streamName){
    let opts = this.opts;
    let appName = opts.bucket;
    let play = opts.live_play;
    let httpPrefix = opts.protocol + `://${play}/${appName}/${streamName}`;
    let rtmpPrefix = `rtmp://${play}/${appName}/${streamName}`;

    /*
    let address = [{
      flv: httpPrefix + '_1280x720.flv',
      m3u8: httpPrefix + '_1280x720.m3u8',
      rtmp: rtmpPrefix + '_1280x720',
      text: "高清",
      quality: 2,
      resolution: "1280x720"
    }, {
      flv: httpPrefix + '_1920x1080.flv',
      m3u8: httpPrefix + '_1920x1080.m3u8',
      rtmp: rtmpPrefix + '_1920x1080',
      text: "超清",
      quality: 3,
      resolution: "1920x1080"
    }, {
      flv: httpPrefix + '.flv',
      m3u8: httpPrefix + '.m3u8',
      rtmp: rtmpPrefix,
      text: "4K",
      quality: 4,
      resolution: "3840x2160"
    }];
    */

    return {
      push: 'rtmp://' + opts.live_push + '/' + appName + '/' + streamName,
      play: [{
        flv: httpPrefix + '.flv',
        m3u8: httpPrefix + '.m3u8',
        rtmp: rtmpPrefix,
        text: "4K",
        quality: 4,
        resolution: "3840x2160"
      }]
    };
  }
}

function sortObjectKeys(obj) {
  const res = {};
  Object
  .keys(obj || {}).sort((a, b) => { return a < b ? -1 : 1; })
  .forEach(key => { res[key] = obj[key]; });
  return res;
}
