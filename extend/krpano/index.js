"use strict";

const fs = require('fs');
const platform = require('os').platform();
const {spawn} = require('child_process');
const path = require('path');
const tool = path.join(__dirname, 'krpanotools' + (platform == 'linux' ? '' : '.exe'));
const config = '-config=' + path.join(__dirname, 'scene.config');
const queues = [];
let worker;

function makepano(inputs) {

  return new Promise((resolve, reject) => {
    let callback = function () {
      let params = ['makepano', config].concat(inputs);
      let error = function (e) {
        reject('图片处理失败：' + (e.message || e));
      };

      worker = spawn(tool, params);

      worker.stdout.on('data', function (data) {
        // 不可删除，否则linux服务器不会继续执行
      });

      worker.on('close', (code) => {
        if (code === 0) {
          try {
            let dir = path.dirname(inputs[0]);
            let xml = fs.readFileSync(path.join(dir, 'tour.xml'), 'utf8');
            xml = xml.match(/multires="((\d+,?)+)"/);
            resolve({path: path.join(dir, 'vtour'), multires: xml[1]});
          } catch (e) {
            error(e);
          }
        } else {
          error(code);
        }

        worker = null;
        params = null;
        error = null;

        if (queues.length > 0) {
          queues.shift()();
        }
      });

      worker.on('error', error);
    };

    if (worker) {
      queues.push(callback);
    } else {
      callback();
    }
  });

}

module.exports = {
  makepano
};
