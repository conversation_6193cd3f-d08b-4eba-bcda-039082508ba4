const request = require('request');
const fs = require('fs');
const path = require('path');
const position = ['b', 'd', 'f', 'l', 'r', 'u'];
const rule = '/%s/l%l/%v/l%l_%s_%v_%h.jpg';

const pad = function (v, l) {
  v += '';

  while (v.length < l) {
    v = '0' + v;
  }

  return v;
};

// 解析路径
const parsePath = function (multires, vrimg) {
  let v, h, len,
    p = multires && Math.ceil(multires[multires.length - 1] / multires[0]).toString().length,
    files = ['/preview.jpg'];

  position.forEach(s => {
    vrimg && files.push(vrimg.replace(/\%s/g, s), '/vr/pano_' + s + '.jpg');

    multires && multires.forEach((width, l) => {
      if (l == 0) {
        return;
      }

      len = Math.ceil(width / multires[0]);

      for (v = 1; v <= len; v++) {
        for (h = 1; h <= len; h++) {
          files.push(rule.replace(/\%l/g, l).replace(/\%s/g, s).replace(/\%v/g, pad(v, p)).replace(/\%h/g, pad(h, p)));
        }
      }

    });
  });

  return files;
};

// 执行下载
const download = function (prefix, suffix, root, referer) {
  let file = path.join(root, suffix);

  return new Promise((resolve, reject) => {

    fs.stat(file, function (err, res) {
      if (!err && res.size > 0) {
        return resolve();
      }

      fs.mkdir(path.dirname(file), {recursive: true}, (err) => {
        if (err) return reject(err);

        request({
          url: prefix + suffix,
          headers: {
            referer: referer
          }
        }, function (err, httpResponse) {
          if (err) return reject(err);

          let type = httpResponse.headers['content-type'];
          if (/(image|stream)/.test(type)) {
            resolve();
          } else {
            fs.unlink(file, function () {

            });

            reject('content-type-' + type);
          }
        }).pipe(fs.createWriteStream(file));

      });

    });

  });
};

let files = parsePath([512, 768, 1536, 3072, 5888, 11904, 23808, 47744, 95488]/*, "/pano_%s.jpg"*/);
let root = path.join(__dirname, '720think');
let len = files.length;
let index = 0;

function run() {
  let suffix = files[index++], i = index;
  if (index >= len) {
    return;
  }

  download(
    'https://resource.720think.com/v/Data/upload/think720/201609/926204010/tiles/201801/135115',
    suffix,
    root,
    'https://9b81cljdn.wasee.com/c/9b81cljdn/p/f87me5yt4?def_sid=340677&ath=9.391375096739756&atv=-10.136450963548578'
  ).then(function () {
    console.log('已完成第' + i + '个，还剩' + (len - index) + '个');
  }).catch(function (err) {
    console.log({
      file: suffix,
      err: err.toString()
    });
  }).finally(run);
}

// 多线程下载
for (let i = 0; i < 4; i++) {
  run();
}
