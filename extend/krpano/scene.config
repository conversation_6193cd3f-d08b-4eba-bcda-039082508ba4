# krpano 1.20


askforxmloverwrite=false


# image filtering and compression settings
filter=LANCZOS
jpegquality=82
jpegsubsamp=422
jpegoptimize=true


# color profile handling: skip, copy, convert or sRGB
profile=sRGB


# load gps exif information from the input files and transfer them to the xml
parsegps=true


# parse the input files for orientation / leveling information and either
# directly remap the images or add a prealign setting in the xml to level them
autolevel=remap


# sort the input files alphabetically
sortinput=true


# protection settings (nomb = no Flashplayer menubar for standalone swf)
kprotectclparameters=-nomb

panotype=autodetect
hfov=360
makescenes=true


# convert spherical/cylindrical to cubical
converttocube=true
converttocubelimit=360x45
converttocubemaxwidth=60000


# multiresolution settings
multires=true
tilesize=512
levels=auto
levelstep=2
maxsize=auto
maxcubesize=auto


# generate stereo images (if there are one) only for VR, not for normal viewing
stereosupport=false


# output images path
tilepath=%INPUTPATH%/vtour/[c/]l%Al/%Av/l%Al[_c]_%Av_%Ah.jpg


# preview pano settings
preview=true
graypreview=false
previewsmooth=25
previewpath=%INPUTPATH%/vtour/preview.jpg

# generate special optimized, non-tiled, lower-res images for VR
customimage[vr].size=1536
customimage[vr].stereosupport=true
customimage[vr].path=%INPUTPATH%/vtour/vr/pano_%s.jpg
customimage[vr].imagesettings=jpegquality=82 jpegsubsamp=444 jpegoptimize=true
customimage[vr].xmlsceneparameters=havevrimage="true"
customimage[vr].xmlimageparameters=if="!webvr.isenabled"
customimage[vr].xmllevel=krpano
customimage[vr].xml=<image if="webvr.isenabled">[NL][TAB]<cube url="[PATH]" />[NL]</image>

# generate thumbnails
makethumb=true
thumbsize=300
thumbpath=%INPUTPATH%/vtour/thumb.jpg

# output
flash=false
html5=false
html=false
xml=true

# xml template
xmlpath=%INPUTPATH%/tour.xml
xmltemplate=vtour.xml
