const request = require('request');
const fs = require('fs');

const root = 'C:/WorkSpace/hvr/static/pano/tile/20190802/';
const list = ['b', 'd', 'f', 'l', 'r', 'u'];

let download = function () {

  let download = function (url, save, path, name) {
    url = url + path;

    if(!name){
      name = path.split('?')[0];
      name = name.substr(name.lastIndexOf('/') + 1);
    }

    path = path.split('?')[0];
    path = path.substr(0, path.lastIndexOf('/') + 1);

    let filename = save + path + name;

    fs.mkdir(filename.substr(0, filename.lastIndexOf('/')), {recursive: true}, (err) => {
      request({
        url: url,
        headers: {
          referer: 'https://720yun.com'
        }
      }, function (err, httpResponse) {
        type = httpResponse.headers['content-type'];
        if (type == 'image/jpeg') {
          //console.log( '已下载：' + path + name);
        } else {
          console.log('失败了：' + path + name);
          fs.unlink(filename, function () {

          });
        }
      }).pipe(fs.createWriteStream(filename));
    });
  }

  return function (id, params) {
    let v, h, len, save = root + id;

    list.forEach(s => {
      if (params.resolution) {
        params.resolution.forEach((item, l) => {
          len = Math.ceil(item.width / 512);

          for (v = 1; v <= len; v++) {
            for (h = 1; h <= len; h++) {
              download(params.url, save, item.url.replace(/\%s/g, s).replace(/\%v/g, v).replace(/\%h/g, h));
            }
          }

        });
      }

      if (params.vrimg) {
        download(params.url, save, params.vrimg.replace(/\%s/g, s), '/vr/pano_' + s + '.jpg');
      }
    });

    download(params.url, save, params.preview);
  }

}();

download(32373839, {
  "resolution": [
    {
      "width": 1152,
      "height": 1152,
      "url": "/%s/l1/%v/l1_%s_%v_%h.jpg?t=1566525225"
    },
    {
      "width": 2304,
      "height": 2304,
      "url": "/%s/l2/%v/l2_%s_%v_%h.jpg?t=1566525225"
    },
    {
      "width": 4608,
      "height": 4608,
      "url": "/%s/l3/%v/l3_%s_%v_%h.jpg?t=1566525225"
    }
  ],
  "url": "https://ssl-panoimg94.720static.com/resource/prod/f80i730cen8/78326xO6aun/20462397/1566144128/imgs",
  "preview": "/preview.jpg?t=1566525225",
  "vrimg": "/mobile_%s.jpg?t=1566525225"
});