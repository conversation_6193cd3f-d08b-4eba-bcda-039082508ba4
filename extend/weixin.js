const axios = require('axios');
const crypto = require('crypto');
const fs = require('fs');
//const FormData = require('form-data');

// 微信api根路径
const ApiUrl = 'https://api.weixin.qq.com/';
const ApiCgi = ApiUrl + 'cgi-bin/';
const KeFuApi = ApiUrl + 'customservice/';
// 微信媒体文件根路径
const MediaURL = 'http://file.api.weixin.qq.com/cgi-bin';
// 微信二维码根路径
const QrcodeURL = 'https://mp.weixin.qq.com/cgi-bin';
// 授权登录
const RequestCodeURL = 'https://open.weixin.qq.com/connect/oauth2/authorize';
const OauthApiURL = 'https://api.weixin.qq.com/sns';

module.exports = class WeiXin {

  constructor(ctx, cnf) {
    this.ctx = ctx;

    if (!cnf || cnf == 'wxmp') {
      cnf = ctx.env.wxmp;
    } else if (cnf == 'wxpro') {
      cnf = ctx.env.wxpro;
    } else if (cnf == 'wxop') {
      cnf = ctx.env.wxop;
    } else if (cnf == 'wxth') {
      cnf = ctx.env.wxth;
    }

    this.appid = cnf.appid;
    this.secret = cnf.secret;
    this.token = cnf.token;
  }

  /**
   * 校验签名
   */
  checkSignature(query) {
    let str = [this.token, query.timestamp, query.nonce].sort().join('');
    return helper.sha1(str) == query.signature;
  }

  /**
   * 请求微信API
   */
  request(url, data = null, method = 'GET') {
    let errcode = -1;

    return axios({
      method: method,
      url: url,
      data: data,
      timeout: 15000,
      responseType: 'json'
    }).then(async (response) => {
      let data = response.data;

      errcode = data.errcode;

      if (errcode) {
        console.log(data);
        if (errcode == 40029) {
          await this.cache('wxat:' + this.appid, null);
        }

        throw new Error(errcode);
      }

      return data;
    }).catch(e => {
      return Promise.reject('请求微信接口失败：' + e.message);
    });
  }

  /**
   * 获取access_token
   */
  async getAccessToken() {
    let key = 'wxat:' + this.appid;
    let token = await this.cache(key);

    if (token) return token;

    let param = {appid: this.appid, secret: this.secret, grant_type: 'client_credential'};
    let res = await this.request(ApiCgi + 'token', param);

    token = res.access_token;
    await this.cache(key, token, res.expires_in - 600);

    return token;
  }

  /**
   * 获取jsticket
   */
  async getJsTicket() {
    let key = 'wxjt:' + this.appid;
    let token = await this.cache(key);
    if (token) return token;

    let params = {access_token: await this.getAccessToken(), type: 'jsapi'};
    let res = await this.request(ApiCgi + 'ticket/getticket', params);

    token = res.ticket;
    await this.cache(key, token, res.expires_in - 600);
    return token;
  }

  /**
   * jssdk签名
   */
  async jssign(url) {
    let ticket = await this.getJsTicket();
    let nonceStr = helper.randString(16);
    let timestamp = helper.time();
    let str = 'jsapi_ticket=' + ticket + '&noncestr=' + nonceStr + '&timestamp=' + timestamp + '&url=' + url;

    return {
      appId: this.appid,
      nonceStr: nonceStr,
      timestamp: timestamp,
      signature: helper.sha1(str)
    };
  }

  async getAuthInfo(code) {
    let url = OauthApiURL + '/oauth2/access_token';
    let param = {appid: this.appid, secret: this.secret, code: code, grant_type: 'authorization_code'};
    let res = await this.request(url, param);
    res.appid = this.appid;
    return res;
  }

  /**
   * 获取授权登录用户信息
   */
  async getAuthUser(code) {
    let res = await this.getAuthInfo(code);
    res = await this.request(OauthApiURL + '/userinfo', {access_token: res.access_token, openid: res.openid, lang: 'zh_CN'});
    res.appid = this.appid;
    return res;
  }

  /**
   * 获取关注用户信息
   */
  async getSubUser(openid) {
    let url = ApiCgi + 'user/info';
    let param = {openid: openid, lang: 'zh_CN', access_token: await this.getAccessToken()};
    let res = await this.request(url, param);
    res.appid = this.appid;
    return res;
  }

  async code2Session(code) {
    let url = OauthApiURL + `/jscode2session?appid=${this.appid}&secret=${this.secret}&js_code=${code}&grant_type=authorization_code`;
    let res = await this.request(url);
    return res;
  }

  /**
   * 小程序数据解密
   */
  decodeProData(encryptedData, iv, sessionKey) {
    sessionKey = Buffer.from(sessionKey, 'base64');
    encryptedData = Buffer.from(encryptedData, 'base64');
    iv = Buffer.from(iv, 'base64');

    let decipher = crypto.createDecipheriv('aes-128-cbc', sessionKey, iv);
    decipher.setAutoPadding(true);
    let decoded = decipher.update(encryptedData, 'binary', 'utf8');
    decoded += decipher.final('utf8');
    decoded = JSON.parse(decoded);
    return decoded.watermark.appid == this.appid ? decoded : this.ctx.throw('Illegal Buffer');
  }

  async cache(key, val, exp) {
    let res, cache = this.ctx.redis;

    try {
      if (val === undefined) {
        res = await cache.get(key);
      } else if (val === null) {
        res = await cache.del(key);
      } else {
        res = await cache.set(key, val, exp);
      }
    } catch (e) {

    }

    return res;
  }

  getAppAccessToken(appid) {
    return (!appid || appid == this.appid) ? this.getAccessToken() : this.getAuthAccessToken(appid);
  }

  /**
   * 获取客服列表
   */
  async getKeFuList(appid) {
    let token = await this.getAppAccessToken(appid);
    let url = ApiCgi + 'customservice/getkflist?access_token=' + token;
    let res = await this.request(url);
    return res.kf_list;
  }

  /**
   * 获取在线客服
   */
  async getOnlineKeFu(appid) {
    let token = await this.getAppAccessToken(appid);
    let url = ApiCgi + 'customservice/getonlinekflist?access_token=' + token;
    let res = await this.request(url);
    return res.kf_online_list;
  }

  /**
   * 添加客服账号
   */
  async addKeFu(account, nickname, appid) {
    let token = await this.getAppAccessToken(appid);
    let url = KeFuApi + 'kfaccount/add?access_token=' + token;
    let data = {kf_account: account, nickname: nickname};
    await this.request(url, data, 'POST');
  }

  /**
   * 删除客服
   */
  async delKeFu(account, appid) {
    let token = await this.getAppAccessToken(appid);
    let url = KeFuApi + 'kfaccount/del?access_token=' + token;
    let data = {kf_account: account};
    await this.request(url, data, 'POST');
  }

  /**
   * 绑定客服微信号
   */
  async setKeFuWx(account, wxno, appid) {
    let token = await this.getAppAccessToken(appid);
    let url = KeFuApi + 'kfaccount/inviteworker?access_token=' + token;
    let data = {kf_account: account, invite_wx: wxno};
    await this.request(url, data, 'POST');
  }

  /**
   * 设置客服昵称
   */
  async setKeFuNickname(account, nickname, appid) {
    let token = await this.getAppAccessToken(appid);
    let url = KeFuApi + 'kfaccount/update?access_token=' + token;
    let data = {kf_account: account, nickname: nickname};
    await this.request(url, data, 'POST');
  }

  /**
   * 设置客服头像
   */
  async setKeFuHeadimg(account, file, appid) {
    let token = await this.getAppAccessToken(appid);
    let form = new FormData();

    form.append('media', fs.createReadStream(file.path), {
      filename: file.name,
      knownLength: file.size,
      contentType: file.type
    });

    return new Promise((resolve, reject) => {
      form.submit({
        protocol: 'https:',
        host: 'api.weixin.qq.com',
        path: '/customservice/kfaccount/uploadheadimg?access_token=' + token + '&kf_account=' + account,
        headers: {'Content-Type': 'multipart/form-data'}
      }, function (err, res) {
        if (err) return reject(err);

        if (res.statusCode !== 200) return reject('weixin http status ' + res.statusCode);

        res.on('data', data => {
          try {
            data = JSON.parse(data.toString());
            data.errcode ? reject() : resolve();
          } catch (e) {
            reject(e);
          }
        });

      });
    });
  }

  /***
   * 以下是第三方平台API
   */
  async getComponentAccessToken() {
    let {appid} = this;
    let key = 'wxat:' + appid;
    let token = await this.cache(key);

    if (token) return token;

    let ticket = await this.cache('wxcvt:' + appid);
    this.ctx.assert(ticket, 'invalid component_verify_ticket');

    let url = ApiCgi + 'component/api_component_token';

    let data = {
      component_appid: appid,
      component_appsecret: this.secret,
      component_verify_ticket: ticket
    }

    let res = await this.request(url, data, 'POST');

    token = res.component_access_token;

    await this.cache(key, token, res.expires_in - 600);

    return token;
  }

  /**
   * 预授权码（pre_auth_code）
   * 是第三方平台方实现授权托管的必备信息，每个预授权码有效期为 10 分钟
   * https://developers.weixin.qq.com/doc/oplatform/Third-party_Platforms/api/pre_auth_code.html
   */
  async getPreAuthCode() {
    let token = await this.getComponentAccessToken();
    let url = ApiCgi + 'component/api_create_preauthcode?component_access_token=' + token;
    let data = {component_appid: this.appid};
    let res = await this.request(url, data, 'POST');
    return res.pre_auth_code;
  }

  /**
   * 生成授权地址
   */
  async getAuthLink(redirect, authType) {
    redirect = encodeURIComponent(redirect);

    let code = await this.getPreAuthCode();
    let url = 'https://mp.weixin.qq.com/';
    let pub = 'component_appid=' + this.appid + '&auth_type=' + authType + '&pre_auth_code=' + code + '&redirect_uri=' + redirect;

    return {
      // 授权注册页面扫码授权
      pc: url + 'cgi-bin/componentloginpage?' + pub,
      // 点击移动端链接快速授权
      h5: url + 'safe/bindcomponent?action=bindcomponent&' + pub + '&no_scan=1#wechat_redirect'
    }
  }

  /**
   * 使用授权码获取授权信息
   * 授权后必须调用此方法并保存authorizer_refresh_token
   */
  async queryAuth(code) {
    let token = await this.getComponentAccessToken();
    let url = ApiCgi + 'component/api_query_auth?component_access_token=' + token;
    let data = {
      component_appid: this.appid,
      authorization_code: code
    };
    let res = await this.request(url, data, 'POST');
    res = res.authorization_info;

    await this.cache('wxaat:' + res.authorizer_appid, res.authorizer_access_token, res.expires_in - 600);
    await this.cache('wxart:' + res.authorizer_appid, res.authorizer_refresh_token);

    return {
      appid: res.authorizer_appid,
      access_token: res.authorizer_access_token,
      refresh_token: res.authorizer_refresh_token
    };
  }

  parseAuthAccess(res) {
    return res.authorization_info.func_info.map(item => {
      return item.funcscope_category.id
    })
  }

  /**
   * 获取/刷新接口调用令牌
   */
  async getAuthAccessToken(appid) {
    let key = 'wxaat:' + appid;
    let token = await this.cache(key);

    if (token) return token;

    let refresh = await this.cache('wxart:' + appid);
    if (!refresh) this.ctx.throw('刷新令牌丢失，请重新授权');

    let cat = await this.getComponentAccessToken();
    let url = ApiCgi + 'component/api_authorizer_token?component_access_token=' + cat;
    let data = {
      component_appid: this.appid,
      authorizer_appid: appid,
      authorizer_refresh_token: refresh
    }
    let res = await this.request(url, data, 'POST');

    token = res.authorizer_access_token;

    await this.cache('wxaat:' + appid, token, res.expires_in - 600);
    await this.cache('wxart:' + appid, res.authorizer_refresh_token);

    return token;
  }

  /**
   * 获取授权方的帐号基本信息
   */
  async getAuthAppInfo(appid) {
    let token = await this.getComponentAccessToken();
    let url = ApiCgi + 'component/api_get_authorizer_info?component_access_token=' + token;
    let data = {
      component_appid: this.appid,
      authorizer_appid: appid
    }
    let res = await this.request(url, data, 'POST');

    await this.cache('wxart:' + appid, res.authorization_info.authorizer_refresh_token);

    return res;
  }

  /**
   * 获取授权方选项信息
   */
  async getAuthAppOption(appid, name) {
    let token = await this.getComponentAccessToken();
    let url = ApiCgi + 'component/api_get_authorizer_option?component_access_token=' + token;
    let data = {
      component_appid: this.appid,
      authorizer_appid: appid,
      option_name: name
    }
    return this.request(url, data, 'POST');
  }

  /**
   * 设置授权方选项信息
   * https://developers.weixin.qq.com/doc/oplatform/Third-party_Platforms/api/api_set_authorizer_option.html
   */
  async setAuthAppOption(appid, name, value) {
    let token = await this.getComponentAccessToken();
    let url = ApiCgi + 'component/api_set_authorizer_option?component_access_token=' + token;
    let data = {
      component_appid: '第三方平台appid',
      authorizer_appid: appid,
      option_name: name,
      option_value: value
    }
    return this.request(url, data, 'POST');
  }

};
