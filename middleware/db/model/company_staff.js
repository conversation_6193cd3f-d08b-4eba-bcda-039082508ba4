module.exports = function (sequelize, DataTypes) {
  return sequelize.define('company_staff', {
    id: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: true,
      primaryKey: true,
      autoIncrement: true
    },
    company_id: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false
    },
    headimg: {
      type: DataTypes.STRING(256),
      allowNull: false,
      defaultValue: ''
    },
    name: {
      type: DataTypes.STRING(32),
      allowNull: false
    },
    sex: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false
    },
    status: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    sequence: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    position_name: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: ''
    },
    nation: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    birthday: {
      type: DataTypes.STRING(10),
      allowNull: false,
      defaultValue: '0000-00-00'
    },
    school_name: {
      type: DataTypes.STRING(32),
      allowNull: false,
      defaultValue: ''
    },
    education: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    join_work: {
      type: DataTypes.STRING(10),
      allowNull: false,
      defaultValue: '0000-00-00'
    },
    dept_id: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    created: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: helper.time
    },
    modified: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: helper.time
    },
    party_code: {
      type: DataTypes.STRING(10),
      allowNull: false,
      defaultValue: 0
    },
    party_join: {
      type: DataTypes.STRING(10),
      allowNull: false,
      defaultValue: '0000-00-00'
    },
    party_title: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: ''
    },
    party_oath: {
      type: DataTypes.STRING(64),
      allowNull: false,
      defaultValue: ''
    },
    party_intro: {
      type: DataTypes.STRING(1000),
      allowNull: false,
      defaultValue: ''
    },
    tag: {
      type: DataTypes.STRING(50),
      allowNull: false,
      defaultValue: ''
    },
    grade: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    promise_image: {
      type: DataTypes.STRING(1000),
      allowNull: false,
      defaultValue: ''
    },
  }, {
    tableName: 'company_staff',
    timestamps: false
  });
}
