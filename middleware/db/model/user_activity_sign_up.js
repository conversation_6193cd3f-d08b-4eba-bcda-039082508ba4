module.exports = function (sequelize, DataTypes) {
    return sequelize.define('user_activity_sign_up', {
        id: {
            type: DataTypes.INTEGER(10).UNSIGNED,
            allowNull: false,
            primaryKey: true,
            autoIncrement: true
        },
        // 用户唯一标识
        open_id: {
            type: DataTypes.STRING(32),
            allowNull: false,
            defaultValue: ''
        },
        // 用户名
        user_name: {
            type: DataTypes.STRING(32),
            allowNull: false,
            defaultValue: ''
        },
        // 活动id
        activity_id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: 0
        },
        // 活动名称
        activity_name: {
            type: DataTypes.STRING(64),
            allowNull: false,
            defaultValue: ''
        },
        // 签到
        check_in: {
            type: DataTypes.STRING(32),
            allowNull: true
        },
        // 签退
        check_out: {
            type: DataTypes.STRING(32),
            allowNull: true
        },
        created: {
            type: DataTypes.INTEGER(11),
            allowNull: false
        }
    }, {
        tableName: 'user_activity_sign_up',
        timestamps: false
    });
};
