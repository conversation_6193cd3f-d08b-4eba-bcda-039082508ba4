/* jshint indent: 1 */

module.exports = function(sequelize, DataTypes) {
	return sequelize.define('user_visitor', {
		id: {
			type: DataTypes.INTEGER(11),
			allowNull: false,
			primaryKey: true,
			autoIncrement: true
		},
		owner_id: {
			type: DataTypes.INTEGER(10).UNSIGNED,
			allowNull: false
		},
		name: {
			type: DataTypes.STRING(32),
			allowNull: false,
			defaultValue: ''
		},
		mobile: {
			type: DataTypes.STRING(15),
			allowNull: false,
			defaultValue: ''
		},
		idcard: {
			type: DataTypes.STRING(18),
			allowNull: false,
			defaultValue: ''
		},
		passport: {
			type: DataTypes.STRING(32),
			allowNull: false,
			defaultValue: ''
		}
	}, {
		tableName: 'user_visitor',
		timestamps: false
	});
};
