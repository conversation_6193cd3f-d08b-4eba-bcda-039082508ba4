/* jshint indent: 1 */

module.exports = function(sequelize, DataTypes) {
	return sequelize.define('user_extend', {
		id: {
			type: DataTypes.BIGINT,
			allowNull: false,
			primaryKey: true
		},
		uid: {
			type: DataTypes.BIGINT,
			allowNull: false
		},
		key: {
			type: DataTypes.STRING(16),
			allowNull: false
		},
		value: {
			type: DataTypes.STRING(5000),
			allowNull: false
		}
	}, {
		tableName: 'user_extend',
		timestamps: false
	});
};
