module.exports = function (sequelize, DataTypes) {
  return sequelize.define('media_motto', {
    id: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      primaryKey: true,
      autoIncrement: true
    },
    bgcolor: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: ''
    },
    bgimage: {
      type: DataTypes.STRING(128),
      allowNull: false,
      defaultValue: ''
    },
    bgmusic: {
      type: DataTypes.JSON,
      allowNull: true
    },
    interval: {
      type: DataTypes.INTEGER(5),
      allowNull: false,
      defaultValue: 3
    }
  }, {
    tableName: 'media_motto',
    timestamps: false
  });
};