module.exports = function (sequelize, DataTypes) {
    return sequelize.define('volunteer_activity_plan', {
        id: {
            type: DataTypes.INTEGER(10).UNSIGNED,
            allowNull: false,
            primaryKey: true,
            autoIncrement: true
        },
        // 活动名称
        activity_name: {
            type: DataTypes.STRING(100),
            allowNull: false,
            defaultValue: ''
        },
        // 活动内容
        activity_content: {
            type: DataTypes.STRING(1000),
            allowNull: false,
            defaultValue: ''
        },
        // 活动日期
        activity_date: {
            type: DataTypes.STRING(64),
            allowNull: false,
            defaultValue: ''
        },
        // 开始时间
        start_time: {
            type: DataTypes.STRING(32),
            allowNull: false,
            defaultValue: ''

        },
        // 结束时间
        end_time: {
            type: DataTypes.STRING(32),
            allowNull: false,
            defaultValue: ''

        },
        // 活动地点
        location: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: ''
        },
        // 主办单位
        organizer: {
            type: DataTypes.STRING(100),
            allowNull: false,
            defaultValue: ''
        },
        // 活动负责人
        person_in_charge: {
            type: DataTypes.STRING(32),
            allowNull: false,
            defaultValue: ''
        },
        // 联系方式
        contact: {
            type: DataTypes.STRING(16),
            allowNull: false,
            defaultValue: ''
        },
        /**
         * 服务类别
         * 0：社区服务类
         * 1：人文关怀类
         * 2：文化建设类
         * 3：健康促进类
         * 4：应急管理类
         * 5：自治管理类
         */
        service_type: {
            type: DataTypes.STRING(1),
            allowNull: false,
            defaultValue: '0'
        },
        // 备注
        remarks: {
            type: DataTypes.STRING(255),
            allowNull: true
        },
        // 创建时间
        created_at: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
        },
        
    }, {
        tableName: 'volunteer_activity_plan',
        timestamps: false, 
    });
};