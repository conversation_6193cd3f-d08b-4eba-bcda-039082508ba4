module.exports = function (sequelize, DataTypes) {
    return sequelize.define('media_column', {
        id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true
        },
        cover_url: {
            type: DataTypes.STRING(218),
            allowNull: false
        },
        title: {
            type: DataTypes.STRING(64),
            allowNull: false
        },
        sort: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: 0
        },
        vid: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: 0
        },
        cid: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: 0
        }
    }, {
        tableName: 'media_column',
        timestamps: false
    });
};
