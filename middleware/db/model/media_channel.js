module.exports = function (sequelize, DataTypes) {
  return sequelize.define('media_channel', {
    id: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      primaryKey: true,
      autoIncrement: true
    },
    type: {
      type: DataTypes.INTEGER(11),
      allowNull: false
    },
    name: {
      type: DataTypes.STRING(16),
      allowNull: false
    },
    sort: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    logo: {
      type: DataTypes.STRING(128),
      allowNull: false,
      defaultValue: ''
    },
    hidden: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    owner: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    created: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: helper.time
    },
    level: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    leaf: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 1
    },
    pid: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    intro: {
      type: DataTypes.STRING(128),
      allowNull: false,
      defaultValue: 0
    },
    parents: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    item_count: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    template: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    first_sort: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 1
    },
    second_sort: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 1
    }
  }, {
    tableName: 'media_channel',
    timestamps: false
  });
};
