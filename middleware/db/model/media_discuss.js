module.exports = function(sequelize, DataTypes) {
	return sequelize.define('media_discuss', {
		id: {
			type: DataTypes.INTEGER(10).UNSIGNED,
			allowNull: false,
			primaryKey: true,
      autoIncrement: true
		},
    item_id: {
			type: DataTypes.INTEGER(10).UNSIGNED,
			allowNull: false
		},
		reply_id: {
			type: DataTypes.INTEGER(10).UNSIGNED,
			allowNull: false,
			defaultValue: 0
		},
		at_id: {
			type: DataTypes.INTEGER(10).UNSIGNED,
			allowNull: false,
			defaultValue: 0
		},
    user_id: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false
    },
    content: {
      type: DataTypes.STRING(500),
      allowNull: false
    },
    praise_num: {
			type: DataTypes.INTEGER(10).UNSIGNED,
			allowNull: false,
			defaultValue: 0
		},
    reply_num: {
			type: DataTypes.INTEGER(10).UNSIGNED,
			allowNull: false,
			defaultValue: 0
		},
		created: {
			type: DataTypes.INTEGER(10).UNSIGNED,
			allowNull: false,
      defaultValue: helper.time
    }
	}, {
		tableName: 'media_discuss',
		timestamps: false
	});
};