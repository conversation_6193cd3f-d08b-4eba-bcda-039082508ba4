module.exports = function (sequelize, DataTypes) {
    return sequelize.define('media_activity', {
        id: {
            type: DataTypes.INTEGER(10).UNSIGNED,
            allowNull: false,
            primaryKey: true,
            autoIncrement: true
        },
        // 活动名称
        activity_name: {
            type: DataTypes.STRING(64),
            allowNull: false,
            defaultValue: ''
        },
        // 活动时间
        activity_date: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: ''
        },
        // 活动地点
        activity_address: {
            type: DataTypes.STRING(32),
            allowNull: false,
            defaultValue: ''
        },
        // 活动时长
        activity_duration: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: ""
        },
        // 截止时间
        dead_line: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: ''
        },
        // 招募人数
        people_num: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: 0
        },
        // 已报名人数
        register_num:{
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: 0
        },
        // 联系人
        people_name: {
            type: DataTypes.STRING(32),
            allowNull: false,
            defaultValue: ''
        },
        // 联系电话
        people_mobile: {
            type: DataTypes.STRING(32),
            allowNull: false,
            defaultValue: ''
        },
        // 活动地址定位
        position: {
            type: DataTypes.STRING(32),
            allowNull: false,
            defaultValue: ''
        },
        // 活动封面图片
        activity_poster: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: ''
        },
        // 活动内容
        content: {
            type: DataTypes.STRING(5000),
            allowNull: false,
            defaultValue: ''
        },
        created: {
            type: DataTypes.INTEGER(11),
            allowNull: false
        }
    }, {
        tableName: 'media_activity',
        timestamps: false
    });
};
