module.exports = function (sequelize, DataTypes) {
  return sequelize.define('trade_pay_create', {
    id: {
      type: DataTypes.STRING(20),
      allowNull: false,
      primaryKey: true
    },
    payment: {
      type: DataTypes.DECIMAL,
      allowNull: false
    },
    paycode: {
      type: DataTypes.STRING(25),
      allowNull: false
    },
    created: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: helper.time
    },
    trade: {
      type: DataTypes.JSON,
      allowNull: false
    },
    appid: {
      type: DataTypes.STRING(64),
      allowNull: false,
      defaultValue: ''
    },
    notify_time: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    transaction_id: {
      type: DataTypes.STRING(32),
      allowNull: false,
      defaultValue: ''
    },
    params: {
      type: DataTypes.JSON,
      allowNull: false,
      defaultValue: {}
    },
    refund_fee: {
      type: DataTypes.DECIMAL,
      allowNull: false,
      defaultValue: '0.00'
    }
  }, {
    tableName: 'trade_pay_create',
    timestamps: false
  });
};