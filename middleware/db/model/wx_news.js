/* jshint indent: 1 */

module.exports = function(sequelize, DataTypes) {
	return sequelize.define('wx_news', {
		id: {
			type: DataTypes.INTEGER(10).UNSIGNED,
			allowNull: false,
			primaryKey: true
		},
		project_id: {
			type: DataTypes.INTEGER(10),
			allowNull: false
		},
		title: {
			type: DataTypes.STRING(64),
			allowNull: false
		},
		digest: {
			type: DataTypes.STRING(120),
			allowNull: false
		},
		link: {
			type: DataTypes.STRING(300),
			allowNull: false
		},
		cover_url: {
			type: DataTypes.STRING(128),
			allowNull: false
		},
		pid: {
			type: DataTypes.INTEGER(10),
			allowNull: false,
			defaultValue: '0'
		},
		created: {
			type: DataTypes.DATE,
			allowNull: false
		}
	}, {
		tableName: 'wx_news',
		timestamps: false
	});
};
