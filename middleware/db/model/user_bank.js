/* jshint indent: 1 */

module.exports = function(sequelize, DataTypes) {
	return sequelize.define('user_bank', {
		id: {
			type: DataTypes.BIGINT,
			allowNull: false,
			primaryKey: true
		},
		uid: {
			type: DataTypes.INTEGER(11),
			allowNull: false
		},
		type: {
			type: DataTypes.INTEGER(4),
			allowNull: false,
			defaultValue: '0'
		},
		bank_name: {
			type: DataTypes.STRING(25),
			allowNull: false
		},
		user_name: {
			type: DataTypes.STRING(32),
			allowNull: false
		},
		card_no: {
			type: DataTypes.STRING(32),
			allowNull: false
		},
		qrcode: {
			type: DataTypes.STRING(128),
			allowNull: false
		},
		created: {
			type: DataTypes.INTEGER(11),
			allowNull: false
		},
		unbind: {
			type: DataTypes.INTEGER(11),
			allowNull: false
		}
	}, {
		tableName: 'user_bank',
		timestamps: false
	});
};
