module.exports = function (sequelize, DataTypes) {
    return sequelize.define('cms_user_access', {
        id: {
            type: DataTypes.BIGINT,
            allowNull: false,
            primaryKey: true,
            autoIncrement: true
        },
        user_id: {
            type: DataTypes.BIGINT,
            allowNull: false
        },
        roles: {
            type: DataTypes.STRING,
            allowNull: false
        },
        nodes: {
            type: DataTypes.JSON,
            allowNull: true
        }
    }, {
        tableName: 'cms_user_access',
        timestamps: false
    });
};
