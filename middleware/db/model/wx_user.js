module.exports = function (sequelize, DataTypes) {
  return sequelize.define('wx_user', {
    id: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      primaryKey: true,
      autoIncrement: true
    },
    uid: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    appid: {
      type: DataTypes.STRING(20),
      allowNull: false
    },
    openid: {
      type: DataTypes.STRING(32),
      allowNull: false
    },
    unionid: {
      type: DataTypes.STRING(50),
      allowNull: false,
      defaultValue: ''
    },
    first_sub: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    last_sub: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    issub: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    unsub: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    created: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: helper.time
    },
    nickname: {
      type: DataTypes.STRING(32),
      allowNull: false,
      defaultValue: ''
    },
    sex: {
      type: DataTypes.INTEGER(1),
      allowNull: false,
      defaultValue: 0
    },
    headimgurl: {
      type: DataTypes.STRING(256),
      allowNull: false,
      defaultValue: ''
    },
    province: {
      type: DataTypes.STRING(15),
      allowNull: false,
      defaultValue: ''
    },
    city: {
      type: DataTypes.STRING(15),
      allowNull: false,
      defaultValue: ''
    },
    country: {
      type: DataTypes.STRING(15),
      allowNull: false,
      defaultValue: ''
    },
    groupid: {
      type: DataTypes.INTEGER(10),
      allowNull: false,
      defaultValue: 0
    },
    remark: {
      type: DataTypes.STRING(50),
      allowNull: false,
      defaultValue: ''
    }
  }, {
    tableName: 'wx_user',
    timestamps: false
  });
};