module.exports = function (sequelize, DataTypes) {
    return sequelize.define('home_carousel', {
        id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true
        },
        sort: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: 0
        },
        type: {
            type: DataTypes.STRING(2),
            allowNull: false
        },
        cover_url: {
            type: DataTypes.STRING(200),
            allowNull: false,
            defaultValue: ''
        },
        address: {
            type: DataTypes.STRING(200),
            allowNull: false,
            defaultValue: ''
        },
        status: {
            type: DataTypes.STRING(200),
            allowNull: false,
            defaultValue: '0'
        }
    }, {
        tableName: 'home_carousel',
        timestamps: false
    });
};
