module.exports = function (sequelize, DataTypes) {
  return sequelize.define('region', {
    id: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      primaryKey: true,
      autoIncrement: true
    },
    code: {
      type: DataTypes.STRING(12),
      allowNull: false,
    },
    parent_code: {
      type: DataTypes.STRING(12),
      allowNull: false,
      defaultValue: ''
    },
    name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      defaultValue: ''
    },
    sort: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    building: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    unit: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    type: {
      type: DataTypes.STRING(2),
      allowNull: false,
      defaultValue: ''
    },
    remark: {
      type: DataTypes.STRING(500),
      allowNull: false,
      defaultValue: ''
    }
  }, {
    tableName: 'region',
    timestamps: false
  });
};
