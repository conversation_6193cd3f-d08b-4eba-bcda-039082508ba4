
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('media_tag', {
    id: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      primaryKey: true,
      autoIncrement: true
    },
    label: {
      type: DataTypes.STRING(5),
      allowNull: false
    },
    value: {
      type: DataTypes.INTEGER(16),
      allowNull: false,
      defaultValue: 'custom'
    },
    created: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: helper.time
    }
  }, {
    tableName: 'media_tag',
    timestamps: false
  });
};