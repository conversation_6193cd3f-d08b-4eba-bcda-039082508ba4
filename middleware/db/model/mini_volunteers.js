module.exports = function (sequelize, DataTypes) {
    return sequelize.define('mini_volunteers', {
        id: {
            type: DataTypes.INTEGER(10).UNSIGNED,
            allowNull: false,
            primaryKey: true,
            autoIncrement: true
        },
        // 用户唯一标识
        open_id: {
            type: DataTypes.STRING(32),
            allowNull: false,
            defaultValue: ''
        },
        // 姓名
        name: {
            type: DataTypes.STRING(32),
            allowNull: false,
            defaultValue: ''
        },
        // 民族
        group: {
            type: DataTypes.STRING(32),
            allowNull: false,
            defaultValue: ''
        },
        // 年龄
        age: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: 0
        },
        // 身份证
        id_card: {
            type: DataTypes.STRING(32),
            allowNull: false,
            defaultValue: ''
        },
        // 家庭住址
        family_address: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: ''
        },
        // 联系电话
        mobile: {
            type: DataTypes.STRING(32),
            allowNull: false,
            defaultValue: ''
        },
        // 审核标志 1未通过 2通过
        audit:{
            type: DataTypes.INTEGER(1),
            allowNull: false,
            defaultValue: 1
        },
        created: {
            type: DataTypes.INTEGER(11),
            allowNull: false
        }
    }, {
        tableName: 'mini_volunteers',
        timestamps: false
    });
};
