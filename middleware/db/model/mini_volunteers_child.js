module.exports = function (sequelize, DataTypes) {
    return sequelize.define('mini_volunteers_child', {
        id: {
            type: DataTypes.INTEGER(10).UNSIGNED,
            allowNull: false,
            primaryKey: true,
            autoIncrement: true
        },
        // 用户唯一标识
        parent_open_id: {
            type: DataTypes.STRING(32),
            allowNull: false,
            defaultValue: ''
        },
        // 姓名
        name: {
            type: DataTypes.STRING(32),
            allowNull: false,
            defaultValue: ''
        },
        // 身份证
        id_card: {
            type: DataTypes.STRING(32),
            allowNull: false,
            defaultValue: ''
        },
        // 联系电话
        mobile: {
            type: DataTypes.STRING(32),
            allowNull: false,
            defaultValue: ''
        },
        // 监护人姓名
        parent_name: {
            type: DataTypes.STRING(32),
            allowNull: false,
            defaultValue: ''
        },
        // 监护人身份证
        parent_id_card: {
            type: DataTypes.STRING(32),
            allowNull: false,
            defaultValue: ''
        },
        // 监护人联系电话
        parent_mobile: {
            type: DataTypes.STRING(32),
            allowNull: false,
            defaultValue: ''
        },
        // 审核标志 1未通过 2通过
        audit:{
            type: DataTypes.INTEGER(1),
            allowNull: false,
            defaultValue: 1
        },
        created: {
            type: DataTypes.INTEGER(11),
            allowNull: false
        }
    }, {
        tableName: 'mini_volunteers_child',
        timestamps: false
    });
};
