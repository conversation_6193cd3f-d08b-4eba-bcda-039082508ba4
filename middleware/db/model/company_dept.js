module.exports = function (sequelize, DataTypes) {
  return sequelize.define('company_dept', {
    id: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: true,
      primaryKey: true,
      autoIncrement: true
    },
    company_id: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false
    },
    code: {
      type: DataTypes.STRING(15),
      allowNull: false,
      defaultValue: ''
    },
    name: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: ''
    },
    intro: {
      type: DataTypes.STRING(500),
      allowNull: false,
      defaultValue: ''
    },
    sort: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    leader_id: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    phone: {
      type: DataTypes.STRING(15),
      allowNull: false,
      defaultValue: ''
    },
    people: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    level: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    pid: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    leaf: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 1
    },
    parents: {
      type: DataTypes.STRING(128),
      allowNull: false,
      defaultValue: ''
    },
    created: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false
    }
  }, {
    tableName: 'company_dept',
    timestamps: false
  });
}