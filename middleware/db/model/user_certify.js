module.exports = function (sequelize, DataTypes) {
  return sequelize.define('user_certify', {
    id: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      primaryKey: true,
      autoIncrement: true
    },
    type: {
      type: DataTypes.INTEGER(11),
      allowNull: false
    },
    uid: {
      type: DataTypes.INTEGER(10),
      allowNull: false
    },
    cert_name: {
      type: DataTypes.STRING(64),
      allowNull: false
    },
    cert_code: {
      type: DataTypes.STRING(64),
      allowNull: false
    },
    apply_time: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: helper.time
    },
    apply_info: {
      type: DataTypes.JSON,
      allowNull: false
    },
    audit_status: {
      type: DataTypes.INTEGER(4),
      allowNull: false,
      defaultValue: 1
    },
    audit_time: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    audit_remark: {
      type: DataTypes.STRING(255),
      allowNull: false,
      defaultValue: ''
    },
    audit_staff: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    }
  }, {
    tableName: 'user_certify',
    timestamps: false
  });
};