module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    'media_flag',
    {
      id: {
        type: DataTypes.INTEGER(11),
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      item_id: {
        type: DataTypes.INTEGER(11),
        allowNull: false,
      },
      type: {
        type: DataTypes.STRING(200),
        allowNull: false,
      },
      version: {
        type: DataTypes.STRING(200),
        allowNull: false,
      },
      user_id: {
        type: DataTypes.STRING(200),
        allowNull: false,
      },
      user_name: {
        type: DataTypes.STRING(200),
        allowNull: false,
      },
      created: {
        type: DataTypes.INTEGER(10).UNSIGNED,
        allowNull: false,
        defaultValue: helper.time,
      },
    },
    {
      tableName: 'media_flag',
      timestamps: false,
    }
  )
}
