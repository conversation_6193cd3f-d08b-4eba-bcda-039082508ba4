module.exports = function (sequelize, DataTypes) {
  return sequelize.define('media_item', {
    id: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      primaryKey: true,
      autoIncrement: true
    },
    type: {
      type: DataTypes.STRING(32),
      allowNull: false
    },
    status: {
      type: DataTypes.STRING(32),
      allowNull: false
    },
    creator: {
      type: DataTypes.INTEGER(10),
      allowNull: false
    },
    version: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    draft: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    pv: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    uv: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    is_top: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    praise_num: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    reply_num: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    share_num: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    collect_num: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    reward_num: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    allow_reply: {
      type: DataTypes.INTEGER(1),
      allowNull: false,
      defaultValue: 1
    },
    created: {
      type: DataTypes.INTEGER(10),
      allowNull: false
    },
    modified: {
      type: DataTypes.INTEGER(10),
      allowNull: false
    },
    top_expired: {
      type: DataTypes.INTEGER(10),
      allowNull: false,
      defaultValue: 0
    },
    pid: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    sub_users: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: []
    },
    staff_id: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: true,
      defaultValue: 0
    },
    display_date: {
      type: DataTypes.INTEGER(10),
      allowNull: true
    },
  }, {
    tableName: 'media_item',
    timestamps: false
  });
};
