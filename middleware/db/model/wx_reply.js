/* jshint indent: 1 */

module.exports = function(sequelize, DataTypes) {
	return sequelize.define('wx_reply', {
		id: {
			type: DataTypes.BIGINT,
			allowNull: false,
			primaryKey: true
		},
		appid: {
			type: DataTypes.STRING(18),
			allowNull: false
		},
		rule: {
			type: DataTypes.STRING(8),
			allowNull: false
		},
		is_default: {
			type: DataTypes.INTEGER(1),
			allowNull: false,
			defaultValue: '0'
		},
		is_subscribe: {
			type: DataTypes.INTEGER(1),
			allowNull: false,
			defaultValue: '0'
		},
		is_rand: {
			type: DataTypes.INTEGER(1).UNSIGNED,
			allowNull: false,
			defaultValue: '1'
		},
		content: {
			type: DataTypes.TEXT,
			allowNull: false
		},
		modified: {
			type: DataTypes.DATE,
			allowNull: false,
			defaultValue: '0000-00-00 00:00:00'
		}
	}, {
		tableName: 'wx_reply',
		timestamps: false
	});
};
