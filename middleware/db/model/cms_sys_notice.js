module.exports = function (sequelize, DataTypes) {
    return sequelize.define('cms_sys_notice', {
        id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true
        },
        notice_title: {
            type: DataTypes.STRING(50),
            allowNull: false
        },
        notice_content: {
            type: DataTypes.STRING(200),
            allowNull: false
        },
        user_id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: 0
        },
        user_type: {
            type: DataTypes.STRING(2),
            allowNull: false
        },
        status: {
            type: DataTypes.STRING(2),
            allowNull: false,
            defaultValue: '0'
        },
        remark: {
            type: DataTypes.STRING(128),
            allowNull: false,
            defaultValue: ''
        },
        created: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: 0
        },
        pubdate: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: 0
        }
    }, {
        tableName: 'cms_sys_notice',
        timestamps: false
    });
};
