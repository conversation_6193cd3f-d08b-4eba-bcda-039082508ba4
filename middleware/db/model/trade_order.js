module.exports = function (sequelize, DataTypes) {
  return sequelize.define('trade_order', {
    oid: {
      type: DataTypes.STRING(20),
      allowNull: false,
      primaryKey: true
    },
    tid: {
      type: DataTypes.STRING(20),
      allowNull: false
    },
    type: {
      type: DataTypes.STRING(20),
      allowNull: false
    },
    status: {
      type: DataTypes.INTEGER(11),
      allowNull: false
    },
    title: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    thumbnail: {
      type: DataTypes.STRING(128),
      allowNull: false
    },
    cat_id: {
      type: DataTypes.INTEGER(11),
      allowNull: false
    },
    item_id: {
      type: DataTypes.INTEGER(11),
      allowNull: false
    },
    sku_id: {
      type: DataTypes.INTEGER(11),
      allowNull: false
    },
    sku_val: {
      type: DataTypes.STRING(32),
      allowNull: true
    },
    outer_id: {
      type: DataTypes.STRING(20),
      allowNull: false
    },
    outer_no: {
      type: DataTypes.STRING(20),
      allowNull: false
    },
    barcode: {
      type: DataTypes.STRING(20),
      allowNull: false
    },
    price: {
      type: DataTypes.DECIMAL,
      allowNull: false
    },
    del_price: {
      type: DataTypes.STRING(10),
      allowNull: false
    },
    quantity: {
      type: DataTypes.INTEGER(11),
      allowNull: false
    },
    total_fee: {
      type: DataTypes.DECIMAL,
      allowNull: false
    },
    discount_fee: {
      type: DataTypes.DECIMAL,
      allowNull: false
    },
    adjust_fee: {
      type: DataTypes.DECIMAL,
      allowNull: false,
      defaultValue: '0.00'
    },
    payment: {
      type: DataTypes.DECIMAL,
      allowNull: false
    },
    stock_tally: {
      type: DataTypes.INTEGER(4),
      allowNull: false
    },
    freight_val: {
      type: DataTypes.DOUBLE(2),
      allowNull: false
    },
    shipping_type: {
      type: DataTypes.STRING(20),
      allowNull: false
    },
    message: {
      type: DataTypes.JSON,
      allowNull: true
    },
    extend: {
      type: DataTypes.JSON,
      allowNull: false,
      defaultValue: {}
    }
  }, {
    tableName: 'trade_order',
    timestamps: false
  });
};