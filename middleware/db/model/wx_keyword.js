/* jshint indent: 1 */

module.exports = function(sequelize, DataTypes) {
	return sequelize.define('wx_keyword', {
		id: {
			type: DataTypes.INTEGER(11),
			allowNull: false,
			primaryKey: true
		},
		reply_id: {
			type: DataTypes.INTEGER(11),
			allowNull: false
		},
		keyword: {
			type: DataTypes.STRING(16),
			allowNull: false
		},
		full_match: {
			type: DataTypes.INTEGER(1),
			allowNull: false,
			defaultValue: '1'
		}
	}, {
		tableName: 'wx_keyword',
		timestamps: false
	});
};
