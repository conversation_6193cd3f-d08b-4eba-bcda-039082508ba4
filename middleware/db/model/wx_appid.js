/* jshint indent: 1 */

module.exports = function(sequelize, DataTypes) {
	return sequelize.define('wx_appid', {
		id: {
			type: DataTypes.BIGINT,
			allowNull: false,
			primaryKey: true
		},
		appid: {
			type: DataTypes.STRING(18),
			allowNull: false,
			unique: true
		},
		name: {
			type: DataTypes.STRING(16),
			allowNull: false
		},
		alias: {
			type: DataTypes.STRING(32),
			allowNull: false
		},
		head_img: {
			type: DataTypes.STRING(128),
			allowNull: false
		},
		qrcode: {
			type: DataTypes.STRING(218),
			allowNull: false
		},
		token: {
			type: DataTypes.STRING(16),
			allowNull: false
		},
		num_sub: {
			type: DataTypes.INTEGER(11),
			allowNull: false,
			defaultValue: '0'
		},
		num_unsub: {
			type: DataTypes.INTEGER(11),
			allowNull: false,
			defaultValue: '0'
		},
		user_name: {
			type: DataTypes.STRING(32),
			allowNull: false
		},
		secret: {
			type: DataTypes.STRING(32),
			allowNull: false
		},
		encoding_aes_key: {
			type: DataTypes.STRING(64),
			allowNull: false
		},
		mch_id: {
			type: DataTypes.STRING(32),
			allowNull: false,
			defaultValue: ''
		},
		sub_mch_id: {
			type: DataTypes.STRING(32),
			allowNull: false,
			defaultValue: ''
		},
		mch_key: {
			type: DataTypes.STRING(32),
			allowNull: false
		},
		mch_name: {
			type: DataTypes.STRING(32),
			allowNull: false
		},
		template: {
			type: DataTypes.TEXT,
			allowNull: false
		},
		third_appid: {
			type: DataTypes.STRING(18),
			allowNull: false,
			defaultValue: ''
		},
		project_id: {
			type: DataTypes.INTEGER(11),
			allowNull: false,
			defaultValue: '0'
		},
		refresh_token: {
			type: DataTypes.STRING(128),
			allowNull: false,
			defaultValue: ''
		},
		authorized: {
			type: DataTypes.INTEGER(1),
			allowNull: false,
			defaultValue: '1'
		},
		auth_bound: {
			type: DataTypes.DATE,
			allowNull: false
		},
		auth_unbound: {
			type: DataTypes.DATE,
			allowNull: false
		},
		auth_ids: {
			type: DataTypes.STRING(128),
			allowNull: false,
			defaultValue: ''
		},
		service_type: {
			type: DataTypes.INTEGER(4),
			allowNull: false,
			defaultValue: '0'
		},
		verify_type: {
			type: DataTypes.INTEGER(4),
			allowNull: false,
			defaultValue: '-1'
		},
		signature: {
			type: DataTypes.STRING(255),
			allowNull: false
		},
		principal_name: {
			type: DataTypes.STRING(64),
			allowNull: false
		},
		open_store: {
			type: DataTypes.INTEGER(4),
			allowNull: false,
			defaultValue: '0'
		},
		open_scan: {
			type: DataTypes.INTEGER(4),
			allowNull: false,
			defaultValue: '0'
		},
		open_pay: {
			type: DataTypes.INTEGER(4),
			allowNull: false,
			defaultValue: '0'
		},
		open_card: {
			type: DataTypes.INTEGER(4),
			allowNull: false,
			defaultValue: '0'
		},
		open_shake: {
			type: DataTypes.INTEGER(4),
			allowNull: false,
			defaultValue: '0'
		}
	}, {
		tableName: 'wx_appid',
		timestamps: false
	});
};
