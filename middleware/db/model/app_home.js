module.exports = function (sequelize, DataTypes) {
  return sequelize.define('app_home', {
    id: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      primaryKey: true,
      autoIncrement: true
    },
    owner: {
      type: DataTypes.INTEGER(10),
      allowNull: false
    },
    title: {
      type: DataTypes.STRING(64),
      allowNull: false,
      defaultValue: ''
    },
    bgimage: {
      type: DataTypes.STRING(128),
      allowNull: false,
      defaultValue: ''
    },
    bgcolor: {
      type: DataTypes.STRING(25),
      allowNull: false,
      defaultValue: ''
    },
    grayscale: {
      type: DataTypes.INTEGER(10),
      allowNull: false,
      defaultValue: 0
    },
    theme: {
      type: DataTypes.INTEGER(10),
      allowNull: false,
      defaultValue: 0
    },
    menus: {
      type: DataTypes.JSON,
      allowNull: false,
      defaultValue: []
    },
    body: {
      type: DataTypes.JSON,
      allowNull: false,
      defaultValue: []
    },
    modified: {
      type: DataTypes.INTEGER(10),
      allowNull: false
    }
  }, {
    tableName: 'app_home',
    timestamps: false
  });
};