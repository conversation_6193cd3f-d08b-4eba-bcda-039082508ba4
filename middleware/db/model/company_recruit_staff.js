module.exports = function (sequelize, DataTypes) {
    return sequelize.define('company_recruit_staff', {
        id: {
            type: DataTypes.INTEGER(10).UNSIGNED,
            allowNull: false,
            primaryKey: true,
            autoIncrement: true
        },
        name: {
            type: DataTypes.STRING(32),
            allowNull: false,
            defaultValue: ''
        },
        company_id: {
            type: DataTypes.INTEGER(11),
            allowNull: false
        },
        stage: {
            type: DataTypes.INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: 0
        },
        sort: {
            type: DataTypes.INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: 0
        },
        created: {
            type: DataTypes.DATE,
            allowNull: false
        }
    }, {
        tableName: 'company_recruit_staff',
        timestamps: false
    });
};
