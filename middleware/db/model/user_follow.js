/* jshint indent: 1 */

module.exports = function(sequelize, DataTypes) {
	return sequelize.define('user_follow', {
		id: {
			type: DataTypes.INTEGER(10).UNSIGNED,
			allowNull: false,
			primaryKey: true,
            autoIncrement: true
		},
		source_id: {
			type: DataTypes.INTEGER(10).UNSIGNED,
			allowNull: false
		},
		target_id: {
			type: DataTypes.INTEGER(10).UNSIGNED,
			allowNull: false
		},
		created: {
			type: DataTypes.INTEGER(10).UNSIGNED,
			allowNull: false,
            defaultValue: helper.time
		}
	}, {
		tableName: 'user_follow',
		timestamps: false
	});
};
