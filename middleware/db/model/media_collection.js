
module.exports = function(sequelize, DataTypes) {
	return sequelize.define('media_collection', {
		id: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      primaryKey: true,
      autoIncrement: true
		},
    user_id: {
      type: DataTypes.INTEGER(11),
      allowNull: false
    },
    item_id: {
      type: DataTypes.INTEGER(11),
      allowNull: false
    },
    created: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: helper.time
    }
	}, {
		tableName: 'media_collection',
		timestamps: false
	});
};
