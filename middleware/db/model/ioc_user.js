module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    'ioc_user',
    {
      id: {
        type: DataTypes.INTEGER(11),
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      name: {
        type: DataTypes.STRING(40),
        allowNull: false,
        defaultValue: '',
      },
      age: {
        type: DataTypes.INTEGER(3),
        allowNull: false,
      },
      birthday: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      family_relation: {
        type: DataTypes.STRING(12),
        allowNull: false,
      },
      gender: {
        type: DataTypes.STRING(12),
        allowNull: false,
        defaultValue: '',
      },
      nation: {
        type: DataTypes.STRING(12),
        allowNull: false,
        defaultValue: '',
      },
      id_card: {
        type: DataTypes.STRING(15),
        allowNull: false,
        defaultValue: '',
      },
      domicile: {
        type: DataTypes.STRING(12),
        allowNull: false,
        defaultValue: '',
      },
      address: {
        type: DataTypes.STRING(100),
        allowNull: false,
        defaultValue: '',
      },
      room: {
        type: DataTypes.STRING(20),
        allowNull: false,
        defaultValue: '',
      },
      domicile_type: {
        type: DataTypes.STRING(20),
        allowNull: false,
        defaultValue: '',
      },
      domicile_from: {
        type: DataTypes.STRING(20),
        allowNull: false,
        defaultValue: '',
      },
      political: {
        type: DataTypes.STRING(20),
        allowNull: false,
        defaultValue: '',
      },
      special_group: {
        type: DataTypes.STRING(20),
        allowNull: false,
        defaultValue: '',
      },
      famous_type: {
        type: DataTypes.STRING(20),
        allowNull: false,
        defaultValue: '',
      },
      famous_title: {
        type: DataTypes.STRING(20),
        allowNull: false,
        defaultValue: '',
      },
      soldier_type: {
        type: DataTypes.STRING(20),
        allowNull: false,
        defaultValue: '',
      },
      focus_type: {
        type: DataTypes.STRING(20),
        allowNull: false,
        defaultValue: '',
      },
      grid_code: {
        type: DataTypes.STRING(20),
        allowNull: false,
        defaultValue: '',
      },
      grid_name: {
        type: DataTypes.STRING(20),
        allowNull: false,
        defaultValue: '',
      },
      house_code: {
        type: DataTypes.STRING(20),
        allowNull: false,
        defaultValue: '',
      },
      house_name: {
        type: DataTypes.STRING(20),
        allowNull: false,
        defaultValue: '',
      },
      remark: {
        type: DataTypes.STRING(50),
        allowNull: false,
        defaultValue: '',
      },
      created: {
        type: DataTypes.INTEGER(10).UNSIGNED,
        allowNull: false,
        defaultValue: helper.time,
      },
      phone: {
        type: DataTypes.STRING(500),
        allowNull: false,
        defaultValue: '',
      },
      occupation: {
        type: DataTypes.STRING(500),
        allowNull: false,
        defaultValue: '',
      },
      soldier_start_time: {
        type: DataTypes.STRING(500),
        allowNull: false,
        defaultValue: '',
      },
      soldier_end_time: {
        type: DataTypes.STRING(500),
        allowNull: false,
        defaultValue: '',
      },
      soldier_honor_grade: {
        type: DataTypes.STRING(500),
        allowNull: false,
        defaultValue: '',
      },
    },
    {
      tableName: 'ioc_user',
      timestamps: false,
    }
  )
}
