module.exports = function (sequelize, DataTypes) {
    return sequelize.define('company_staff_volunteer', {
        id: {
            type: DataTypes.INTEGER(10).UNSIGNED,
            allowNull: false,
            primaryKey: true,
            autoIncrement: true
        },
        name: {
            type: DataTypes.STRING(32),
            allowNull: false,
            defaultValue: ''
        },
        image_url: {
            type: DataTypes.STRING(1000),
            allowNull: false,
            defaultValue: ''
        },
        content: {
            type: DataTypes.STRING(1000),
            allowNull: false,
            defaultValue: ''
        },
        company_id: {
            type: DataTypes.INTEGER(11),
            allowNull: false
        },
        staff_id: {
            type: DataTypes.INTEGER(11),
            allowNull: false
        },
        sort: {
            type: DataTypes.INTEGER(10),
            allowNull: false
        },
        created: {
            type: DataTypes.INTEGER(10),
            allowNull: false
        }
    }, {
        tableName: 'company_staff_volunteer',
        timestamps: false
    });
};
