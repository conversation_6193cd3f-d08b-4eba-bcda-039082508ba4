module.exports = function (sequelize, DataTypes) {
    return sequelize.define('company_honor', {
        id: {
            type: DataTypes.INTEGER(10).UNSIGNED,
            allowNull: false,
            primaryKey: true,
            autoIncrement: true
        },
        name: {
            type: DataTypes.STRING(32),
            allowNull: false,
            defaultValue: ''
        },
        content: {
            type: DataTypes.STRING(1000),
            allowNull: false,
            defaultValue: ''
        },
        honor_image: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: ''
        },
        company_id: {
            type: DataTypes.INTEGER(11),
            allowNull: false
        },
        honor_date: {
            type: DataTypes.DATE,
            allowNull: false
        },
        created: {
            type: DataTypes.DATE,
            allowNull: false
        }
    }, {
        tableName: 'company_honor',
        timestamps: false
    });
};
