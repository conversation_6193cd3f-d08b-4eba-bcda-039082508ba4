module.exports = function(sequelize, DataTypes) {
	return sequelize.define('trade_base', {
		tid: {
			type: DataTypes.STRING(20),
			allowNull: false,
			primaryKey: true
		},
		type: {
			type: DataTypes.STRING(20),
			allowNull: false
		},
		status: {
      type: DataTypes.STRING(20),
      allowNull: false
		},
		created: {
			type: DataTypes.INTEGER(11),
			allowNull: false
		},
		seller_id: {
			type: DataTypes.INTEGER(11),
			allowNull: false
		},
    seller_name: {
			type: DataTypes.STRING(32),
			allowNull: false
		},
		seller_remark: {
			type: DataTypes.STRING(128),
			allowNull: false,
			defaultValue: ''
		},
		seller_rated: {
			type: DataTypes.INTEGER(11),
			allowNull: false,
			defaultValue: 0
		},
		receiver_name: {
			type: DataTypes.STRING(32),
			allowNull: false
		},
		receiver_mobile: {
			type: DataTypes.STRING(15),
			allowNull: false
		},
		receiver_province: {
			type: DataTypes.STRING(16),
			allowNull: false
		},
		receiver_city: {
			type: DataTypes.STRING(16),
			allowNull: false
		},
		receiver_district: {
			type: DataTypes.STRING(16),
			allowNull: false
		},
		receiver_address: {
			type: DataTypes.STRING(64),
			allowNull: false
		},
		buyer_id: {
			type: DataTypes.INTEGER(11),
			allowNull: false
		},
		buyer_nickname: {
			type: DataTypes.STRING(32),
			allowNull: false
		},
		buyer_level: {
      type: DataTypes.INTEGER(11),
			allowNull: false
		},
		buyer_remark: {
			type: DataTypes.STRING(128),
			allowNull: false
		},
		buyer_ip: {
			type: DataTypes.STRING(15),
			allowNull: false
		},
    buyer_signed: {
      type: DataTypes.INTEGER(11),
      allowNull: false
    },
    buyer_rated: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    buyer_deleted: {
			type: DataTypes.INTEGER(3),
			allowNull: false,
			defaultValue: 0
		},
		anonymous: {
			type: DataTypes.INTEGER(3),
			allowNull: false,
			defaultValue: 0
		},
		referrer_id: {
			type: DataTypes.INTEGER(3),
			allowNull: false
		},
		freight_unit: {
			type: DataTypes.STRING(15),
			allowNull: false,
			defaultValue: 0
		},
		freight_fee: {
			type: DataTypes.DECIMAL,
			allowNull: false
		},
		total_quantity: {
			type: DataTypes.INTEGER(10).UNSIGNED,
			allowNull: false
		},
		total_fee: {
			type: DataTypes.DECIMAL,
			allowNull: false
		},
		discount_fee: {
			type: DataTypes.DECIMAL,
			allowNull: false
		},
		adjust_fee: {
			type: DataTypes.DECIMAL,
			allowNull: false
		},
		payment: {
			type: DataTypes.DECIMAL,
			allowNull: false
		},
		paid_fee: {
			type: DataTypes.DECIMAL,
			allowNull: false
		},
		pay_timeout: {
			type: DataTypes.INTEGER(10).UNSIGNED,
			allowNull: false
		},
		pay_type: {
			type: DataTypes.STRING(16),
			allowNull: false,
			defaultValue: ''
		},
		pay_time: {
			type: DataTypes.INTEGER(10),
			allowNull: false,
			defaultValue: 0
		},
		shipping_type: {
			type: DataTypes.STRING(16),
			allowNull: false
		},
		express_id: {
			type: DataTypes.INTEGER(11),
			allowNull: false,
			defaultValue: 0
		},
		deliver_time: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
		deliver_info: {
			type: DataTypes.JSON,
			allowNull: false,
			defaultValue: {}
		},
		refund_status: {
			type: DataTypes.STRING(16),
			allowNull: false,
			defaultValue: 'no_refund'
		},
		refund_fee: {
			type: DataTypes.DECIMAL,
			allowNull: false,
			defaultValue: '0.00'
		},
		end_time: {
			type: DataTypes.INTEGER(11),
			allowNull: false,
			defaultValue: 0
		},
		end_type: {
			type: DataTypes.STRING(20),
			allowNull: false,
			defaultValue: 'none'
		},
		end_reason: {
			type: DataTypes.STRING(20),
			allowNull: false,
			defaultValue: ''
		},
		invoice_kind: {
			type: DataTypes.INTEGER(11),
			allowNull: false,
			defaultValue: 0
		},
		invoice_type: {
			type: DataTypes.STRING(16),
			allowNull: false,
			defaultValue: ''
		},
    invoice_name: {
      type: DataTypes.STRING(32),
      allowNull: false,
      defaultValue: ''
    },
    invoice_email: {
      type: DataTypes.STRING(32),
      allowNull: false,
      defaultValue: ''
    },
    modified: {
      type: DataTypes.INTEGER(11),
      allowNull: true
    }
	}, {
		tableName: 'trade_base',
		timestamps: false
	});
};