module.exports = function (sequelize, DataTypes) {
  return sequelize.define('media_image', {
    id: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      primaryKey: true,
      autoIncrement: true
    },
    autoplay: {
      type: DataTypes.INTEGER(5),
      allowNull: false,
      defaultValue: 1
    },
    interval: {
      type: DataTypes.INTEGER(5),
      allowNull: false,
      defaultValue: 5
    },
    animation: {
      type: DataTypes.INTEGER(5),
      allowNull: false,
      defaultValue: 0
    },
    bgmusic: {
      type: DataTypes.JSON,
      allowNull: false
    },
    bgvoice: {
      type: DataTypes.JSON,
      allowNull: false
    }
  }, {
    tableName: 'media_image',
    timestamps: false
  });
};