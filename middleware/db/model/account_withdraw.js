/* jshint indent: 1 */

module.exports = function(sequelize, DataTypes) {
	return sequelize.define('account_withdraw', {
		id: {
			type: DataTypes.INTEGER(10).UNSIGNED,
			allowNull: false,
			primaryKey: true,
			autoIncrement: true
		},
		account_id: {
			type: DataTypes.INTEGER(10),
			allowNull: false
		},
		amount: {
			type: DataTypes.DECIMAL,
			allowNull: false
		},
		card_type: {
			type: DataTypes.INTEGER(4),
			allowNull: false
		},
		card_master: {
			type: DataTypes.STRING(20),
			allowNull: false
		},
		card_name: {
			type: DataTypes.STRING(32),
			allowNull: false
		},
		card_number: {
			type: DataTypes.STRING(20),
			allowNull: false
		},
		user_remark: {
			type: DataTypes.STRING(100),
			allowNull: false,
			defaultValue: ''
		},
		created:{
			type: DataTypes.INTEGER(10),
			allowNull: false,
			defaultValue: helper.time
		},
		audit_time: {
			type: DataTypes.INTEGER(10),
			allowNull: false,
			defaultValue: 0
		},
		audit_result: {
			type: DataTypes.INTEGER(3),
			allowNull: false,
			defaultValue: 0
		},
    refusal_reason: {
      type: DataTypes.STRING(255),
      allowNull: false,
      defaultValue: ''
    },
    audit_remark: {
      type: DataTypes.STRING(255),
      allowNull: false,
      defaultValue: ''
    },
		audit_username: {
			type: DataTypes.STRING(255),
			allowNull: false,
			defaultValue: ''
		}
	}, {
		tableName: 'account_withdraw',
		timestamps: false
	});
};
