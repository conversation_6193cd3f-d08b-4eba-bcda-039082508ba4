module.exports = function(sequelize, DataTypes) {
  return sequelize.define('friend_link', {
    id: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      primaryKey: true,
      autoIncrement: true
    },
    title: {
      type: DataTypes.STRING(32),
      allowNull: false,
      defaultValue: ''
    },
    url: {
      type: DataTypes.STRING(128),
      allowNull: false,
      defaultValue: ''
    },
    icon: {
      type: DataTypes.STRING(128),
      allowNull: false,
      defaultValue: ''
    },
    seq: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 100
    },
    visible: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 1
    }
  }, {
    tableName: 'friend_link',
    timestamps: false
  });
};
