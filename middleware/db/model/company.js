module.exports = function (sequelize, DataTypes) {
  return sequelize.define('company', {
    id: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: true,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(64),
      allowNull: false,
      defaultValue: ''
    },
    alias: {
      type: DataTypes.STRING(16),
      allowNull: false,
      defaultValue: ''
    },
    logo: {
      type: DataTypes.STRING(128),
      allowNull: false,
      defaultValue: ''
    },
    status: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    intro: {
      type: DataTypes.STRING(500),
      allowNull: false,
      defaultValue: ''
    },
    created: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false
    },
    industry_code: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    stage_code: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    scale_code: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    website: {
      type: DataTypes.STRING(128),
      allowNull: false,
      defaultValue: ''
    },
    start_date: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    oper_name: {
      type: DataTypes.STRING(32),
      allowNull: false,
      defaultValue: ''
    },
    license_image: {
      type: DataTypes.STRING(128),
      allowNull: false,
      defaultValue: ''
    },
    license_range: {
      type: DataTypes.STRING(255),
      allowNull: false,
      defaultValue: ''
    },
    credit_code: {
      type: DataTypes.STRING(32),
      allowNull: false,
      defaultValue: ''
    },
    register_address: {
      type: DataTypes.STRING(128),
      allowNull: false,
      defaultValue: ''
    },
    work_start: {
      type: DataTypes.STRING(5),
      allowNull: false,
      defaultValue: ''
    },
    work_end: {
      type: DataTypes.STRING(5),
      allowNull: false,
      defaultValue: ''
    },
    overtime: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    rest_code: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    images: {
      type: DataTypes.JSON,
      allowNull: true
    },
    video: {
      type: DataTypes.JSON,
      allowNull: true
    },
    service_phone: {
      type: DataTypes.STRING(15),
      allowNull: false,
      defaultValue: ''
    },
    province_code: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    city_code: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    district_code: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    address: {
      type: DataTypes.STRING(128),
      allowNull: false,
      defaultValue: ''
    },
    lng: {
      type: DataTypes.DOUBLE,
      allowNull: false,
      defaultValue: 0
    },
    lat: {
      type: DataTypes.DOUBLE,
      allowNull: false,
      defaultValue: 0
    },
    dept_num: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    staff_num: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    modified: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    company_image: {
      type: DataTypes.STRING(255),
      allowNull: false,
      defaultValue: ''
    },
    company_background: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
  }, {
    tableName: 'company',
    timestamps: false
  });
};
