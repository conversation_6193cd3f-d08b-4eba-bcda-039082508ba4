module.exports = function (sequelize, DataTypes) {
  return sequelize.define('cms_sys_menu', {
    id: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      primaryKey: true,
      autoIncrement: true
    },
    title: {
      type: DataTypes.STRING(6),
      allowNull: false
    },
    type: {
      type: DataTypes.INTEGER(4).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    icon: {
      type: DataTypes.STRING(16),
      allowNull: false,
      defaultValue: ''
    },
    pid: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    path: {
      type: DataTypes.STRING(128),
      allowNull: false,
      defaultValue: ''
    },
    alias: {
      type: DataTypes.STRING(32),
      allowNull: false,
      defaultValue: ''
    },
    sort: {
      type: DataTypes.INTEGER(6),
      allowNull: false,
      defaultValue: 0
    },
    disabled: {
      type: DataTypes.INTEGER(4),
      allowNull: false,
      defaultValue: 0
    }
  }, {
    tableName: 'cms_sys_menu',
    timestamps: false
  });
};
