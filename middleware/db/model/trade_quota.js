/* jshint indent: 1 */

module.exports = function(sequelize, DataTypes) {
	return sequelize.define('trade_quota', {
		id: {
			type: DataTypes.INTEGER(11),
			allowNull: false,
			primaryKey: true
		},
		key: {
			type: DataTypes.STRING(32),
			allowNull: false,
			unique: true
		},
		quantity: {
			type: DataTypes.INTEGER(4),
			allowNull: false,
			defaultValue: '1'
		},
		type: {
			type: DataTypes.STRING(16),
			allowNull: false
		},
		value: {
			type: DataTypes.STRING(20),
			allowNull: false
		},
		modify: {
			type: DataTypes.INTEGER(11),
			allowNull: false
		}
	}, {
		tableName: 'trade_quota',
		timestamps: false
	});
};
