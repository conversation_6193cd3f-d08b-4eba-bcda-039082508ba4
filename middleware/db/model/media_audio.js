module.exports = function (sequelize, DataTypes) {
  return sequelize.define('media_audio', {
    id: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      primaryKey: true,
      autoIncrement: true
    },
    address: {
      type: DataTypes.STRING(128),
      allowNull: false
    },
    file_size: {
      type: DataTypes.INTEGER(20),
      allowNull: false
    },
    duration: {
      type: DataTypes.DECIMAL,
      allowNull: false,
      defaultValue: 0
    }
  }, {
    tableName: 'media_audio',
    timestamps: false
  });
}