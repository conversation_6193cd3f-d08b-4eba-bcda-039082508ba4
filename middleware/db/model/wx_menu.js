/* jshint indent: 1 */

module.exports = function(sequelize, DataTypes) {
	return sequelize.define('wx_menu', {
		id: {
			type: DataTypes.INTEGER(11).UNSIGNED,
			allowNull: false,
			primaryKey: true
		},
		appid: {
			type: DataTypes.STRING(18),
			allowNull: false,
			defaultValue: ''
		},
		button: {
			type: DataTypes.STRING(3500),
			allowNull: false
		},
		modify_time: {
			type: DataTypes.DATE,
			allowNull: false
		},
		is_matchrule: {
			type: DataTypes.INTEGER(1),
			allowNull: false,
			defaultValue: '0'
		},
		group_id: {
			type: DataTypes.INTEGER(10).UNSIGNED,
			allowNull: false
		},
		sex: {
			type: DataTypes.INTEGER(1).UNSIGNED,
			allowNull: false
		},
		country: {
			type: DataTypes.STRING(16),
			allowNull: false
		},
		province: {
			type: DataTypes.STRING(16),
			allowNull: false
		},
		city: {
			type: DataTypes.STRING(16),
			allowNull: false
		},
		client_platform_type: {
			type: DataTypes.STRING(16),
			allowNull: false
		},
		language: {
			type: DataTypes.STRING(16),
			allowNull: false
		}
	}, {
		tableName: 'wx_menu',
		timestamps: false
	});
};
