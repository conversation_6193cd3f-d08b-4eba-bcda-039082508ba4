module.exports = function (sequelize, DataTypes) {
  return sequelize.define('cms_sys_role', {
    id: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(8),
      allowNull: false
    },
    menus: {
      type: DataTypes.JSON,
      allowNull: false,
      defaultValue: []
    },
    nodes: {
      type: DataTypes.JSON,
      allowNull: false,
      defaultValue: {}
    },
    enabled: {
      type: DataTypes.INTEGER(1),
      allowNull: false,
      defaultValue: 0
    },
    remark: {
      type: DataTypes.STRING(50),
      allowNull: false,
      defaultValue: ''
    },
    group: {
      type: DataTypes.STRING(8),
      allowNull: false,
      defaultValue: ''
    }
  }, {
    tableName: 'cms_sys_role',
    timestamps: false
  });
};
