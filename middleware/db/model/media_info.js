module.exports = function (sequelize, DataTypes) {
  return sequelize.define('media_info', {
    id: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      primaryKey: true,
      autoIncrement: true
    },
    editor: {
      type: DataTypes.INTEGER(10),
      allowNull: false
    },
    item_id: {
      type: DataTypes.INTEGER(11),
      allowNull: false
    },
    title: {
      type: DataTypes.STRING(64),
      allowNull: false,
      defaultValue: ''
    },
    source: {
      type: DataTypes.STRING(25),
      allowNull: false,
      defaultValue: ''
    },
    subtitle: {
      type: DataTypes.STRING(64),
      allowNull: false,
      defaultValue: ''
    },
    abstract: {
      type: DataTypes.STRING(250),
      allowNull: false,
      defaultValue: ''
    },
    cover_url: {
      type: DataTypes.STRING(128),
      allowNull: false,
      defaultValue: ''
    },
    pubdate: {
      type: DataTypes.INTEGER(10),
      allowNull: false,
      defaultValue: helper.time
    },
    year: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: function () {
        return helper.date('YYYY');
      }
    },
    area: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: ''
    },
    language: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: ''
    },
    province_code: {
      type: DataTypes.INTEGER(10),
      allowNull: false,
      defaultValue: 0
    },
    city_code: {
      type: DataTypes.INTEGER(10),
      allowNull: false,
      defaultValue: 0
    },
    district_code: {
      type: DataTypes.INTEGER(10),
      allowNull: false,
      defaultValue: 0
    },
    created: {
      type: DataTypes.INTEGER(11),
      allowNull: false
    },
    detail_url: {
      type: DataTypes.STRING(128),
      allowNull: false,
      defaultValue: ''
    },
    top_left_corner: {
      type: DataTypes.STRING(10),
      allowNull: false,
      defaultValue: ''
    },
    lower_right_corner: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: ''
    }
  }, {
    tableName: 'media_info',
    timestamps: false
  });
};