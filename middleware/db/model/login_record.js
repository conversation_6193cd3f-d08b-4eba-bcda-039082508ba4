
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('login_record', {
    id: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      primaryKey: true,
      autoIncrement: true
    },
    open_id: {
      type: DataTypes.STRING(32),
      allowNull: false
    },
    flag: {
      type: DataTypes.STRING(16),
      allowNull: false,
      defaultValue: 'custom'
    },
  }, {
    tableName: 'login_record',
    timestamps: false
  });
};