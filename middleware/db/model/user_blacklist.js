/* jshint indent: 1 */

module.exports = function(sequelize, DataTypes) {
	return sequelize.define('user_blacklist', {
		id: {
			type: DataTypes.INTEGER(11),
			allowNull: false,
			primaryKey: true
		},
		uid: {
			type: DataTypes.INTEGER(11),
			allowNull: false
		},
		function: {
			type: DataTypes.INTEGER(11),
			allowNull: false
		},
		created: {
			type: DataTypes.INTEGER(11),
			allowNull: false
		},
		expired: {
			type: DataTypes.INTEGER(11),
			allowNull: false
		},
		reason: {
			type: DataTypes.STRING(128),
			allowNull: false
		},
		creator: {
			type: DataTypes.STRING(25),
			allowNull: false
		},
		unlock_time: {
			type: DataTypes.INTEGER(11),
			allowNull: false
		},
		unlock_admin: {
			type: DataTypes.STRING(25),
			allowNull: false
		}
	}, {
		tableName: 'user_blacklist',
		timestamps: false
	});
};
