/* jshint indent: 1 */

module.exports = function(sequelize, DataTypes) {
	return sequelize.define('trade_pay_refund', {
		id: {
			type: DataTypes.STRING(20),
			allowNull: false,
			primaryKey: true
		},
		refund_id: {
      type: DataTypes.INTEGER(11),
      allowNull: false
		},
    pay_id: {
      type: DataTypes.STRING(20),
      allowNull: false
		},
    refund_fee: {
      type: DataTypes.DECIMAL,
      allowNull: false
    },
    created: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
			defaultValue: helper.time
    },
    notify_time: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    params: {
      type: DataTypes.JSON,
      allowNull: false,
      defaultValue: {}
    },
    transaction_id: {
      type: DataTypes.STRING(32),
      allowNull: false,
      defaultValue: ''
    },
    errcode: {
      type: DataTypes.INTEGER(11),
			allowNull: false,
			defaultValue: 0
		},
    errmsg: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: ''
    }
	}, {
		tableName: 'trade_pay_refund',
		timestamps: false
	});
};
