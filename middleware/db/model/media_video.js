module.exports = function (sequelize, DataTypes) {
  return sequelize.define('media_video', {
    id: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      primaryKey: true,
      autoIncrement: true
    },
    file_url: {
      type: DataTypes.STRING(128),
      allowNull: false
    },
    file_size: {
      type: DataTypes.INTEGER(20),
      allowNull: false
    },
    address: {
      type: DataTypes.JSON,
      allowNull: false,
      defaultValue: []
    },
    duration: {
      type: DataTypes.DECIMAL,
      allowNull: false,
      defaultValue: 0
    },
    width: {
      type: DataTypes.INTEGER(10),
      allowNull: false,
      defaultValue: 0
    },
    height: {
      type: DataTypes.INTEGER(10),
      allowNull: false,
      defaultValue: 0
    },
    pano: {
      type: DataTypes.DECIMAL,
      allowNull: false,
      defaultValue: 0
    }
  }, {
    tableName: 'media_video',
    timestamps: false
  });
};