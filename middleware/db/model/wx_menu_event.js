/* jshint indent: 1 */

module.exports = function(sequelize, DataTypes) {
	return sequelize.define('wx_menu_event', {
		id: {
			type: DataTypes.INTEGER(10).UNSIGNED,
			allowNull: false,
			primaryKey: true
		},
		menu_id: {
			type: DataTypes.INTEGER(10).UNSIGNED,
			allowNull: false
		},
		type: {
			type: DataTypes.STRING(16),
			allowNull: false
		},
		content: {
			type: DataTypes.TEXT,
			allowNull: false
		},
		event_key: {
			type: DataTypes.STRING(16),
			allowNull: false
		}
	}, {
		tableName: 'wx_menu_event',
		timestamps: false
	});
};
