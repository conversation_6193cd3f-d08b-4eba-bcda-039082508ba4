/* jshint indent: 1 */

module.exports = function(sequelize, DataTypes) {
	return sequelize.define('user_address', {
		id: {
			type: DataTypes.BIGINT,
			allowNull: false,
			primaryKey: true,
      autoIncrement: true
		},
		uid: {
			type: DataTypes.INTEGER(10).UNSIGNED,
			allowNull: false
		},
		type: {
			type: DataTypes.INTEGER(1),
			allowNull: false,
			defaultValue: '0'
		},
		receiver_name: {
			type: DataTypes.STRING(16),
			allowNull: false
		},
		receiver_mobile: {
			type: DataTypes.STRING(25),
			allowNull: false
		},
		country_code: {
			type: DataTypes.INTEGER(11),
			allowNull: false
		},
		province_code: {
			type: DataTypes.INTEGER(8),
			allowNull: false
		},
		city_code: {
			type: DataTypes.INTEGER(8),
			allowNull: false
		},
		county_code: {
			type: DataTypes.INTEGER(8),
			allowNull: false
		},
		receiver_detail: {
			type: DataTypes.STRING(218),
			allowNull: false
		},
		receiver_zip: {
			type: DataTypes.STRING(6),
			allowNull: false,
      defaultValue: ''
		},
		is_default: {
			type: DataTypes.INTEGER(4),
			allowNull: false,
			defaultValue: 0
		}
	}, {
		tableName: 'user_address',
		timestamps: false
	});
};
