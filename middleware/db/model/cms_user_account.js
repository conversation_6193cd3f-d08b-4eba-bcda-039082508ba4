module.exports = function(sequelize, DataTypes) {
	return sequelize.define('cms_user_account', {
		id: {
			type: DataTypes.BIGINT,
			allowNull: false,
			primaryKey: true,
      autoIncrement: true
		},
		username: {
			type: DataTypes.STRING(32),
			allowNull: false,
			unique: true
		},
		type: {
			type: DataTypes.INTEGER(4),
			allowNull: false
		},
		uid: {
			type: DataTypes.BIGINT,
			allowNull: false
		},
		last_login: {
			type: DataTypes.INTEGER(10).UNSIGNED,
			allowNull: false,
      defaultValue: helper.time
		}
	}, {
		tableName: 'cms_user_account',
		timestamps: false
	});
};
