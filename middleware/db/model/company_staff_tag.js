module.exports = function (sequelize, DataTypes) {
    return sequelize.define('company_staff_tag', {
        id: {
            type: DataTypes.INTEGER(10).UNSIGNED,
            allowNull: true,
            primaryKey: true,
            autoIncrement: true
        },
        company_id: {
            type: DataTypes.INTEGER(10).UNSIGNED,
            allowNull: false
        },
        name: {
            type: DataTypes.STRING(32),
            allowNull: false
        },
        sort: {
            type: DataTypes.INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: 0
        },
        people: {
            type: DataTypes.INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: 0
        },
        hidden: {
            type: DataTypes.INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: 0
        },
        created: {
            type: DataTypes.INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: helper.time
        }
    }, {
        tableName: 'company_staff_tag',
        timestamps: false
    });
}
