/* jshint indent: 1 */

module.exports = function(sequelize, DataTypes) {
  return sequelize.define('trade_prepay', {
    id: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      primaryKey: true,
      autoIncrement: true
    },
    sign: {
      type: DataTypes.STRING(32),
      allowNull: false
    },
    payment: {
      type: DataTypes.DECIMAL,
      allowNull: false
    },
    title: {
      type: DataTypes.STRING(32),
      allowNull: false
    },
    trade: {
      type: DataTypes.JSON,
      allowNull: false
    },
    timeout: {
      type: DataTypes.INTEGER(11),
      allowNull: false
    }
  }, {
    tableName: 'trade_prepay',
    timestamps: false
  });
};