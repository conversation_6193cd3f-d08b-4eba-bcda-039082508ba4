module.exports = function (sequelize, DataTypes) {
  return sequelize.define('wallet_account', {
    id: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      primaryKey: true,
      autoIncrement: true
    },
    type: {
      type: DataTypes.INTEGER(10),
      allowNull: false
    },
    number: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: ''
    },
    status: {
      type: DataTypes.STRING(20),
      allowNull: false
    },
    owner_id: {
      type: DataTypes.INTEGER(11),
      allowNull: false
    },
    owner_type: {
      type: DataTypes.INTEGER(11),
      allowNull: false
    },
    total_balance: {
      type: DataTypes.DECIMAL,
      allowNull: false,
      defaultValue: '0.00'
    },
    usable_balance: {
      type: DataTypes.DECIMAL,
      allowNull: false,
      defaultValue: '0.00'
    },
    locked_balance: {
      type: DataTypes.DECIMAL,
      allowNull: false,
      defaultValue: '0.00'
    },
    frozen_balance: {
      type: DataTypes.DECIMAL,
      allowNull: false,
      defaultValue: '0.00'
    },
    created: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: helper.time
    },
    activated: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    expired: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    frozen_time: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    frozen_reason: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    logout_time: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    deleted: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    }
  }, {
    tableName: 'wallet_account',
    timestamps: false
  });
};
