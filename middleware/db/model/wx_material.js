/* jshint indent: 1 */

module.exports = function(sequelize, DataTypes) {
	return sequelize.define('wx_material', {
		media_id: {
			type: DataTypes.STRING(64),
			allowNull: false,
			primaryKey: true
		},
		appid: {
			type: DataTypes.STRING(18),
			allowNull: false
		},
		type: {
			type: DataTypes.STRING(16),
			allowNull: false
		},
		title: {
			type: DataTypes.STRING(64),
			allowNull: false
		},
		url: {
			type: DataTypes.STRING(218),
			allowNull: false
		},
		content: {
			type: DataTypes.TEXT,
			allowNull: false
		},
		update_time: {
			type: DataTypes.DATE,
			allowNull: false
		}
	}, {
		tableName: 'wx_material',
		timestamps: false
	});
};
