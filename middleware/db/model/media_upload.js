module.exports = function(sequelize, DataTypes) {
	return sequelize.define('media_upload', {
		id: {
			type: DataTypes.INTEGER(11),
			allowNull: false,
			primaryKey: true,
			autoIncrement: true
		},
		bucket: {
			type: DataTypes.STRING(20),
			allowNull: false,
      defaultValue: ''
		},
		object: {
			type: DataTypes.STRING(128),
			allowNull: false,
      defaultValue: ''
		},
		mime: {
			type: DataTypes.STRING(128),
			allowNull: false,
      defaultValue: ''
		},
		name: {
			type: DataTypes.STRING(20),
			allowNull: false,
			defaultValue: ''
		},
		type: {
			type: DataTypes.INTEGER(10),
			allowNull: false,
			defaultValue: 0
		},
		ext: {
			type: DataTypes.STRING(8),
			allowNull: false,
			defaultValue: ''
		},
		size: {
			type: DataTypes.BIGINT(20),
			allowNull: false,
			defaultValue: 0
		},
		private: {
			type: DataTypes.INTEGER(1),
			allowNull: false,
			defaultValue: 0
		},
		creator: {
			type: DataTypes.INTEGER(10),
			allowNull: false,
			defaultValue: 0
		},
		width: {
			type: DataTypes.INTEGER(10),
			allowNull: false,
			defaultValue: 0
		},
		height: {
			type: DataTypes.INTEGER(10),
			allowNull: false,
			defaultValue: 0
		},
		duration: {
			type: DataTypes.DOUBLE,
			allowNull: false,
			defaultValue: 0
		},
		md5: {
			type: DataTypes.STRING(32),
			allowNull: false,
			defaultValue: ''
		},
		hash: {
			type: DataTypes.STRING(64),
			allowNull: false,
			defaultValue: ''
		},
		created: {
			type: DataTypes.INTEGER(10),
			allowNull: false,
			defaultValue: helper.time
		}
	}, {
		tableName: 'media_upload',
		timestamps: false
	});
};