/* jshint indent: 1 */

module.exports = function(sequelize, DataTypes) {
	return sequelize.define('trade_refund', {
		id: {
			type: DataTypes.INTEGER(10).UNSIGNED,
			allowNull: false,
			primaryKey: true,
      autoIncrement: true
		},
    tid: {
      type: DataTypes.STRING(20),
      allowNull: false
    },
		oid: {
      type: DataTypes.STRING(20),
      allowNull: false,
			defaultValue: '0'
		},
    biz_type: {
      type: DataTypes.STRING(20),
      allowNull: false
		},
		refund_status: {
      type: DataTypes.STRING(20),
      allowNull: false
    },
		refund_fee: {
			type: DataTypes.DECIMAL,
			allowNull: false,
      defaultValue: '0.00'
		},
    refund_reason: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false
    },
		refund_quantity: {
			type: DataTypes.INTEGER(10).UNSIGNED,
			allowNull: false,
			defaultValue: '0'
		},
    apply_fee: {
      type: DataTypes.DECIMAL,
      allowNull: false
    },
    apply_time: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
			defaultValue: helper.time
    },
    only_refund: {
      type: DataTypes.INTEGER(3),
      allowNull: false,
      defaultValue: 0
		},
    is_received: {
			type: DataTypes.INTEGER(3),
			allowNull: false,
			defaultValue: 0
		},
		remark: {
			type: DataTypes.STRING(200),
			allowNull: false,
			defaultValue: ''
		},
    express: {
      type: DataTypes.STRING(128),
      allowNull: false,
      defaultValue: ''
    },
    images: {
      type: DataTypes.STRING(500),
      allowNull: false,
      defaultValue: ''
    },
    audit_user: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: ''
		},
    audit_time: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    audit_fee: {
      type: DataTypes.DECIMAL,
      allowNull: false,
      defaultValue: '0.00'
    },
    audit_post: {
      type: DataTypes.DECIMAL,
      allowNull: false,
      defaultValue: '0.00'
    },
    audit_remark: {
      type: DataTypes.STRING(128),
      allowNull: false,
      defaultValue: ''
    },
    refusal_reason: {
      type: DataTypes.STRING(128),
      allowNull: false,
      defaultValue: ''
		},
    seller_id: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
		},
    receiver_name: {
      type: DataTypes.STRING(16),
      allowNull: false,
      defaultValue: ''
    },
    receiver_mobile: {
      type: DataTypes.BIGINT,
      allowNull: false,
      defaultValue: ''
    },
    receiver_address: {
      type: DataTypes.STRING(128),
      allowNull: false,
      defaultValue: ''
    },
    refund_user: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: ''
		},
    refund_end: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
		},
    refund_error: {
      type: DataTypes.JSON,
      allowNull: false,
      defaultValue: []
    }
	}, {
		tableName: 'trade_refund',
		timestamps: false
	});
};
