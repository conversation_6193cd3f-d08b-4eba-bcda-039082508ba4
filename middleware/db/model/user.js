module.exports = function (sequelize, DataTypes) {
  return sequelize.define('user', {
    id: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: true,
      primaryKey: true,
      autoIncrement: true
    },
    pid: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    name: {
      type: DataTypes.STRING(32),
      allowNull: false,
      defaultValue: ''
    },
    sex: {
      type: DataTypes.INTEGER(3).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    mobile: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: ''
    },
    mobile_area: {
      type: DataTypes.STRING(5),
      allowNull: false,
      defaultValue: ''
    },
    nickname: {
      type: DataTypes.STRING(16),
      allowNull: false,
      defaultValue: ''
    },
    headimg: {
      type: DataTypes.STRING(256),
      allowNull: false,
      defaultValue: ''
    },
    username: {
      type: DataTypes.STRING(32),
      allowNull: false,
      defaultValue: ''
    },
    email: {
      type: DataTypes.STRING(64),
      allowNull: false,
      defaultValue: ''
    },
    password: {
      type: DataTypes.STRING(32),
      allowNull: false,
      defaultValue: ''
    },
    last_login: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    created: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: helper.time
    },
    province_code: {
      type: DataTypes.STRING(10),
      allowNull: false,
      defaultValue: 0
    },
    city_code: {
      type: DataTypes.STRING(10),
      allowNull: false,
      defaultValue: 0
    },
    district_code: {
      type: DataTypes.STRING(10),
      allowNull: false,
      defaultValue: 0
    },
    realname_status: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    intro: {
      type: DataTypes.STRING(255),
      allowNull: false,
      defaultValue: ''
    },
    nation: {
      type: DataTypes.STRING(6),
      allowNull: false,
      defaultValue: 0
    },
    birthday: {
      type: DataTypes.STRING(10),
      allowNull: false,
      defaultValue: '0000-00-00'
    },
    school_name: {
      type: DataTypes.STRING(32),
      allowNull: false,
      defaultValue: ''
    },
    education: {
      type: DataTypes.STRING(10),
      allowNull: false,
      defaultValue: 0
    },
    join_work: {
      type: DataTypes.STRING(10),
      allowNull: false,
      defaultValue: '0000-00-00'
    },
    position_name: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: ''
    },
    company_id: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    audit_status: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    status: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    },
    private_menu_name: {
      type: DataTypes.STRING(255),
      allowNull: false,
      defaultValue: ''
    },
    home_datasource: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: 0
    }
  }, {
    tableName: 'user',
    timestamps: false
  });
};
