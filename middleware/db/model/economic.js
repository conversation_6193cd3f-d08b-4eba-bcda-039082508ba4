module.exports = function (sequelize, DataTypes) {
    return sequelize.define('economic', {
      id: {
        type: DataTypes.INTEGER(11),
        allowNull: false,
        primaryKey: true,
        autoIncrement: true
      },
      sort: {
        type: DataTypes.INTEGER(10).UNSIGNED,
        allowNull: false,
        defaultValue: 0
      },
      company_name: {
        type: DataTypes.STRING(500),
        allowNull: false,
        defaultValue: ''
      },
      grid_name: {
        type: DataTypes.STRING(50),
        allowNull: false,
        defaultValue: ''
      },
      type: {
        type: DataTypes.STRING(2),
        allowNull: false,
        defaultValue: ''
      },
      remark: {
        type: DataTypes.STRING(500),
        allowNull: false,
        defaultValue: ''
      },
      legal_person: {
        type: DataTypes.STRING(50),
        allowNull: false,
        defaultValue: ''
      },
      phone: {
        type: DataTypes.STRING(500),
        allowNull: false,
        defaultValue: ''
      },
      address: {
        type: DataTypes.STRING(500),
        allowNull: false,
        defaultValue: ''
      }
    }, {
      tableName: 'economic',
      timestamps: false
    });
  };