module.exports = function (sequelize, DataTypes) {
  return sequelize.define('message_sms', {
    id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true,
      autoIncrement: true
    },
    type: {
      type: DataTypes.STRING(25),
      allowNull: false
    },
    mobile: {
      type: DataTypes.STRING(25),
      allowNull: false
    },
    captcha: {
      type: DataTypes.INTEGER(1),
      allowNull: false
    },
    body: {
      type: DataTypes.JSON,
      allowNull: false
    },
    exp: {
      type: DataTypes.INTEGER(10),
      allowNull: false,
      defaultValue: 0
    },
    res: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false
    },
    msg: {
      type: DataTypes.STRING(50),
      allowNull: false,
      defaultValue: ''
    },
    ip: {
      type: DataTypes.STRING(15),
      allowNull: false,
      defaultValue: ''
    },
    created: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: helper.time
    }
  }, {
    tableName: 'message_sms',
    timestamps: false
  });
};