const Sequelize = require("sequelize");

/**
 * 数据库连接
 */
module.exports = function (app) {

  const cnf = app.env.db;
  const db = new Sequelize(cnf.database, cnf.username, cnf.password, {
    host: cnf.host,
    port: cnf.port,
    dialect: cnf.type,
    timezone: '+08:00',
    logging: app.debug ? console.log : false,
    pool: {min: 1, max: 500, acquire: 10000, idle: 60000}
  });

  app.context.db = function(name){
    return name ? db.import(__dirname + '/model/' + name) : db;
  }

}
