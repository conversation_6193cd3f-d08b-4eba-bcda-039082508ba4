module.exports = function (app) {

  const _throw = function (code, message) {
    if (typeof code == 'string') {
      message = code;
      code = 400;
    }

    let e = new Error(message);
    e.code = code;
    e.expose = true;
    throw e;
  }

  app.context.throw = _throw;

  app.context.assert = function (bool, code, message) {
    bool || _throw(code, message);
  }

  return async function (ctx, next) {
    let code, message = '', data = null;

    try {
      await next();

      code = 200;
      let {output} = ctx;
      if (typeof output == 'string') {
        message = output;
      } else {
        data = output || null;
      }
    } catch (e) {
      app.debug && e.code != 404 && console.error(e);

      code = e.code || 500;
      message = e.message || e.toString();
    } finally {
      if (!ctx.body) {
        ctx.body = {code: code, data: data, message: message};
      }
    }

  }

}