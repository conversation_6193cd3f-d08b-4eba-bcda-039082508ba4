const parse = require('co-body');
const xml2js = require('xml2js');
const formidable = require('formidable');

const jsonTypes = ['application/json', 'application/json-patch+json', 'application/vnd.api+json', 'application/csp-report'];
const formTypes = ['application/x-www-form-urlencoded'];
const textTypes = ['text/plain'];
const xmlTypes = ['text/xml', 'application/xml'];

const jsonOpts = {limit: '2mb'};
const formOpts = {limit: '2mb'};
const textOpts = {limit: '2mb'};
const xmlOpts = {limit: '2mb'};

async function parseBody(ctx) {
  if (ctx.is(jsonTypes)) {
    return parse.json(ctx, jsonOpts);
  }

  if (ctx.is(formTypes)) {
    return parse.form(ctx, formOpts);
  }

  if (ctx.is(textTypes)) {
    return parse.text(ctx, textOpts);
  }

  if (ctx.is(xmlTypes)) {
    const txt = await parse.text(ctx, xmlOpts);
    return xml2js.parseStringPromise(txt, {limit: xmlOpts.limit, explicitArray: false});
  }

  if (ctx.is('multipart')) {
    const form = formidable({multiples: true, maxFileSize: 1024 * 1024 * 1024 * 2, uploadDir: ctx.env.temp});

    return new Promise((resolve, reject) => {
      form.parse(ctx.req, (err, fields, files) => {
        err ? reject(err) : resolve({fields, files});
      });
    });
  }

  return {};
}

module.exports = function () {

  return async function (ctx, next) {
    let res = await parseBody(ctx);

    if (ctx.is('multipart')) {
      ctx.request.body = res.fields;
      ctx.request.files = res.files;
    } else {
      ctx.request.body = res;
    }

    return next();
  }

}