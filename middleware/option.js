'use strict';

module.exports = function (app) {

  // 目前直接读取数据库，可以改成读取redis中的数据
  Object.defineProperties(app.context, {
    getOption: {
      async value(key, uid = 0) {
        if (key.length > 32) key = helper.md5(key);

        let sql = 'SELECT val FROM user_option WHERE `key`=? AND uid=? LIMIT 1';
        let rows = await this.db().query(sql, {type: 'SELECT', replacements: [key, uid]});
        return rows.length > 0 ? rows[0].val.v : null;
      }
    },
    setOption: {
      async value(key, val, uid = 0) {
        if (key.length > 32) key = helper.md5(key);

        let sql, params = [uid, key]
        if (val === null || val === undefined) {
          sql = 'DELETE FROM user_option WHERE uid=?, `key`=?';
        } else {
          sql = 'INSERT INTO user_option SET uid=?, `key`=?, val=? ON DUPLICATE KEY UPDATE val=VALUES(val)';

          val = JSON.stringify({v: val});
          params.push(val);
        }
        await this.db().query(sql, {type: 'UPDATE', replacements: params});
      }
    }
  });

}