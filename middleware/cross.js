'use strict';

/**
 * 跨域请求处理
 * 如无特殊处理建议在nginx中就做好此步
 */
module.exports = function () {

  return function (ctx, next) {
    ctx.set('Access-Control-Allow-Origin', ctx.get('origin') || ctx.host);
    ctx.set('Access-Control-Max-Age', 86400);
    ctx.set('Access-Control-Allow-Methods', 'OPTIONS, GET, POST, PUT, PATCH, DELETE');
    ctx.set('Access-Control-Allow-Headers', 'Content-Type, Content-Range, Content-Disposition, Content-Description, X-Requested-With, Authorization');
    ctx.set('Access-Control-Expose-Headers', 'Authorization');

    if (ctx.method == 'OPTIONS') {
      ctx.res.statusCode = 200;
      ctx.res.end();
      ctx.respond = false;
      ctx.throw(null);
    } else {
      return next();
    }
  }

}