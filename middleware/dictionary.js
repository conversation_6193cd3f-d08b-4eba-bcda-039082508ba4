module.exports = function (app) {

  app.context.getDict = async function (type, code, name = '') {
    const key = 'dict:' + type;
    const res = await this.redis.hget(key, code);
    return res || name;
  }

  app.context.setDict = async function (type, code, name) {
    const key = 'dict:' + type;

    if (name) {
      await this.redis.hset(key, code, name);
    } else {
      await this.redis.hdel(key, code);
    }

  }

  app.context.allDict = async function (type) {
    return this.redis.hget('dict:' + type);
  }
}