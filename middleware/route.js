const compose = require('koa-compose');

module.exports = function () {

  const groups = {};

  return async function (ctx, next) {
    const {module} = ctx.route;
    let key = module;

    const middleware = ctx.route.middleware.map(name => {
      key += '/' + name;
      return ctx.app.routerMiddleware[module + '/' + name];
    });

    if (middleware.length > 0) {
      if (!groups[key]) {
        groups[key] = compose(middleware);
      }

      return groups[key](ctx, next);
    }

    return next();
  }

}