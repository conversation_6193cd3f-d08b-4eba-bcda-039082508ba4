const AsyncValidator = require('async-validator').default;

module.exports = function (app) {

  const validators = {};

  for (let name in app.validators) {
    validators[name] = {};
    const object = require(app.validators[name]);

    for (let key in object) {
      validators[name + '.' + key] = new AsyncValidator(object[key]);
    }
  }

  app.context.validate = async function (name, body) {

    let validator = validators[name];
    this.assert(!!validator, '校验器' + name + '不存在');

    return validator.validate(body).then(() => {
      return body;
    }).catch(({errors, fields}) => {
      this.throw(errors[0].message);
    });
  }

}