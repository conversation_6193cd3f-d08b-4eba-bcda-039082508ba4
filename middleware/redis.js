const IRedis = require('ioredis');

const isEmpty = function (v) {
  return v === undefined || v === null || v === ''
}

class Redis {

  constructor(cnf) {
    let handler = new IRedis(cnf);

    ['del', 'incrby'].forEach(method => {
      this[method] = (...args) => handler[method](...args);
    });

    this.handler = handler;
  }

  async get(key, isJson) {
    let val = await this.handler.get(key);
    return !val ? null : isJson ? helper.json_decode(val) : val;
  }

  set(key, val, expire) {
    if (val === null || val === undefined) {
      return this.del(key);
    }

    let v = helper.json_encode(val);

    if (expire) {
      return this.handler.set(key, v, 'EX', expire);
    } else {
      return this.handler.set(key, v);
    }
  }

  async nextid() {
    let key = 'inctrade';
    let prefix = await this.incrby(key, helper.random(2, 10));
    if (prefix < 10000 || prefix > 99999) { // 重新初始化
      prefix = await this.incrby(key, helper.random(10000, 12345));
    }
    return helper.date('YYYYMMDD').substr(2) + prefix + Math.random().toString().substr(-2);
  }

  async hget(map, field) {
    return isEmpty(field) ? this.handler.hgetall(map) : this.handler.hget(map, field);
  }

  async hset(map, field, value) {
    if (isEmpty(field)) return this.del(map);
    if (isEmpty(value)) return this.hdel(map, field);

    return this.handler.hset(map, field, value);
  }

  async hdel(map, field) {
    if (!field) return this.del(map);
    return this.handler.hdel(map, field);
  }

  async list(key, val) {
    if (val === undefined) {
      let res = await this.handler.smembers(key);
      return res || [];
    }

    await this.handler.del(key);

    if (Array.isArray(val) && val.length > 0) {
      await this.handler.sadd(...[key].concat(val));
    }
  }
}

module.exports = function (app) {

  app.context.redis = new Redis(app.env.redis);

};
