module.exports = function (app) {

  return async function (ctx, next) {
    const {module, controller, action, values, params} = ctx.route;
    const filename = app.controllers[(module ? module + '.' : '') + controller];
    const Controller = require(filename);
    const instance = new Controller(ctx);
    const method = typeof instance[action] == 'function' ? action : '__call';

    ctx.output = await instance[method].apply(instance, values);

    return next();
  }

}