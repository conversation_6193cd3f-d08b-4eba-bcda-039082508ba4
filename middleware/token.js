const jwt = require('jsonwebtoken');

class Token {

  constructor(key, exp) {
    this.key = key;
    this.exp = exp;
  }

  get time() {
    return parseInt(Date.now() / 1000);
  }

  decodeHead(data) {
    return Object.defineProperties({}, {
      client: {
        value: data.c
      },
      version: {
        value: data.v
      },
      platform: {
        value: data.p
      },
      iat: {
        writable: true,
        value: parseInt(data.i) || 0
      },
      exp: {
        writable: true,
        value: parseInt(data.e) || 0
      }
    })
  }

  encodeHead(body) {
    let iat = this.time;

    return {
      i: iat,
      e: Math.max(body.exp, this.exp ? iat + this.exp : 0),
      c: body.client,
      v: body.version,
      p: body.platform
    }
  }

  encode(body) {
    return jwt.sign(body, this.key, {
      noTimestamp: true,
      header: this.encodeHead(body)
    });
  }

  decode(id) {
    if (!id) return;

    // 客户端 - 版本号 - 运行环境
    let iv = id.match(/^(www|admin|console|app)-(\d{1,4}\.\d{1,4}\.\d{1,4})-(weixin|wxpro|android|ios|browser|alipay)$/);
    if (iv) {
      return this.decodeHead({c: iv[1], v: iv[2], p: iv[3]});
    }

    return jwt.verify(id, this.key, {complete: true}, (err, decoded) => {
      if (err) return;

      let {header, payload} = decoded;
      let body = this.decodeHead(header);

      return !body.exp || body.exp > this.time ? Object.assign(body, payload) : body;
    });
  }

}

module.exports = function () {

  return async function (ctx, next) {
    let key = ctx.env.keys;
    let cnf = ctx.env.session;

    let id = ctx.get(cnf.name);
    let token = new Token(key, cnf.exp);
    let err, body = token.decode(id) || {};

    ctx.token = body;

    try {
      await next();
    } catch (e) {
      err = e;
    }

    ctx.set(cnf.name, token.encode(body));
    if (err) throw err;
  }

};