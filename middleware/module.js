const compose = require('koa-compose');

module.exports = function (app) {

  const middlewares = {};

  for (let module in app.modules) {
    const list = [];
    const env = app.modules[module];

    env.middleware.forEach(name => {
      const fn = require(app.middlewarePath + '/' + name)(app, env);
      list.push(fn);
    });

    if (list.length > 0) {
      middlewares[module] = compose(list);
    }
  }

  return async function (ctx, next) {
    const fn = middlewares[ctx.route.module];

    if (fn) {
      await fn(ctx);
    }

    return next();
  }

}