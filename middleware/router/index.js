const Router = require('./lib/router');

module.exports = function (app) {

  const router = new Router();

  for (let module in app.modules) {
    const env = app.modules[module];

    // 导入路由规则
    router.import(module, env.root + '/router.js');

    // 加载路由中间件
    const names = router.middlewares[module];
    names && (Array.isArray(names) ? names : [names]).forEach(name => {
      app.routerMiddleware[module + '/' + name] = require(app.middlewarePath + '/' + name)(app, env);
    });
  }

  return async function (ctx, next) {
    const route = router.match(ctx.hostname, ctx.path, ctx.method);

    ctx.assert(route, 404, 'NOT FOUND');
    ctx.route = route;
    ctx.env = app.modules[route.module];

    return next();
  };

}