"use strict";

const fs = require('fs');
const pathToRegexp = require('path-to-regexp');
const allowMethods = function () {
  return {GET: [], POST: [], PUT: [], DELETE: [], PATCH: [], HEAD: [], OPTIONS: []}
}

module.exports = class Router {

  constructor(options) {
    options = options || {};

    this.domain = options.domain || '*';
    this.base = options.base || '/';
    this.equRoutes = {}; // 完全匹配规则
    this.regRoutes = {}; // 正则匹配规则
    this.defRoutes = allowMethods(); // 最后匹配规则
    this.middlewares = {};
  }

  import(module, filename) {
    const options = fs.existsSync(filename) && require(filename);

    if (!options) return;

    this.middlewares[module] = [];

    let parent = {
      module: module,
      domain: options.domain || this.domain,
      base: options.base || this.base,
      middleware: options.middleware || []
    }

    options.routes.forEach(params => {
      this.push(params, parent);
    });
  }

  push(params, parent) {
    let domain = params.domain || parent.domain;
    let middleware = [].concat(parent.middleware);

    if (params.middleware) {
      (Array.isArray(params.middleware) ? params.middleware : [params.middleware]).forEach(name => {
        if (middleware.indexOf(name) == -1) {
          middleware.push(name);
        }

        if (this.middlewares[parent.module].indexOf(name) == -1) {
          this.middlewares[parent.module].push(name);
        }
      });
    }

    let path;

    if (params.path) {
      if (params.path[0] == '/') {
        path = (parent.base == '/' ? '' : parent.base) + params.path;
      } else if (parent.path) {
        path = parent.path + (parent.path == '/' ? '' : '/') + params.path;
      } else {
        path = parent.base + (parent.base == '/' ? '' : '/') + params.path;
      }
    } else if (params.name) {
      if (parent.path) {
        path = parent.path + (parent.path == '/' ? '' : '/') + params.name;
      } else {
        path = parent.base + (parent.base == '/' ? '' : '/') + params.name;
      }
    } else {
      path = parent.path;
    }

    if (Array.isArray(params.children) && params.children.length > 0) {
      Object.assign(parent, {domain, path, middleware});

      return params.children.forEach(params => this.push(params, parent));
    }

    // 控制器
    let handle = params.handle || params.name;
    let index = handle.lastIndexOf('.');

    let keys = [];
    let route = {
      name: params.name,
      module: parent.module,
      controller: handle.substr(0, index),
      action: handle.substr(index + 1),
      keys: keys,
      regexp: pathToRegexp(path, keys, {strict: true}),
      access: params.access === undefined ? 1 : params.access,
      middleware: middleware
    };

    let routes, regexp;
    (Array.isArray(domain) ? domain : [domain]).forEach(name => {
      if (name === '*') {
        routes = this.defRoutes;
      } else {
        keys = [];
        regexp = pathToRegexp(path, keys, {strict: true});

        if (keys.length > 0) {
          routes = this.regRoutes[name];
          if (!routes) {
            routes = this.regRoutes[name] = allowMethods();
            routes.regexp = regexp;
            routes.keys = keys;
          }
        } else {
          routes = this.equRoutes[name];
          if (!routes) {
            routes = this.equRoutes[name] = allowMethods();
          }
        }
      }

      routes[params.method].push(route);
    });
  }

  match(domain, path, method) {
    if (path == '/favicon.ico') return;

    let routes;

    if (this.equRoutes[domain]) {// 完全匹配规则
      routes = this.equRoutes;
    } else if (this.regRoutes[domain]) { // 正则匹配规则
      let domains = this.regRoutes[domain];

      for (let name in domains) {
        if (domains[name].regexp.test(domain)) {
          routes = domains[name];
          break;
        }
      }
    } else { // 最后匹配规则
      routes = this.defRoutes;
    }

    if (!routes) return;

    routes = routes[method];

    let route, matched;

    path = path.split('?')[0];

    for (let i = 0; i < routes.length; i++) {
      route = routes[i];

      matched = route.regexp.exec(path);
      if (!matched) continue;

      const params = {};
      const values = route.keys.map(function (info, i) {
        params[info.name] = matched[i + 1];
        return matched[i + 1];
      });

      route = Object.assign({}, route, {params, values});

      delete route.keys;
      delete route.regexp;

      return route;
    }
  }
}