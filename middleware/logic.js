module.exports = function () {

  return function (ctx, next) {
    const cache = {};

    ctx.logic = function (name, instance = '') {
      const key = name + '.' + instance;

      if (!cache[key]) {
        if (!ctx.app.logics[name]) {
          ctx.throw('逻辑模块不存在：' + name);
        }

        const Logic = require(ctx.app.logics[name]);

        if (typeof Logic.getInstance == 'function') {
          cache[key] = Logic.getInstance(ctx, instance);
        } else {
          cache[key] = new Logic(ctx);
        }
      }

      return cache[key];
    }

    return next();
  }

}
