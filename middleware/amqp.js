module.exports = async function (app) {

  let conn = await require('amqplib').connect(app.env.amqp);
  let ch = await conn.createChannel();

  // 频道
  await ch.assertQueue('LiveReply', {autoDelete: false, durable: true});

  Object.defineProperty(app.context, 'amqp', {
    value: {
      push(channel, data) {
        return ch.sendToQueue(channel, Buffer.from(JSON.stringify(data)));
      }
    }
  });

};