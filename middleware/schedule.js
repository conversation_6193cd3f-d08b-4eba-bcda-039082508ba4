
const schedule = require('node-schedule');

function Schedule(app, opts){

  let scope = this;
  let paths = opts.logic.split('.');
  let method = paths.pop();
  // let path = paths.shift() + '/logic/' + paths.join('.');
  let path = '/logic/' + paths.join('.');
  let Logic = require('../app/' + path);
  let j;


  this.start = function(){
    scope.cancel();
    j = schedule.scheduleJob(opts.timer, scope.execute);
  };

  this.cancel = function(){
    if(j){
      j.cancel();
      j = null;
    }
  }

  this.execute = function(){
    let ctx = Object.create(app.context);
    ctx.app = app;

    let logic = new Logic(ctx);
    logic[method]().then(function(){

    }).catch(function(){

    }).finally(function(){
      logic = null;
      ctx = null;
    });
  }

  this.start();
}

/**
 * 定时器
 */
module.exports = function(app){

  let list = app.env.schedule;
  if(!list || list.length == 0){
    return;
  }

  list.forEach(function(item){

    item.worker = new Schedule(app, item);

  });

}
