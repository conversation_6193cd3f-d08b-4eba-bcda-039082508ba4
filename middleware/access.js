module.exports = function (app, env) {

  return async function (ctx, next) {
    let {access} = ctx.route;

    switch (access) {
      case 1: // 公共权限
        return next();
      case 2: // 需要登录
        if (!ctx.token.user) ctx.throw('请登录', 401);
        break;
      case 0: // 禁止访问
        ctx.throw('无权限', 403);
        break;
      default: // 根据权限配置
        let {user} = ctx.token;
        if (!user) ctx.throw('请登录', 401);

        let logic = ctx.logic('sys.access');
        let has = await logic.hasAccess(user.id, access);
        if (!has) ctx.throw('无权限', 403);
    }

    return next();
  }
}