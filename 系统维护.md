安装mysql8
wget https://repo.mysql.com/mysql80-community-release-el7-3.noarch.rpm
yum localinstall mysql80-community-release-el7-3.noarch.rpm
yum install mysql-community-server

启动
service mysqld start
获取默认密码
cat /var/log/mysqld.log | grep password
如：qZ:#c3to8kwj

登录修改root密码
mysql -hlocalhost -uroot -p
ALTER USER "root"@"localhost" IDENTIFIED BY "14A218_317da@b74D8295%9dfa3b732c";

修改配置文件
vi /etc/my.cnf
加入如下代码
port=3306

ft_min_word_len = 1
innodb_ft_min_token_size=1
max_sp_recursion_depth=5

sql_mode="STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION"
max_connections=500


重启服务
service mysqld restart

创建用户
CREATE USER 'dev'@'%' IDENTIFIED by 'v7iaK0QC@bjoN3ZO%xqUWeGz';
删除用户
DROP USER 'dev'@'%';

创建数据库
CREATE DATABASE `www_xd56b_com` CHARACTER SET 'utf8mb4' COLLATE 'utf8mb4_general_ci';

快速授权
grant all privileges on `www_xd56b_com`.* to 'dev'@'%';

创建nodejs运行用户
create user `node`@`%` IDENTIFIED WITH mysql_native_password BY 'A9ed2F5e15ec1c6b4%0913fb';
grant select, insert, update, delete on `www_xd56b_com`.* to 'node'@'localhost';

立即生效
flush privileges;

然后就可以外网连接数据库了，一定要记得去阿里云设置入网规则允许数据库端口(3306)
私有服务器如果外网连接不上可以开放端口
firewall-cmd --zone=public --add-port=4372/tcp --permanent
如果还连接不上就关闭防火墙
firewall-cmd --state
systemctl stop firewalld.service


安装Nodejs 
wget https://npm.taobao.org/mirrors/node/v12.13.0/node-v12.13.0-linux-x64.tar.gz
tar -xvf node-v12.13.0-linux-x64.tar.gz
mv node-v12.13.0-linux-x64.tar.gz node
cd node/bin
pwd
./node -v
创建链接
ln -s /var/www/node/bin/node /usr/bin/node
ln -s /var/www/node/bin/npm /usr/bin/npm

可以安装淘宝镜像，避免安装东西过慢，以后npm就改成cnpm
npm install -g cnpm --registry=https://registry.npm.taobao.org
ln -s /var/www/node/bin/cnpm /usr/bin/cnpm
cnpm -v

安装pm2守护进程
cnpm i pm2 -g
ln -s /var/www/node/bin/pm2 /usr/bin/pm2
pm2 -v
pm2 list
pm2 start all
pm2 stop all
pm2 start app.js -name api.xxx.com


安装Nginx
yum -y install yum-utils
vi /etc/yum.repos.d/nginx.repo
输入如下代码保存
[nginx-stable]
name=nginx stable repo
baseurl=http://nginx.org/packages/centos/$releasever/$basearch/
gpgcheck=1
enabled=1
gpgkey=https://nginx.org/keys/nginx_signing.key

[nginx-mainline]
name=nginx mainline repo
baseurl=http://nginx.org/packages/mainline/centos/$releasever/$basearch/
gpgcheck=1
enabled=0
gpgkey=https://nginx.org/keys/nginx_signing.key

启用安装包
yum-config-manager --enable nginx-mainline

安装nginx一路y
yum install nginx

cd /etc/nginx/conf.d



查看当前已登录用户
w
注销linux用户登录
pkill -kill -t pts/0

清理安装包
yum clean packages
yum clean headers
yum clean oldheaders
yum clean all

【启动redis】
cd /www/server/redis-6.0.10/src
./redis-server ../redis.conf

【以下是程序待发布】

数据库变更
