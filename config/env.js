const os = require('os');
const path = require('path');
const root = path.dirname(__dirname);

module.exports = {
  cpu: 1, // os.cpus().length,
  port: ${api_port},
  temp: os.type() == 'Windows_NT' ? path.join(root, './temp') : os.tmpdir(),
  static: path.join(root, '../static'),
  keys: '3da0b6bc65c53269f409701c9541a421',
  plugin: ['db', 'redis', 'option', 'extend', 'validator', 'dictionary'],
  middleware: ['cross', 'response', 'router', 'logic', 'model', 'module', 'token', 'body', 'route', 'controller','schedule'],
  // middleware: ['cross', 'response', 'router', 'logic', 'model', 'module', 'token', 'body', 'route', 'controller'],
  session: {
    type: 'token',
    exp: 7776000,
    name: 'Authorization'
  },
  db: {
    type: 'mysql',
    host: '${db_host}',
    port: ${db_port},
    database: '${database}',
    username: '${username}',
    password: '${password}',
    charset: 'utf8mb4'
  },
  redis: {
    host: '127.0.0.1',
    port: ${redis_port},
    password: '${redis_pass}',
    db: ${redis_db},
  },
  upload: {
    type: 1,
    https: 1,
    domain: 'cdn.zn.nextv.show',
    // domain: 'ry4i3ucs9.bkt.clouddn.com',
    zone: 'cn-east-2',
    bucket: 'zn-nextvshow',
    // bucket: 'zn-private-test',
    accessKey: '${accessKey}',
    secretKey: '${secretKey}'
  },
  schedule:[{'logic':'media.item.handleTopExpired','timer':'0 0 2 * * ?'}]
}

